{"name": "farm-management-backend-mysql", "version": "1.0.0", "description": "农场智慧管理系统后端 - MySQL版本", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "migrate": "node src/scripts/migrate.js", "seed": "node src/scripts/seed.js", "db:create": "node src/scripts/createDatabase.js", "db:reset": "node src/scripts/resetDatabase.js", "db:backup": "node src/scripts/backup.js", "db:restore": "node src/scripts/restore.js"}, "keywords": ["farm", "agriculture", "iot", "management", "mysql", "sequelize", "nodejs"], "author": "Farm Management Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "sequelize": "^6.35.0", "mysql2": "^3.6.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "morgan": "^1.10.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "node-cron": "^3.0.3", "socket.io": "^4.7.4", "redis": "^4.6.10", "nodemailer": "^6.9.7", "moment": "^2.29.4", "lodash": "^4.17.21", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "prettier": "^3.1.0", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/farm-management/backend-mysql.git"}, "bugs": {"url": "https://github.com/farm-management/backend-mysql/issues"}, "homepage": "https://github.com/farm-management/backend-mysql#readme", "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/app.js", "!src/config/**", "!src/migrations/**", "!src/scripts/**"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm test"}}}