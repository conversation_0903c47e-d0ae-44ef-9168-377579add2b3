# 农场智慧管理系统后端 - MySQL版本

基于 Node.js + Express + MySQL + Sequelize 的农场智慧管理系统后端服务。

## 🌟 特性

- **现代化技术栈**: Node.js + Express + MySQL + Sequelize ORM
- **完整的用户系统**: 认证、授权、角色管理
- **农场管理**: 地块、作物、设备全生命周期管理
- **IoT设备支持**: 传感器数据采集、设备监控
- **智能预警**: 基于规则的预警系统
- **任务管理**: 农事任务分配和跟踪
- **数据可视化**: 丰富的统计和分析接口
- **安全可靠**: JWT认证、数据验证、SQL注入防护
- **高性能**: 数据库索引优化、查询缓存
- **易于部署**: Docker支持、环境配置

## 📋 系统要求

- Node.js >= 16.0.0
- MySQL >= 8.0
- Redis >= 6.0 (可选，用于缓存)
- npm >= 8.0.0

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd backend-mysql
```

### 2. 安装依赖

```bash
npm install
```

### 3. 环境配置

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

### 4. 数据库设置

```bash
# 创建数据库
npm run db:create

# 执行迁移
npm run migrate

# 填充测试数据（可选）
npm run seed
```

### 5. 启动服务

```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

## 📁 项目结构

```
backend-mysql/
├── src/
│   ├── config/          # 配置文件
│   │   ├── database.js  # 数据库配置
│   │   └── redis.js     # Redis配置
│   ├── controllers/     # 控制器
│   │   ├── authController.js
│   │   ├── userController.js
│   │   ├── fieldController.js
│   │   ├── cropController.js
│   │   ├── deviceController.js
│   │   ├── alertController.js
│   │   └── taskController.js
│   ├── middleware/      # 中间件
│   │   ├── auth.js      # 认证中间件
│   │   ├── validation.js # 数据验证
│   │   └── errorHandler.js
│   ├── models/          # 数据模型
│   │   ├── User.js
│   │   ├── Field.js
│   │   ├── Crop.js
│   │   ├── Device.js
│   │   ├── SensorData.js
│   │   ├── Alert.js
│   │   ├── Task.js
│   │   └── associations.js
│   ├── routes/          # 路由定义
│   │   ├── auth.js
│   │   ├── users.js
│   │   ├── fields.js
│   │   ├── crops.js
│   │   ├── devices.js
│   │   ├── alerts.js
│   │   └── tasks.js
│   ├── services/        # 业务服务
│   │   ├── authService.js
│   │   ├── emailService.js
│   │   └── notificationService.js
│   ├── utils/           # 工具函数
│   │   ├── logger.js
│   │   ├── response.js
│   │   └── helpers.js
│   ├── migrations/      # 数据库迁移
│   │   └── 001-create-tables.sql
│   ├── scripts/         # 脚本文件
│   │   ├── migrate.js
│   │   ├── seed.js
│   │   └── backup.js
│   └── app.js          # 应用入口
├── tests/              # 测试文件
├── uploads/            # 上传文件目录
├── logs/              # 日志文件目录
├── backups/           # 备份文件目录
├── .env.example       # 环境配置示例
├── package.json
└── README.md
```

## 🔧 配置说明

### 数据库配置

```env
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=farm_management
```

### JWT配置

```env
JWT_SECRET=your_super_secret_jwt_key
JWT_EXPIRES_IN=7d
```

### 邮件配置

```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
```

## 📊 数据库模型

### 核心实体

- **User**: 用户管理
- **Field**: 地块管理
- **Crop**: 作物管理
- **Device**: 设备管理
- **SensorData**: 传感器数据
- **Alert**: 预警管理
- **Task**: 任务管理

### 关系设计

```
User (1:N) Field (1:N) Crop
User (1:N) Device (1:N) SensorData
User (1:N) Alert
User (1:N) Task
Field (1:N) Device
Field (1:N) Alert
Field (1:N) Task
```

## 🔌 API接口

### 认证接口

```
POST /api/auth/login     # 用户登录
POST /api/auth/register  # 用户注册
POST /api/auth/refresh   # 刷新令牌
POST /api/auth/logout    # 用户登出
```

### 用户管理

```
GET    /api/users        # 获取用户列表
GET    /api/users/:id    # 获取用户详情
POST   /api/users        # 创建用户
PUT    /api/users/:id    # 更新用户
DELETE /api/users/:id    # 删除用户
```

### 地块管理

```
GET    /api/fields       # 获取地块列表
GET    /api/fields/:id   # 获取地块详情
POST   /api/fields       # 创建地块
PUT    /api/fields/:id   # 更新地块
DELETE /api/fields/:id   # 删除地块
```

### 设备管理

```
GET    /api/devices      # 获取设备列表
GET    /api/devices/:id  # 获取设备详情
POST   /api/devices      # 添加设备
PUT    /api/devices/:id  # 更新设备
DELETE /api/devices/:id  # 删除设备
```

### 传感器数据

```
GET    /api/sensors/data        # 获取传感器数据
GET    /api/sensors/:id/data    # 获取指定设备数据
POST   /api/sensors/data        # 上传传感器数据
GET    /api/sensors/:id/stats   # 获取数据统计
```

### 预警管理

```
GET    /api/alerts       # 获取预警列表
GET    /api/alerts/:id   # 获取预警详情
POST   /api/alerts       # 创建预警
PUT    /api/alerts/:id   # 更新预警
DELETE /api/alerts/:id   # 删除预警
```

## 🛠️ 开发工具

### 数据库操作

```bash
# 创建数据库
npm run db:create

# 执行迁移
npm run migrate

# 检查迁移状态
npm run migrate status

# 回滚迁移
npm run migrate rollback

# 重置数据库
npm run db:reset

# 数据库备份
npm run db:backup

# 数据库恢复
npm run db:restore
```

### 代码质量

```bash
# 代码检查
npm run lint

# 自动修复
npm run lint:fix

# 运行测试
npm test

# 测试覆盖率
npm run test:coverage
```

## 🐳 Docker部署

### 使用Docker Compose

```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MYSQL_HOST=mysql
    depends_on:
      - mysql
      - redis
  
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: farm_management
    volumes:
      - mysql_data:/var/lib/mysql
  
  redis:
    image: redis:7-alpine
    
volumes:
  mysql_data:
```

### 启动服务

```bash
docker-compose up -d
```

## 📈 性能优化

### 数据库优化

- **索引优化**: 为常用查询字段添加索引
- **查询优化**: 使用Sequelize的include和attributes优化查询
- **连接池**: 配置合适的数据库连接池大小
- **分页查询**: 大数据量查询使用分页

### 缓存策略

- **Redis缓存**: 缓存频繁查询的数据
- **查询缓存**: 缓存复杂查询结果
- **会话缓存**: 使用Redis存储用户会话

### 安全措施

- **输入验证**: 使用express-validator验证输入
- **SQL注入防护**: Sequelize ORM自动防护
- **XSS防护**: 使用helmet中间件
- **限流保护**: 使用express-rate-limit

## 🔍 监控和日志

### 日志系统

- **Winston日志**: 结构化日志记录
- **日志轮转**: 按日期轮转日志文件
- **错误追踪**: 详细的错误堆栈信息

### 健康检查

```bash
# 检查服务状态
curl http://localhost:3000/health
```

### 性能监控

- **响应时间**: 监控API响应时间
- **内存使用**: 监控内存使用情况
- **数据库性能**: 监控数据库查询性能

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请提交 [Issue](https://github.com/farm-management/backend-mysql/issues)。

---

**农场智慧管理系统 MySQL 版本** - 让农业管理更智能、更高效！ 🌾✨
