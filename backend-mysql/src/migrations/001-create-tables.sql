-- 农场智慧管理系统 MySQL 数据库表结构
-- 创建时间: 2024-01-01
-- 版本: 1.0.0

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE,
  `email` varchar(100) NOT NULL UNIQUE,
  `password` varchar(255) NOT NULL,
  `name` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `role` enum('admin','manager','worker','viewer') NOT NULL DEFAULT 'worker',
  `department` varchar(100) DEFAULT NULL,
  `status` enum('active','inactive','deleted') NOT NULL DEFAULT 'active',
  `last_login_at` datetime DEFAULT NULL,
  `login_count` int(11) DEFAULT 0,
  `password_changed_at` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_username` (`username`),
  KEY `idx_email` (`email`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`),
  KEY `idx_department` (`department`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 地块表
CREATE TABLE IF NOT EXISTS `fields` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `code` varchar(50) NOT NULL UNIQUE,
  `location` varchar(255) NOT NULL,
  `area` decimal(10,2) NOT NULL,
  `used_area` decimal(10,2) NOT NULL DEFAULT 0.00,
  `soil_type` varchar(50) DEFAULT NULL,
  `ph_level` decimal(3,1) DEFAULT NULL,
  `fertility_level` enum('low','medium','high') DEFAULT NULL,
  `irrigation_type` enum('drip','sprinkler','flood','manual') DEFAULT NULL,
  `coordinates` text COMMENT 'GeoJSON格式的地理坐标',
  `elevation` decimal(8,2) DEFAULT NULL COMMENT '海拔高度(米)',
  `slope` decimal(5,2) DEFAULT NULL COMMENT '坡度(度)',
  `drainage` enum('excellent','good','fair','poor') DEFAULT NULL,
  `status` enum('active','inactive','maintenance') NOT NULL DEFAULT 'active',
  `description` text,
  `images` json COMMENT '地块图片URL数组',
  `created_by` int(11) NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_status` (`status`),
  KEY `idx_location` (`location`),
  KEY `idx_soil_type` (`soil_type`),
  KEY `idx_area` (`area`),
  KEY `fk_fields_created_by` (`created_by`),
  KEY `fk_fields_updated_by` (`updated_by`),
  CONSTRAINT `fk_fields_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_fields_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='地块表';

-- 作物表
CREATE TABLE IF NOT EXISTS `crops` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `variety` varchar(100) NOT NULL,
  `field_id` int(11) NOT NULL,
  `area` decimal(10,2) NOT NULL,
  `status` enum('planned','planted','growing','harvested','failed') NOT NULL DEFAULT 'planned',
  `planting_date` date DEFAULT NULL,
  `expected_harvest_date` date DEFAULT NULL,
  `actual_harvest_date` date DEFAULT NULL,
  `expected_yield` decimal(10,2) DEFAULT NULL COMMENT '预期产量（公斤）',
  `actual_yield` decimal(10,2) DEFAULT NULL COMMENT '实际产量（公斤）',
  `growth_stages` json COMMENT '生长阶段定义',
  `growth_records` json COMMENT '生长记录',
  `care_instructions` json COMMENT '护理说明',
  `environmental_requirements` json COMMENT '环境要求',
  `notes` text,
  `images` json COMMENT '作物图片',
  `created_by` int(11) NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_variety` (`variety`),
  KEY `idx_field_id` (`field_id`),
  KEY `idx_status` (`status`),
  KEY `idx_planting_date` (`planting_date`),
  KEY `idx_expected_harvest_date` (`expected_harvest_date`),
  KEY `idx_actual_harvest_date` (`actual_harvest_date`),
  KEY `fk_crops_created_by` (`created_by`),
  KEY `fk_crops_updated_by` (`updated_by`),
  CONSTRAINT `fk_crops_field_id` FOREIGN KEY (`field_id`) REFERENCES `fields` (`id`),
  CONSTRAINT `fk_crops_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_crops_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='作物表';

-- 设备表
CREATE TABLE IF NOT EXISTS `devices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `device_id` varchar(100) NOT NULL UNIQUE,
  `name` varchar(100) NOT NULL,
  `type` enum('sensor','camera','controller','irrigation','weather') NOT NULL,
  `model` varchar(100) DEFAULT NULL,
  `manufacturer` varchar(100) DEFAULT NULL,
  `version` varchar(50) DEFAULT NULL,
  `status` enum('active','inactive','maintenance','error','deleted') NOT NULL DEFAULT 'active',
  `is_online` tinyint(1) NOT NULL DEFAULT 0,
  `field_id` int(11) DEFAULT NULL,
  `location` json COMMENT '设备位置坐标 {lat, lng, altitude}',
  `position` varchar(200) DEFAULT NULL COMMENT '设备位置描述',
  `configuration` json COMMENT '设备配置参数',
  `specifications` json COMMENT '设备技术规格',
  `last_seen_at` datetime DEFAULT NULL,
  `last_online_at` datetime DEFAULT NULL,
  `last_data_time` datetime DEFAULT NULL,
  `installation_date` date DEFAULT NULL,
  `warranty_expiry` date DEFAULT NULL,
  `last_maintenance_at` date DEFAULT NULL,
  `maintenance_interval` int(11) DEFAULT 30 COMMENT '维护间隔天数',
  `maintenance_notes` text,
  `description` text,
  `created_by` int(11) NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_id` (`device_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_is_online` (`is_online`),
  KEY `idx_field_id` (`field_id`),
  KEY `idx_last_seen_at` (`last_seen_at`),
  KEY `idx_last_data_time` (`last_data_time`),
  KEY `fk_devices_created_by` (`created_by`),
  KEY `fk_devices_updated_by` (`updated_by`),
  CONSTRAINT `fk_devices_field_id` FOREIGN KEY (`field_id`) REFERENCES `fields` (`id`),
  CONSTRAINT `fk_devices_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_devices_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备表';

-- 传感器数据表
CREATE TABLE IF NOT EXISTS `sensor_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `device_id` int(11) NOT NULL,
  `timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `data` json NOT NULL COMMENT '传感器数据，包含各种参数值',
  `data_quality` int(11) DEFAULT NULL COMMENT '数据质量评分 0-100',
  `signal_strength` int(11) DEFAULT NULL COMMENT '信号强度 dBm',
  `battery_level` int(11) DEFAULT NULL COMMENT '电池电量百分比',
  `is_valid` tinyint(1) NOT NULL DEFAULT 1 COMMENT '数据是否有效',
  `validation_errors` json COMMENT '数据验证错误信息',
  PRIMARY KEY (`id`),
  KEY `idx_device_timestamp` (`device_id`,`timestamp`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_is_valid` (`is_valid`),
  KEY `idx_data_quality` (`data_quality`),
  CONSTRAINT `fk_sensor_data_device_id` FOREIGN KEY (`device_id`) REFERENCES `devices` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='传感器数据表';

-- 预警表
CREATE TABLE IF NOT EXISTS `alerts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `message` text NOT NULL,
  `type` enum('device','sensor','weather','system','security','maintenance') NOT NULL,
  `level` enum('critical','warning','info') NOT NULL DEFAULT 'info',
  `status` enum('active','resolved','dismissed') NOT NULL DEFAULT 'active',
  `source` varchar(100) DEFAULT NULL,
  `device_id` int(11) DEFAULT NULL,
  `field_id` int(11) DEFAULT NULL,
  `data` json COMMENT '预警相关数据',
  `threshold_parameter` varchar(100) DEFAULT NULL,
  `threshold_value` decimal(10,2) DEFAULT NULL,
  `threshold_operator` enum('>','<','>=','<=','==','!=') DEFAULT '>',
  `threshold_unit` varchar(20) DEFAULT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `read_at` datetime DEFAULT NULL,
  `read_by` int(11) DEFAULT NULL,
  `resolution` text,
  `resolved_at` datetime DEFAULT NULL,
  `resolved_by` int(11) DEFAULT NULL,
  `notification_sent` tinyint(1) NOT NULL DEFAULT 0,
  `notification_sent_at` datetime DEFAULT NULL,
  `fingerprint` varchar(255) DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_type_level` (`type`,`level`),
  KEY `idx_status_created_at` (`status`,`created_at`),
  KEY `idx_device_created_at` (`device_id`,`created_at`),
  KEY `idx_field_created_at` (`field_id`,`created_at`),
  KEY `idx_is_read_created_at` (`is_read`,`created_at`),
  KEY `idx_fingerprint_created_at` (`fingerprint`,`created_at`),
  KEY `idx_level` (`level`),
  KEY `fk_alerts_device_id` (`device_id`),
  KEY `fk_alerts_field_id` (`field_id`),
  KEY `fk_alerts_created_by` (`created_by`),
  KEY `fk_alerts_read_by` (`read_by`),
  KEY `fk_alerts_resolved_by` (`resolved_by`),
  CONSTRAINT `fk_alerts_device_id` FOREIGN KEY (`device_id`) REFERENCES `devices` (`id`),
  CONSTRAINT `fk_alerts_field_id` FOREIGN KEY (`field_id`) REFERENCES `fields` (`id`),
  CONSTRAINT `fk_alerts_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_alerts_read_by` FOREIGN KEY (`read_by`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_alerts_resolved_by` FOREIGN KEY (`resolved_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='预警表';

-- 任务表
CREATE TABLE IF NOT EXISTS `tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `description` text,
  `type` enum('planting','irrigation','fertilization','harvesting','maintenance','inspection','other') NOT NULL,
  `priority` enum('low','medium','high','urgent') NOT NULL DEFAULT 'medium',
  `status` enum('pending','in_progress','completed','cancelled') NOT NULL DEFAULT 'pending',
  `field_id` int(11) DEFAULT NULL,
  `crop_id` int(11) DEFAULT NULL,
  `assigned_to` int(11) DEFAULT NULL,
  `start_date` datetime DEFAULT NULL,
  `due_date` datetime NOT NULL,
  `estimated_duration` int(11) DEFAULT NULL COMMENT '预计耗时（小时）',
  `actual_duration` int(11) DEFAULT NULL COMMENT '实际耗时（小时）',
  `started_at` datetime DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL,
  `cancelled_at` datetime DEFAULT NULL,
  `completion_notes` text,
  `cancellation_reason` text,
  `progress_records` json COMMENT '进度记录',
  `requirements` json COMMENT '任务要求和规格',
  `resources_needed` json COMMENT '所需资源',
  `attachments` json COMMENT '附件文件',
  `created_by` int(11) NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_type` (`type`),
  KEY `idx_assigned_to` (`assigned_to`),
  KEY `idx_field_id` (`field_id`),
  KEY `idx_crop_id` (`crop_id`),
  KEY `idx_due_date` (`due_date`),
  KEY `idx_start_date` (`start_date`),
  KEY `idx_completed_at` (`completed_at`),
  KEY `idx_status_due_date` (`status`,`due_date`),
  KEY `idx_assigned_status` (`assigned_to`,`status`),
  KEY `fk_tasks_field_id` (`field_id`),
  KEY `fk_tasks_crop_id` (`crop_id`),
  KEY `fk_tasks_assigned_to` (`assigned_to`),
  KEY `fk_tasks_created_by` (`created_by`),
  KEY `fk_tasks_updated_by` (`updated_by`),
  CONSTRAINT `fk_tasks_field_id` FOREIGN KEY (`field_id`) REFERENCES `fields` (`id`),
  CONSTRAINT `fk_tasks_crop_id` FOREIGN KEY (`crop_id`) REFERENCES `crops` (`id`),
  CONSTRAINT `fk_tasks_assigned_to` FOREIGN KEY (`assigned_to`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_tasks_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_tasks_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务表';

-- 插入默认管理员用户
INSERT INTO `users` (`username`, `email`, `password`, `name`, `role`, `status`, `created_by`) 
VALUES ('admin', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq9w5KS', '系统管理员', 'admin', 'active', 1)
ON DUPLICATE KEY UPDATE `updated_at` = CURRENT_TIMESTAMP;

SET FOREIGN_KEY_CHECKS = 1;
