#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config();

const logger = require('../utils/logger');

// 数据库配置
const dbConfig = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: process.env.MYSQL_PORT || 3306,
  user: process.env.MYSQL_USER || 'root',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'farm_management',
  multipleStatements: true
};

// 迁移脚本
const migrate = async () => {
  let connection;
  
  try {
    logger.info('🔄 开始数据库迁移...');
    
    // 连接数据库
    connection = await mysql.createConnection(dbConfig);
    logger.info('✅ 数据库连接成功');
    
    // 读取迁移文件
    const migrationPath = path.join(__dirname, '../migrations/001-create-tables.sql');
    const migrationSQL = await fs.readFile(migrationPath, 'utf8');
    
    logger.info('📄 读取迁移文件成功');
    
    // 执行迁移
    await connection.execute(migrationSQL);
    logger.info('✅ 数据库迁移执行成功');
    
    // 验证表是否创建成功
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? 
      ORDER BY TABLE_NAME
    `, [dbConfig.database]);
    
    logger.info('📊 创建的表:');
    tables.forEach(table => {
      logger.info(`  - ${table.TABLE_NAME}`);
    });
    
    logger.info('🎉 数据库迁移完成！');
    
  } catch (error) {
    logger.error('❌ 数据库迁移失败:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      logger.info('🔒 数据库连接已关闭');
    }
  }
};

// 检查迁移状态
const checkMigrationStatus = async () => {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    
    // 检查是否存在迁移记录表
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'migrations'
    `, [dbConfig.database]);
    
    if (tables.length === 0) {
      logger.info('📋 迁移记录表不存在，将创建新表');
      
      // 创建迁移记录表
      await connection.execute(`
        CREATE TABLE migrations (
          id INT AUTO_INCREMENT PRIMARY KEY,
          filename VARCHAR(255) NOT NULL,
          executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          checksum VARCHAR(64),
          INDEX idx_filename (filename)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);
      
      logger.info('✅ 迁移记录表创建成功');
    }
    
    // 检查已执行的迁移
    const [migrations] = await connection.execute(`
      SELECT filename, executed_at 
      FROM migrations 
      ORDER BY executed_at DESC
    `);
    
    if (migrations.length > 0) {
      logger.info('📋 已执行的迁移:');
      migrations.forEach(migration => {
        logger.info(`  - ${migration.filename} (${migration.executed_at})`);
      });
    } else {
      logger.info('📋 尚未执行任何迁移');
    }
    
  } catch (error) {
    logger.error('❌ 检查迁移状态失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// 回滚迁移
const rollback = async (steps = 1) => {
  let connection;
  
  try {
    logger.info(`🔄 开始回滚最近 ${steps} 个迁移...`);
    
    connection = await mysql.createConnection(dbConfig);
    
    // 获取最近的迁移记录
    const [migrations] = await connection.execute(`
      SELECT filename 
      FROM migrations 
      ORDER BY executed_at DESC 
      LIMIT ?
    `, [steps]);
    
    if (migrations.length === 0) {
      logger.info('📋 没有可回滚的迁移');
      return;
    }
    
    // 这里应该执行回滚脚本
    // 由于这是创建表的迁移，回滚就是删除表
    logger.warn('⚠️  回滚操作将删除所有表和数据！');
    logger.warn('⚠️  请确认您已经备份了重要数据！');
    
    // 在生产环境中，这里应该有更安全的确认机制
    if (process.env.NODE_ENV === 'production') {
      logger.error('❌ 生产环境不允许自动回滚，请手动操作');
      return;
    }
    
    // 删除表（按依赖关系逆序）
    const tablesToDrop = [
      'sensor_data',
      'alerts', 
      'tasks',
      'crops',
      'devices',
      'fields',
      'users'
    ];
    
    for (const table of tablesToDrop) {
      try {
        await connection.execute(`DROP TABLE IF EXISTS ${table}`);
        logger.info(`🗑️  删除表: ${table}`);
      } catch (error) {
        logger.warn(`⚠️  删除表 ${table} 失败: ${error.message}`);
      }
    }
    
    // 删除迁移记录
    await connection.execute(`DELETE FROM migrations WHERE filename IN (${migrations.map(() => '?').join(',')})`, 
      migrations.map(m => m.filename));
    
    logger.info('✅ 回滚完成');
    
  } catch (error) {
    logger.error('❌ 回滚失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// 命令行参数处理
const command = process.argv[2];

switch (command) {
  case 'up':
  case 'migrate':
    migrate();
    break;
  case 'status':
    checkMigrationStatus();
    break;
  case 'rollback':
    const steps = parseInt(process.argv[3]) || 1;
    rollback(steps);
    break;
  default:
    console.log(`
使用方法:
  node migrate.js migrate     # 执行迁移
  node migrate.js status      # 检查迁移状态  
  node migrate.js rollback [steps]  # 回滚迁移
    `);
    break;
}

module.exports = {
  migrate,
  checkMigrationStatus,
  rollback
};
