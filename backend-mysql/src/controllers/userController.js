const { User } = require('../models/associations');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { validationResult } = require('express-validator');
const { successResponse, errorResponse } = require('../utils/response');
const logger = require('../utils/logger');
const { Op } = require('sequelize');

// 获取用户列表
exports.getUsers = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      role,
      status,
      department
    } = req.query;

    // 构建查询条件
    const whereClause = {};
    
    if (search) {
      whereClause[Op.or] = [
        { username: { [Op.like]: `%${search}%` } },
        { name: { [Op.like]: `%${search}%` } },
        { email: { [Op.like]: `%${search}%` } }
      ];
    }
    
    if (role) whereClause.role = role;
    if (status) whereClause.status = status;
    if (department) whereClause.department = { [Op.like]: `%${department}%` };

    // 分页查询
    const offset = (page - 1) * limit;
    const { count, rows: users } = await User.findAndCountAll({
      where: whereClause,
      attributes: { exclude: ['password'] },
      order: [['created_at', 'DESC']],
      offset,
      limit: parseInt(limit)
    });

    const pagination = {
      current: parseInt(page),
      pageSize: parseInt(limit),
      total: count,
      pages: Math.ceil(count / limit)
    };

    logger.info(`获取用户列表成功，共${count}条记录`);
    
    return successResponse(res, {
      users,
      pagination
    }, '获取用户列表成功');
  } catch (error) {
    logger.error('获取用户列表失败:', error);
    return errorResponse(res, '获取用户列表失败', 500);
  }
};

// 获取用户详情
exports.getUserById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const user = await User.findByPk(id, {
      attributes: { exclude: ['password'] }
    });
    
    if (!user) {
      return errorResponse(res, '用户不存在', 404);
    }

    logger.info(`获取用户详情成功: ${user.username}`);
    return successResponse(res, { user }, '获取用户详情成功');
  } catch (error) {
    logger.error('获取用户详情失败:', error);
    return errorResponse(res, '获取用户详情失败', 500);
  }
};

// 创建用户
exports.createUser = async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return errorResponse(res, '输入数据验证失败', 400, errors.array());
    }

    const {
      username,
      name,
      email,
      phone,
      password,
      role = 'worker',
      department,
      status = 'active'
    } = req.body;

    // 检查用户名是否已存在
    const existingUser = await User.findOne({
      where: {
        [Op.or]: [{ username }, { email }]
      }
    });
    
    if (existingUser) {
      return errorResponse(res, '用户名或邮箱已存在', 400);
    }

    // 加密密码
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // 创建用户
    const user = await User.create({
      username,
      name,
      email,
      phone,
      password: hashedPassword,
      role,
      department,
      status,
      created_by: req.user.id
    });

    // 返回用户信息（不包含密码）
    const userResponse = user.toJSON();
    delete userResponse.password;

    logger.info(`创建用户成功: ${username}`);
    return successResponse(res, { user: userResponse }, '创建用户成功', 201);
  } catch (error) {
    logger.error('创建用户失败:', error);
    return errorResponse(res, '创建用户失败', 500);
  }
};

// 更新用户
exports.updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      email,
      phone,
      role,
      department,
      status
    } = req.body;

    // 检查用户是否存在
    const user = await User.findByPk(id);
    if (!user) {
      return errorResponse(res, '用户不存在', 404);
    }

    // 检查邮箱是否被其他用户使用
    if (email && email !== user.email) {
      const existingUser = await User.findOne({ 
        where: { 
          email, 
          id: { [Op.ne]: id } 
        } 
      });
      if (existingUser) {
        return errorResponse(res, '邮箱已被其他用户使用', 400);
      }
    }

    // 更新用户信息
    const updateData = {
      ...(name && { name }),
      ...(email && { email }),
      ...(phone && { phone }),
      ...(role && { role }),
      ...(department && { department }),
      ...(status && { status }),
      updated_by: req.user.id
    };

    await user.update(updateData);

    // 获取更新后的用户信息（不包含密码）
    const updatedUser = await User.findByPk(id, {
      attributes: { exclude: ['password'] }
    });

    logger.info(`更新用户成功: ${updatedUser.username}`);
    return successResponse(res, { user: updatedUser }, '更新用户成功');
  } catch (error) {
    logger.error('更新用户失败:', error);
    return errorResponse(res, '更新用户失败', 500);
  }
};

// 删除用户
exports.deleteUser = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查用户是否存在
    const user = await User.findByPk(id);
    if (!user) {
      return errorResponse(res, '用户不存在', 404);
    }

    // 不能删除自己
    if (id == req.user.id) {
      return errorResponse(res, '不能删除自己的账户', 400);
    }

    // 软删除：标记为已删除
    await user.update({
      status: 'deleted',
      updated_by: req.user.id
    });

    logger.info(`删除用户成功: ${user.username}`);
    return successResponse(res, null, '删除用户成功');
  } catch (error) {
    logger.error('删除用户失败:', error);
    return errorResponse(res, '删除用户失败', 500);
  }
};

// 重置用户密码
exports.resetPassword = async (req, res) => {
  try {
    const { id } = req.params;
    const { newPassword } = req.body;

    if (!newPassword || newPassword.length < 6) {
      return errorResponse(res, '新密码长度不能少于6位', 400);
    }

    // 检查用户是否存在
    const user = await User.findByPk(id);
    if (!user) {
      return errorResponse(res, '用户不存在', 404);
    }

    // 加密新密码
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

    // 更新密码
    await user.update({
      password: hashedPassword,
      password_changed_at: new Date(),
      updated_by: req.user.id
    });

    logger.info(`重置用户密码成功: ${user.username}`);
    return successResponse(res, null, '重置密码成功');
  } catch (error) {
    logger.error('重置密码失败:', error);
    return errorResponse(res, '重置密码失败', 500);
  }
};

// 切换用户状态
exports.toggleUserStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!['active', 'inactive'].includes(status)) {
      return errorResponse(res, '无效的状态值', 400);
    }

    // 检查用户是否存在
    const user = await User.findByPk(id);
    if (!user) {
      return errorResponse(res, '用户不存在', 404);
    }

    // 不能禁用自己
    if (id == req.user.id && status === 'inactive') {
      return errorResponse(res, '不能禁用自己的账户', 400);
    }

    // 更新状态
    await user.update({
      status,
      updated_by: req.user.id
    });

    const updatedUser = await User.findByPk(id, {
      attributes: { exclude: ['password'] }
    });

    logger.info(`切换用户状态成功: ${user.username} -> ${status}`);
    return successResponse(res, { user: updatedUser }, '切换用户状态成功');
  } catch (error) {
    logger.error('切换用户状态失败:', error);
    return errorResponse(res, '切换用户状态失败', 500);
  }
};

// 获取用户统计信息
exports.getUserStats = async (req, res) => {
  try {
    const totalUsers = await User.count({ 
      where: { status: { [Op.ne]: 'deleted' } } 
    });
    
    const activeUsers = await User.count({ 
      where: { status: 'active' } 
    });
    
    const inactiveUsers = await User.count({ 
      where: { status: 'inactive' } 
    });
    
    // 按角色统计
    const roleStats = await User.findAll({
      attributes: [
        'role',
        [User.sequelize.fn('COUNT', User.sequelize.col('id')), 'count']
      ],
      where: { status: { [Op.ne]: 'deleted' } },
      group: ['role'],
      raw: true
    });

    // 按部门统计
    const departmentStats = await User.findAll({
      attributes: [
        'department',
        [User.sequelize.fn('COUNT', User.sequelize.col('id')), 'count']
      ],
      where: { 
        status: { [Op.ne]: 'deleted' },
        department: { [Op.ne]: null }
      },
      group: ['department'],
      raw: true
    });

    // 最近登录统计
    const recentLogins = await User.count({
      where: {
        last_login_at: { 
          [Op.gte]: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) 
        }
      }
    });

    const stats = {
      total: totalUsers,
      active: activeUsers,
      inactive: inactiveUsers,
      recentLogins,
      roleDistribution: roleStats,
      departmentDistribution: departmentStats
    };

    logger.info('获取用户统计信息成功');
    return successResponse(res, { stats }, '获取用户统计信息成功');
  } catch (error) {
    logger.error('获取用户统计信息失败:', error);
    return errorResponse(res, '获取用户统计信息失败', 500);
  }
};
