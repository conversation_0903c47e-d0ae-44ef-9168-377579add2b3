// 模型关联定义
const User = require('./User');
const Field = require('./Field');
const Crop = require('./Crop');
const Device = require('./Device');
const SensorData = require('./SensorData');
const Alert = require('./Alert');
const Task = require('./Task');

// 定义模型关联关系
const defineAssociations = () => {
  // User 关联
  User.hasMany(Field, {
    foreignKey: 'created_by',
    as: 'createdFields'
  });
  
  User.hasMany(Crop, {
    foreignKey: 'created_by',
    as: 'createdCrops'
  });
  
  User.hasMany(Device, {
    foreignKey: 'created_by',
    as: 'createdDevices'
  });
  
  User.hasMany(Alert, {
    foreignKey: 'created_by',
    as: 'createdAlerts'
  });
  
  User.hasMany(Alert, {
    foreignKey: 'read_by',
    as: 'readAlerts'
  });
  
  User.hasMany(Alert, {
    foreignKey: 'resolved_by',
    as: 'resolvedAlerts'
  });
  
  User.hasMany(Task, {
    foreignKey: 'created_by',
    as: 'createdTasks'
  });
  
  User.hasMany(Task, {
    foreignKey: 'assigned_to',
    as: 'assignedTasks'
  });

  // Field 关联
  Field.belongsTo(User, {
    foreignKey: 'created_by',
    as: 'creator'
  });
  
  Field.belongsTo(User, {
    foreignKey: 'updated_by',
    as: 'updater'
  });
  
  Field.hasMany(Crop, {
    foreignKey: 'field_id',
    as: 'crops'
  });
  
  Field.hasMany(Device, {
    foreignKey: 'field_id',
    as: 'devices'
  });
  
  Field.hasMany(Alert, {
    foreignKey: 'field_id',
    as: 'alerts'
  });
  
  Field.hasMany(Task, {
    foreignKey: 'field_id',
    as: 'tasks'
  });

  // Crop 关联
  Crop.belongsTo(Field, {
    foreignKey: 'field_id',
    as: 'field'
  });
  
  Crop.belongsTo(User, {
    foreignKey: 'created_by',
    as: 'creator'
  });
  
  Crop.belongsTo(User, {
    foreignKey: 'updated_by',
    as: 'updater'
  });
  
  Crop.hasMany(Task, {
    foreignKey: 'crop_id',
    as: 'tasks'
  });

  // Device 关联
  Device.belongsTo(Field, {
    foreignKey: 'field_id',
    as: 'field'
  });
  
  Device.belongsTo(User, {
    foreignKey: 'created_by',
    as: 'creator'
  });
  
  Device.belongsTo(User, {
    foreignKey: 'updated_by',
    as: 'updater'
  });
  
  Device.hasMany(SensorData, {
    foreignKey: 'device_id',
    as: 'sensorData'
  });
  
  Device.hasMany(Alert, {
    foreignKey: 'device_id',
    as: 'alerts'
  });

  // SensorData 关联
  SensorData.belongsTo(Device, {
    foreignKey: 'device_id',
    as: 'device'
  });

  // Alert 关联
  Alert.belongsTo(Device, {
    foreignKey: 'device_id',
    as: 'device'
  });
  
  Alert.belongsTo(Field, {
    foreignKey: 'field_id',
    as: 'field'
  });
  
  Alert.belongsTo(User, {
    foreignKey: 'created_by',
    as: 'creator'
  });
  
  Alert.belongsTo(User, {
    foreignKey: 'updated_by',
    as: 'updater'
  });
  
  Alert.belongsTo(User, {
    foreignKey: 'read_by',
    as: 'reader'
  });
  
  Alert.belongsTo(User, {
    foreignKey: 'resolved_by',
    as: 'resolver'
  });

  // Task 关联
  Task.belongsTo(Field, {
    foreignKey: 'field_id',
    as: 'field'
  });
  
  Task.belongsTo(Crop, {
    foreignKey: 'crop_id',
    as: 'crop'
  });
  
  Task.belongsTo(User, {
    foreignKey: 'assigned_to',
    as: 'assignee'
  });
  
  Task.belongsTo(User, {
    foreignKey: 'created_by',
    as: 'creator'
  });
  
  Task.belongsTo(User, {
    foreignKey: 'updated_by',
    as: 'updater'
  });

  console.log('✅ 模型关联定义完成');
};

// 导出所有模型和关联定义函数
module.exports = {
  User,
  Field,
  Crop,
  Device,
  SensorData,
  Alert,
  Task,
  defineAssociations
};
