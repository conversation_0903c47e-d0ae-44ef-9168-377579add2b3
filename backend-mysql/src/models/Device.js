const { DataTypes, Model } = require('sequelize');
const { sequelize } = require('../config/database');

class Device extends Model {
  // 实例方法：更新在线状态
  async updateOnlineStatus(isOnline) {
    this.is_online = isOnline;
    this.last_seen_at = new Date();
    if (isOnline) {
      this.last_online_at = new Date();
    }
    await this.save();
    return this;
  }

  // 实例方法：更新最后数据时间
  async updateLastDataTime() {
    this.last_data_time = new Date();
    await this.save();
    return this;
  }

  // 实例方法：检查是否在线
  isOnline() {
    return this.is_online;
  }

  // 实例方法：检查是否激活
  isActive() {
    return this.status === 'active';
  }

  // 实例方法：获取设备类型显示名称
  getTypeDisplayName() {
    const typeNames = {
      sensor: '传感器',
      camera: '摄像头',
      controller: '控制器',
      irrigation: '灌溉设备',
      weather: '气象站'
    };
    return typeNames[this.type] || this.type;
  }

  // 实例方法：获取状态显示名称
  getStatusDisplayName() {
    const statusNames = {
      active: '正常',
      inactive: '停用',
      maintenance: '维护中',
      error: '故障'
    };
    return statusNames[this.status] || this.status;
  }

  // 实例方法：检查是否需要维护
  needsMaintenance() {
    if (!this.last_maintenance_at) return true;
    
    const maintenanceInterval = this.maintenance_interval || 30; // 默认30天
    const daysSinceLastMaintenance = (Date.now() - this.last_maintenance_at.getTime()) / (1000 * 60 * 60 * 24);
    
    return daysSinceLastMaintenance >= maintenanceInterval;
  }

  // 实例方法：记录维护
  async recordMaintenance(description, performedBy) {
    this.last_maintenance_at = new Date();
    this.maintenance_notes = description;
    this.updated_by = performedBy;
    await this.save();
    return this;
  }

  // 静态方法：按类型查找设备
  static findByType(type) {
    return this.findAll({
      where: { type },
      order: [['name', 'ASC']]
    });
  }

  // 静态方法：查找在线设备
  static findOnline() {
    return this.findAll({
      where: { is_online: true },
      order: [['name', 'ASC']]
    });
  }

  // 静态方法：查找离线设备
  static findOffline() {
    return this.findAll({
      where: { is_online: false },
      order: [['last_seen_at', 'DESC']]
    });
  }

  // 静态方法：查找需要维护的设备
  static async findNeedingMaintenance() {
    const devices = await this.findAll({
      where: { status: 'active' }
    });
    
    return devices.filter(device => device.needsMaintenance());
  }

  // 静态方法：获取设备统计
  static async getStats() {
    const total = await this.count({
      where: { status: { [sequelize.Sequelize.Op.ne]: 'deleted' } }
    });
    
    const online = await this.count({
      where: { is_online: true }
    });
    
    const active = await this.count({
      where: { status: 'active' }
    });
    
    const maintenance = await this.count({
      where: { status: 'maintenance' }
    });
    
    const error = await this.count({
      where: { status: 'error' }
    });

    // 按类型统计
    const typeStats = await this.findAll({
      attributes: [
        'type',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: { status: { [sequelize.Sequelize.Op.ne]: 'deleted' } },
      group: ['type'],
      raw: true
    });

    // 按地块统计
    const fieldStats = await this.findAll({
      attributes: [
        'field_id',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: { 
        status: { [sequelize.Sequelize.Op.ne]: 'deleted' },
        field_id: { [sequelize.Sequelize.Op.ne]: null }
      },
      group: ['field_id'],
      raw: true,
      include: [
        { association: 'field', attributes: ['name'] }
      ]
    });

    return {
      total,
      online,
      offline: total - online,
      active,
      maintenance,
      error,
      onlineRate: total > 0 ? Math.round((online / total) * 100) : 0,
      typeDistribution: typeStats,
      fieldDistribution: fieldStats
    };
  }

  // 静态方法：批量更新在线状态
  static async batchUpdateOnlineStatus(deviceUpdates) {
    const transaction = await sequelize.transaction();
    
    try {
      const promises = deviceUpdates.map(({ deviceId, isOnline }) => {
        return this.update(
          {
            is_online: isOnline,
            last_seen_at: new Date(),
            ...(isOnline && { last_online_at: new Date() })
          },
          {
            where: { device_id: deviceId },
            transaction
          }
        );
      });
      
      await Promise.all(promises);
      await transaction.commit();
      
      return true;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}

// 定义模型
Device.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  
  device_id: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    validate: {
      len: [1, 100]
    }
  },
  
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      len: [1, 100]
    }
  },
  
  type: {
    type: DataTypes.ENUM('sensor', 'camera', 'controller', 'irrigation', 'weather'),
    allowNull: false
  },
  
  model: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  
  manufacturer: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  
  version: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'maintenance', 'error', 'deleted'),
    allowNull: false,
    defaultValue: 'active'
  },
  
  is_online: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  
  field_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'fields',
      key: 'id'
    }
  },
  
  location: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '设备位置坐标 {lat, lng, altitude}'
  },
  
  position: {
    type: DataTypes.STRING(200),
    allowNull: true,
    comment: '设备位置描述'
  },
  
  configuration: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
    comment: '设备配置参数'
  },
  
  specifications: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
    comment: '设备技术规格'
  },
  
  last_seen_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  last_online_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  last_data_time: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  installation_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  warranty_expiry: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  last_maintenance_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  maintenance_interval: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 30,
    comment: '维护间隔天数'
  },
  
  maintenance_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  sequelize,
  modelName: 'Device',
  tableName: 'devices',
  timestamps: true,
  
  // 索引
  indexes: [
    {
      unique: true,
      fields: ['device_id']
    },
    {
      fields: ['type']
    },
    {
      fields: ['status']
    },
    {
      fields: ['is_online']
    },
    {
      fields: ['field_id']
    },
    {
      fields: ['last_seen_at']
    },
    {
      fields: ['last_data_time']
    },
    {
      fields: ['installation_date']
    },
    {
      fields: ['warranty_expiry']
    },
    {
      fields: ['last_maintenance_at']
    }
  ],
  
  // 作用域
  scopes: {
    active: {
      where: { status: 'active' }
    },
    
    online: {
      where: { is_online: true }
    },
    
    offline: {
      where: { is_online: false }
    },
    
    byType: (type) => ({
      where: { type }
    }),
    
    byField: (fieldId) => ({
      where: { field_id: fieldId }
    }),
    
    needsMaintenance: {
      where: {
        [sequelize.Sequelize.Op.or]: [
          { last_maintenance_at: null },
          sequelize.where(
            sequelize.fn('DATEDIFF', sequelize.fn('NOW'), sequelize.col('last_maintenance_at')),
            '>=',
            sequelize.col('maintenance_interval')
          )
        ]
      }
    }
  }
});

module.exports = Device;
