const { DataTypes, Model } = require('sequelize');
const bcrypt = require('bcryptjs');
const { sequelize } = require('../config/database');

class User extends Model {
  // 实例方法：验证密码
  async validatePassword(password) {
    return await bcrypt.compare(password, this.password);
  }

  // 实例方法：更新最后登录时间
  async updateLastLogin() {
    this.last_login_at = new Date();
    this.login_count = (this.login_count || 0) + 1;
    await this.save();
  }

  // 实例方法：检查是否激活
  isActive() {
    return this.status === 'active';
  }

  // 实例方法：检查权限
  hasPermission(permission) {
    const rolePermissions = {
      admin: ['*'],
      manager: [
        'users.read', 'users.create', 'users.update',
        'fields.*', 'crops.*', 'devices.*', 'harvests.*',
        'materials.*', 'tasks.*', 'reports.*'
      ],
      worker: [
        'fields.read', 'crops.read', 'devices.read',
        'tasks.read', 'tasks.update', 'harvests.create',
        'materials.read'
      ],
      viewer: [
        'fields.read', 'crops.read', 'devices.read',
        'harvests.read', 'materials.read', 'reports.read'
      ]
    };

    const permissions = rolePermissions[this.role] || [];
    return permissions.includes('*') || permissions.includes(permission);
  }

  // 实例方法：获取安全的用户信息（不包含密码）
  toSafeObject() {
    const user = this.toJSON();
    delete user.password;
    return user;
  }

  // 静态方法：根据用户名或邮箱查找用户
  static async findByUsernameOrEmail(identifier) {
    return await this.findOne({
      where: {
        [sequelize.Sequelize.Op.or]: [
          { username: identifier },
          { email: identifier }
        ]
      }
    });
  }

  // 静态方法：创建用户
  static async createUser(userData) {
    const { password, ...otherData } = userData;
    
    // 加密密码
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);
    
    return await this.create({
      ...otherData,
      password: hashedPassword
    });
  }

  // 静态方法：获取用户统计
  static async getStats() {
    const total = await this.count({
      where: { status: { [sequelize.Sequelize.Op.ne]: 'deleted' } }
    });
    
    const active = await this.count({
      where: { status: 'active' }
    });
    
    const inactive = await this.count({
      where: { status: 'inactive' }
    });

    // 按角色统计
    const roleStats = await this.findAll({
      attributes: [
        'role',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: { status: { [sequelize.Sequelize.Op.ne]: 'deleted' } },
      group: ['role'],
      raw: true
    });

    // 按部门统计
    const departmentStats = await this.findAll({
      attributes: [
        'department',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: { 
        status: { [sequelize.Sequelize.Op.ne]: 'deleted' },
        department: { [sequelize.Sequelize.Op.ne]: null }
      },
      group: ['department'],
      raw: true
    });

    // 最近7天登录用户
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const recentLogins = await this.count({
      where: {
        last_login_at: { [sequelize.Sequelize.Op.gte]: sevenDaysAgo }
      }
    });

    return {
      total,
      active,
      inactive,
      recentLogins,
      roleDistribution: roleStats,
      departmentDistribution: departmentStats
    };
  }
}

// 定义模型
User.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  
  username: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    validate: {
      len: [3, 50],
      isAlphanumeric: true
    }
  },
  
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      len: [1, 100]
    }
  },
  
  email: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    validate: {
      is: /^[0-9+\-\s()]+$/
    }
  },
  
  password: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      len: [6, 255]
    }
  },
  
  role: {
    type: DataTypes.ENUM('admin', 'manager', 'worker', 'viewer'),
    allowNull: false,
    defaultValue: 'worker'
  },
  
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'deleted'),
    allowNull: false,
    defaultValue: 'active'
  },
  
  department: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  
  avatar: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  last_login_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  login_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  
  password_changed_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  
  deleted_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  
  deleted_at: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  sequelize,
  modelName: 'User',
  tableName: 'users',
  timestamps: true,
  paranoid: false, // 不使用软删除，手动管理deleted_at
  
  // 索引
  indexes: [
    {
      unique: true,
      fields: ['username']
    },
    {
      unique: true,
      fields: ['email']
    },
    {
      fields: ['status']
    },
    {
      fields: ['role']
    },
    {
      fields: ['department']
    },
    {
      fields: ['last_login_at']
    },
    {
      fields: ['created_at']
    }
  ],
  
  // 钩子函数
  hooks: {
    beforeCreate: async (user) => {
      // 创建前验证用户名和邮箱唯一性
      const existingUser = await User.findByUsernameOrEmail(user.username);
      if (existingUser) {
        throw new Error('用户名或邮箱已存在');
      }
    },
    
    beforeUpdate: async (user) => {
      // 更新前检查邮箱唯一性
      if (user.changed('email')) {
        const existingUser = await User.findOne({
          where: {
            email: user.email,
            id: { [sequelize.Sequelize.Op.ne]: user.id }
          }
        });
        if (existingUser) {
          throw new Error('邮箱已被其他用户使用');
        }
      }
      
      // 如果密码被修改，更新密码修改时间
      if (user.changed('password')) {
        user.password_changed_at = new Date();
      }
    }
  },
  
  // 作用域
  scopes: {
    active: {
      where: { status: 'active' }
    },
    
    withoutPassword: {
      attributes: { exclude: ['password'] }
    },
    
    byRole: (role) => ({
      where: { role }
    }),
    
    byDepartment: (department) => ({
      where: { department }
    }),
    
    recentLogin: (days = 7) => ({
      where: {
        last_login_at: {
          [sequelize.Sequelize.Op.gte]: new Date(Date.now() - days * 24 * 60 * 60 * 1000)
        }
      }
    })
  }
});

module.exports = User;
