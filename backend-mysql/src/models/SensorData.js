const { DataTypes, Model } = require('sequelize');
const { sequelize } = require('../config/database');

class SensorData extends Model {
  // 实例方法：获取数据值
  getValue(parameter) {
    const data = this.data || {};
    return data[parameter];
  }

  // 实例方法：检查是否超出阈值
  isOutOfThreshold(parameter, threshold) {
    const value = this.getValue(parameter);
    if (value === undefined || value === null) return false;
    
    const { min, max, operator, value: thresholdValue } = threshold;
    
    if (min !== undefined && value < min) return true;
    if (max !== undefined && value > max) return true;
    
    if (operator && thresholdValue !== undefined) {
      switch (operator) {
        case '>':
          return value > thresholdValue;
        case '<':
          return value < thresholdValue;
        case '>=':
          return value >= thresholdValue;
        case '<=':
          return value <= thresholdValue;
        case '==':
          return value === thresholdValue;
        case '!=':
          return value !== thresholdValue;
        default:
          return false;
      }
    }
    
    return false;
  }

  // 实例方法：获取数据质量评分
  getDataQuality() {
    const data = this.data || {};
    const expectedParameters = ['temperature', 'humidity', 'soilMoisture'];
    
    let score = 0;
    let totalParameters = expectedParameters.length;
    
    expectedParameters.forEach(param => {
      const value = data[param];
      if (value !== undefined && value !== null && !isNaN(value)) {
        score++;
      }
    });
    
    return Math.round((score / totalParameters) * 100);
  }

  // 静态方法：按设备查找数据
  static findByDevice(deviceId, options = {}) {
    const { startTime, endTime, limit = 100, order = 'DESC' } = options;
    
    let whereClause = { device_id: deviceId };
    
    if (startTime || endTime) {
      whereClause.timestamp = {};
      if (startTime) whereClause.timestamp[sequelize.Sequelize.Op.gte] = startTime;
      if (endTime) whereClause.timestamp[sequelize.Sequelize.Op.lte] = endTime;
    }
    
    return this.findAll({
      where: whereClause,
      order: [['timestamp', order]],
      limit,
      include: [
        { association: 'device', attributes: ['id', 'name', 'type'] }
      ]
    });
  }

  // 静态方法：获取最新数据
  static async getLatestData(deviceId) {
    return await this.findOne({
      where: { device_id: deviceId },
      order: [['timestamp', 'DESC']],
      include: [
        { association: 'device', attributes: ['id', 'name', 'type'] }
      ]
    });
  }

  // 静态方法：获取时间范围内的统计数据
  static async getStatistics(deviceId, parameter, startTime, endTime) {
    const data = await this.findAll({
      where: {
        device_id: deviceId,
        timestamp: {
          [sequelize.Sequelize.Op.between]: [startTime, endTime]
        }
      },
      order: [['timestamp', 'ASC']]
    });
    
    const values = data
      .map(record => record.getValue(parameter))
      .filter(value => value !== undefined && value !== null && !isNaN(value));
    
    if (values.length === 0) {
      return {
        count: 0,
        min: null,
        max: null,
        avg: null,
        latest: null
      };
    }
    
    const min = Math.min(...values);
    const max = Math.max(...values);
    const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
    const latest = values[values.length - 1];
    
    return {
      count: values.length,
      min: Math.round(min * 100) / 100,
      max: Math.round(max * 100) / 100,
      avg: Math.round(avg * 100) / 100,
      latest: Math.round(latest * 100) / 100
    };
  }

  // 静态方法：获取趋势数据
  static async getTrendData(deviceId, parameter, timeRange = '24h') {
    let startTime;
    let groupBy;
    
    switch (timeRange) {
      case '1h':
        startTime = new Date(Date.now() - 60 * 60 * 1000);
        groupBy = 'MINUTE';
        break;
      case '24h':
        startTime = new Date(Date.now() - 24 * 60 * 60 * 1000);
        groupBy = 'HOUR';
        break;
      case '7d':
        startTime = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        groupBy = 'DAY';
        break;
      case '30d':
        startTime = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        groupBy = 'DAY';
        break;
      default:
        startTime = new Date(Date.now() - 24 * 60 * 60 * 1000);
        groupBy = 'HOUR';
    }
    
    const query = `
      SELECT 
        DATE_FORMAT(timestamp, '${this.getDateFormat(groupBy)}') as time_group,
        AVG(JSON_EXTRACT(data, '$.${parameter}')) as avg_value,
        MIN(JSON_EXTRACT(data, '$.${parameter}')) as min_value,
        MAX(JSON_EXTRACT(data, '$.${parameter}')) as max_value,
        COUNT(*) as count
      FROM sensor_data 
      WHERE device_id = :deviceId 
        AND timestamp >= :startTime
        AND JSON_EXTRACT(data, '$.${parameter}') IS NOT NULL
      GROUP BY time_group
      ORDER BY time_group ASC
    `;
    
    const results = await sequelize.query(query, {
      replacements: { deviceId, startTime },
      type: sequelize.QueryTypes.SELECT
    });
    
    return results.map(row => ({
      time: row.time_group,
      avg: row.avg_value ? Math.round(row.avg_value * 100) / 100 : null,
      min: row.min_value ? Math.round(row.min_value * 100) / 100 : null,
      max: row.max_value ? Math.round(row.max_value * 100) / 100 : null,
      count: row.count
    }));
  }

  // 静态方法：获取日期格式
  static getDateFormat(groupBy) {
    switch (groupBy) {
      case 'MINUTE':
        return '%Y-%m-%d %H:%i';
      case 'HOUR':
        return '%Y-%m-%d %H:00';
      case 'DAY':
        return '%Y-%m-%d';
      default:
        return '%Y-%m-%d %H:00';
    }
  }

  // 静态方法：批量插入数据
  static async bulkInsert(dataArray) {
    const transaction = await sequelize.transaction();
    
    try {
      const results = await this.bulkCreate(dataArray, {
        transaction,
        validate: true
      });
      
      await transaction.commit();
      return results;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  // 静态方法：清理旧数据
  static async cleanupOldData(retentionDays = 90) {
    const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000);
    
    const deletedCount = await this.destroy({
      where: {
        timestamp: {
          [sequelize.Sequelize.Op.lt]: cutoffDate
        }
      }
    });
    
    return deletedCount;
  }

  // 静态方法：获取数据统计概览
  static async getOverviewStats() {
    const totalRecords = await this.count();
    
    const last24Hours = await this.count({
      where: {
        timestamp: {
          [sequelize.Sequelize.Op.gte]: new Date(Date.now() - 24 * 60 * 60 * 1000)
        }
      }
    });
    
    const deviceStats = await this.findAll({
      attributes: [
        'device_id',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
        [sequelize.fn('MAX', sequelize.col('timestamp')), 'lastUpdate']
      ],
      group: ['device_id'],
      include: [
        { association: 'device', attributes: ['name', 'type'] }
      ],
      raw: false
    });
    
    return {
      totalRecords,
      last24Hours,
      deviceCount: deviceStats.length,
      deviceStats: deviceStats.map(stat => ({
        deviceId: stat.device_id,
        deviceName: stat.device?.name,
        deviceType: stat.device?.type,
        recordCount: stat.dataValues.count,
        lastUpdate: stat.dataValues.lastUpdate
      }))
    };
  }
}

// 定义模型
SensorData.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  
  device_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'devices',
      key: 'id'
    }
  },
  
  timestamp: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  
  data: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: {},
    comment: '传感器数据，包含各种参数值'
  },
  
  data_quality: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 0,
      max: 100
    },
    comment: '数据质量评分 0-100'
  },
  
  signal_strength: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: -100,
      max: 0
    },
    comment: '信号强度 dBm'
  },
  
  battery_level: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 0,
      max: 100
    },
    comment: '电池电量百分比'
  },
  
  is_valid: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '数据是否有效'
  },
  
  validation_errors: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '数据验证错误信息'
  }
}, {
  sequelize,
  modelName: 'SensorData',
  tableName: 'sensor_data',
  timestamps: false, // 使用自定义的timestamp字段
  
  // 索引
  indexes: [
    {
      fields: ['device_id', 'timestamp']
    },
    {
      fields: ['timestamp']
    },
    {
      fields: ['device_id']
    },
    {
      fields: ['is_valid']
    },
    {
      fields: ['data_quality']
    }
  ],
  
  // 钩子函数
  hooks: {
    beforeCreate: (sensorData) => {
      // 计算数据质量评分
      if (!sensorData.data_quality) {
        sensorData.data_quality = sensorData.getDataQuality();
      }
    },
    
    beforeBulkCreate: (sensorDataArray) => {
      sensorDataArray.forEach(sensorData => {
        if (!sensorData.data_quality) {
          sensorData.data_quality = sensorData.getDataQuality();
        }
      });
    }
  },
  
  // 作用域
  scopes: {
    valid: {
      where: { is_valid: true }
    },
    
    recent: (hours = 24) => ({
      where: {
        timestamp: {
          [sequelize.Sequelize.Op.gte]: new Date(Date.now() - hours * 60 * 60 * 1000)
        }
      }
    }),
    
    byDevice: (deviceId) => ({
      where: { device_id: deviceId }
    }),
    
    highQuality: {
      where: {
        data_quality: { [sequelize.Sequelize.Op.gte]: 80 }
      }
    },
    
    lowBattery: {
      where: {
        battery_level: { [sequelize.Sequelize.Op.lte]: 20 }
      }
    }
  }
});

module.exports = SensorData;
