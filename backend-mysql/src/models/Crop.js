const { DataTypes, Model } = require('sequelize');
const { sequelize } = require('../config/database');

class Crop extends Model {
  // 实例方法：计算生长天数
  getDaysFromPlanting() {
    if (!this.planting_date) return 0;
    return Math.floor((Date.now() - this.planting_date.getTime()) / (1000 * 60 * 60 * 24));
  }

  // 实例方法：计算预期收获天数
  getDaysToHarvest() {
    if (!this.expected_harvest_date) return null;
    const days = Math.floor((this.expected_harvest_date.getTime() - Date.now()) / (1000 * 60 * 60 * 24));
    return days > 0 ? days : 0;
  }

  // 实例方法：获取当前生长阶段
  getCurrentGrowthStage() {
    const daysFromPlanting = this.getDaysFromPlanting();
    const growthStages = this.growth_stages || [];
    
    for (let i = growthStages.length - 1; i >= 0; i--) {
      if (daysFromPlanting >= growthStages[i].startDay) {
        return growthStages[i];
      }
    }
    
    return growthStages[0] || { name: '种植期', startDay: 0 };
  }

  // 实例方法：计算生长进度百分比
  getGrowthProgress() {
    if (!this.planting_date || !this.expected_harvest_date) return 0;
    
    const totalDays = (this.expected_harvest_date.getTime() - this.planting_date.getTime()) / (1000 * 60 * 60 * 24);
    const elapsedDays = this.getDaysFromPlanting();
    
    const progress = Math.min((elapsedDays / totalDays) * 100, 100);
    return Math.round(progress);
  }

  // 实例方法：检查是否可以收获
  canHarvest() {
    return this.status === 'growing' && this.getDaysToHarvest() <= 0;
  }

  // 实例方法：检查是否需要关注
  needsAttention() {
    const daysToHarvest = this.getDaysToHarvest();
    return (
      this.status === 'growing' && 
      daysToHarvest !== null && 
      daysToHarvest <= 7 && 
      daysToHarvest > 0
    );
  }

  // 实例方法：更新状态
  async updateStatus(newStatus, userId) {
    this.status = newStatus;
    this.updated_by = userId;
    
    if (newStatus === 'harvested') {
      this.actual_harvest_date = new Date();
    }
    
    await this.save();
    return this;
  }

  // 实例方法：记录生长记录
  async addGrowthRecord(record, userId) {
    const growthRecords = this.growth_records || [];
    growthRecords.push({
      ...record,
      date: new Date(),
      recordedBy: userId
    });
    
    this.growth_records = growthRecords;
    this.updated_by = userId;
    await this.save();
    return this;
  }

  // 静态方法：按状态查找作物
  static findByStatus(status) {
    return this.findAll({
      where: { status },
      order: [['planting_date', 'DESC']]
    });
  }

  // 静态方法：按品种查找作物
  static findByVariety(variety) {
    return this.findAll({
      where: { variety },
      order: [['planting_date', 'DESC']]
    });
  }

  // 静态方法：查找即将收获的作物
  static findReadyForHarvest(days = 7) {
    const targetDate = new Date(Date.now() + days * 24 * 60 * 60 * 1000);
    
    return this.findAll({
      where: {
        status: 'growing',
        expected_harvest_date: {
          [sequelize.Sequelize.Op.lte]: targetDate
        }
      },
      order: [['expected_harvest_date', 'ASC']]
    });
  }

  // 静态方法：按地块查找作物
  static findByField(fieldId) {
    return this.findAll({
      where: { field_id: fieldId },
      order: [['planting_date', 'DESC']]
    });
  }

  // 静态方法：获取作物统计
  static async getStats() {
    const total = await this.count();
    const growing = await this.count({ where: { status: 'growing' } });
    const harvested = await this.count({ where: { status: 'harvested' } });
    const planned = await this.count({ where: { status: 'planned' } });
    
    // 按品种统计
    const varietyStats = await this.findAll({
      attributes: [
        'variety',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
        [sequelize.fn('SUM', sequelize.col('area')), 'totalArea']
      ],
      group: ['variety'],
      raw: true
    });

    // 按状态统计
    const statusStats = await this.findAll({
      attributes: [
        'status',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['status'],
      raw: true
    });

    // 按地块统计
    const fieldStats = await this.findAll({
      attributes: [
        'field_id',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
        [sequelize.fn('SUM', sequelize.col('area')), 'totalArea']
      ],
      where: { field_id: { [sequelize.Sequelize.Op.ne]: null } },
      group: ['field_id'],
      raw: true,
      include: [
        { association: 'field', attributes: ['name'] }
      ]
    });

    // 即将收获的作物数量
    const readyForHarvest = await this.count({
      where: {
        status: 'growing',
        expected_harvest_date: {
          [sequelize.Sequelize.Op.lte]: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
        }
      }
    });

    return {
      total,
      growing,
      harvested,
      planned,
      readyForHarvest,
      varietyDistribution: varietyStats,
      statusDistribution: statusStats,
      fieldDistribution: fieldStats
    };
  }

  // 静态方法：获取产量统计
  static async getYieldStats(timeRange = '1year') {
    let whereClause = { status: 'harvested' };
    
    if (timeRange === '1month') {
      whereClause.actual_harvest_date = {
        [sequelize.Sequelize.Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      };
    } else if (timeRange === '1year') {
      whereClause.actual_harvest_date = {
        [sequelize.Sequelize.Op.gte]: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)
      };
    }

    const yieldStats = await this.findAll({
      attributes: [
        'variety',
        [sequelize.fn('COUNT', sequelize.col('id')), 'cropCount'],
        [sequelize.fn('SUM', sequelize.col('area')), 'totalArea'],
        [sequelize.fn('SUM', sequelize.col('actual_yield')), 'totalYield'],
        [sequelize.fn('AVG', sequelize.col('actual_yield')), 'avgYield']
      ],
      where: whereClause,
      group: ['variety'],
      raw: true
    });

    return yieldStats;
  }
}

// 定义模型
Crop.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      len: [1, 100]
    }
  },
  
  variety: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      len: [1, 100]
    }
  },
  
  field_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'fields',
      key: 'id'
    }
  },
  
  area: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    },
    comment: '种植面积（亩）'
  },
  
  status: {
    type: DataTypes.ENUM('planned', 'planted', 'growing', 'harvested', 'failed'),
    allowNull: false,
    defaultValue: 'planned'
  },
  
  planting_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  expected_harvest_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  actual_harvest_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  expected_yield: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    },
    comment: '预期产量（公斤）'
  },
  
  actual_yield: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    },
    comment: '实际产量（公斤）'
  },
  
  growth_stages: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: '生长阶段定义'
  },
  
  growth_records: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: '生长记录'
  },
  
  care_instructions: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
    comment: '护理说明'
  },
  
  environmental_requirements: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
    comment: '环境要求'
  },
  
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  images: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: '作物图片'
  },
  
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  sequelize,
  modelName: 'Crop',
  tableName: 'crops',
  timestamps: true,
  
  // 索引
  indexes: [
    {
      fields: ['variety']
    },
    {
      fields: ['field_id']
    },
    {
      fields: ['status']
    },
    {
      fields: ['planting_date']
    },
    {
      fields: ['expected_harvest_date']
    },
    {
      fields: ['actual_harvest_date']
    },
    {
      fields: ['status', 'expected_harvest_date']
    }
  ],
  
  // 作用域
  scopes: {
    growing: {
      where: { status: 'growing' }
    },
    
    harvested: {
      where: { status: 'harvested' }
    },
    
    planned: {
      where: { status: 'planned' }
    },
    
    byVariety: (variety) => ({
      where: { variety }
    }),
    
    byField: (fieldId) => ({
      where: { field_id: fieldId }
    }),
    
    readyForHarvest: (days = 7) => ({
      where: {
        status: 'growing',
        expected_harvest_date: {
          [sequelize.Sequelize.Op.lte]: new Date(Date.now() + days * 24 * 60 * 60 * 1000)
        }
      }
    }),
    
    recentlyHarvested: (days = 30) => ({
      where: {
        status: 'harvested',
        actual_harvest_date: {
          [sequelize.Sequelize.Op.gte]: new Date(Date.now() - days * 24 * 60 * 60 * 1000)
        }
      }
    })
  }
});

module.exports = Crop;
