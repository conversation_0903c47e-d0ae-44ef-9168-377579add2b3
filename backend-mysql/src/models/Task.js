const { DataTypes, Model } = require('sequelize');
const { sequelize } = require('../config/database');

class Task extends Model {
  // 实例方法：检查是否逾期
  isOverdue() {
    if (this.status === 'completed' || this.status === 'cancelled') return false;
    return new Date() > this.due_date;
  }

  // 实例方法：计算进度百分比
  getProgress() {
    if (this.status === 'completed') return 100;
    if (this.status === 'cancelled') return 0;
    if (this.status === 'pending') return 0;
    
    // 根据时间计算进度
    const now = new Date();
    const start = new Date(this.start_date || this.created_at);
    const end = new Date(this.due_date);
    
    if (now >= end) return 100;
    if (now <= start) return 0;
    
    const total = end - start;
    const elapsed = now - start;
    return Math.min(Math.round((elapsed / total) * 100), 100);
  }

  // 实例方法：获取剩余天数
  getDaysRemaining() {
    if (this.status === 'completed' || this.status === 'cancelled') return 0;
    
    const now = new Date();
    const due = new Date(this.due_date);
    const diffTime = due - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return Math.max(diffDays, 0);
  }

  // 实例方法：获取优先级显示名称
  getPriorityDisplayName() {
    const priorityNames = {
      low: '低',
      medium: '中',
      high: '高',
      urgent: '紧急'
    };
    return priorityNames[this.priority] || this.priority;
  }

  // 实例方法：获取状态显示名称
  getStatusDisplayName() {
    const statusNames = {
      pending: '待处理',
      in_progress: '进行中',
      completed: '已完成',
      cancelled: '已取消'
    };
    return statusNames[this.status] || this.status;
  }

  // 实例方法：获取类型显示名称
  getTypeDisplayName() {
    const typeNames = {
      planting: '种植',
      irrigation: '灌溉',
      fertilization: '施肥',
      harvesting: '收获',
      maintenance: '维护',
      inspection: '检查',
      other: '其他'
    };
    return typeNames[this.type] || this.type;
  }

  // 实例方法：开始任务
  async start(userId) {
    this.status = 'in_progress';
    this.started_at = new Date();
    this.updated_by = userId;
    await this.save();
    return this;
  }

  // 实例方法：完成任务
  async complete(completionNotes, userId) {
    this.status = 'completed';
    this.completed_at = new Date();
    this.completion_notes = completionNotes;
    this.updated_by = userId;
    await this.save();
    return this;
  }

  // 实例方法：取消任务
  async cancel(reason, userId) {
    this.status = 'cancelled';
    this.cancelled_at = new Date();
    this.cancellation_reason = reason;
    this.updated_by = userId;
    await this.save();
    return this;
  }

  // 实例方法：添加进度记录
  async addProgressRecord(record, userId) {
    const progressRecords = this.progress_records || [];
    progressRecords.push({
      ...record,
      timestamp: new Date(),
      recordedBy: userId
    });
    
    this.progress_records = progressRecords;
    this.updated_by = userId;
    await this.save();
    return this;
  }

  // 静态方法：按状态查找任务
  static findByStatus(status) {
    return this.findAll({
      where: { status },
      order: [['due_date', 'ASC']]
    });
  }

  // 静态方法：按优先级查找任务
  static findByPriority(priority) {
    return this.findAll({
      where: { priority },
      order: [['due_date', 'ASC']]
    });
  }

  // 静态方法：按类型查找任务
  static findByType(type) {
    return this.findAll({
      where: { type },
      order: [['due_date', 'ASC']]
    });
  }

  // 静态方法：查找逾期任务
  static findOverdue() {
    return this.findAll({
      where: {
        status: ['pending', 'in_progress'],
        due_date: {
          [sequelize.Sequelize.Op.lt]: new Date()
        }
      },
      order: [['due_date', 'ASC']]
    });
  }

  // 静态方法：查找即将到期的任务
  static findDueSoon(days = 3) {
    const targetDate = new Date(Date.now() + days * 24 * 60 * 60 * 1000);
    
    return this.findAll({
      where: {
        status: ['pending', 'in_progress'],
        due_date: {
          [sequelize.Sequelize.Op.between]: [new Date(), targetDate]
        }
      },
      order: [['due_date', 'ASC']]
    });
  }

  // 静态方法：按负责人查找任务
  static findByAssignee(assigneeId) {
    return this.findAll({
      where: { assigned_to: assigneeId },
      order: [['due_date', 'ASC']]
    });
  }

  // 静态方法：按地块查找任务
  static findByField(fieldId) {
    return this.findAll({
      where: { field_id: fieldId },
      order: [['due_date', 'ASC']]
    });
  }

  // 静态方法：获取任务统计
  static async getStats() {
    const total = await this.count();
    const pending = await this.count({ where: { status: 'pending' } });
    const inProgress = await this.count({ where: { status: 'in_progress' } });
    const completed = await this.count({ where: { status: 'completed' } });
    const cancelled = await this.count({ where: { status: 'cancelled' } });
    
    // 逾期任务数量
    const overdue = await this.count({
      where: {
        status: ['pending', 'in_progress'],
        due_date: { [sequelize.Sequelize.Op.lt]: new Date() }
      }
    });
    
    // 即将到期任务数量
    const dueSoon = await this.count({
      where: {
        status: ['pending', 'in_progress'],
        due_date: {
          [sequelize.Sequelize.Op.between]: [
            new Date(),
            new Date(Date.now() + 3 * 24 * 60 * 60 * 1000)
          ]
        }
      }
    });
    
    // 按优先级统计
    const priorityStats = await this.findAll({
      attributes: [
        'priority',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['priority'],
      raw: true
    });
    
    // 按类型统计
    const typeStats = await this.findAll({
      attributes: [
        'type',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['type'],
      raw: true
    });
    
    // 按负责人统计
    const assigneeStats = await this.findAll({
      attributes: [
        'assigned_to',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: { assigned_to: { [sequelize.Sequelize.Op.ne]: null } },
      group: ['assigned_to'],
      include: [
        { association: 'assignee', attributes: ['name', 'username'] }
      ],
      raw: false
    });
    
    // 完成率
    const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0;

    return {
      total,
      pending,
      inProgress,
      completed,
      cancelled,
      overdue,
      dueSoon,
      completionRate,
      priorityDistribution: priorityStats,
      typeDistribution: typeStats,
      assigneeDistribution: assigneeStats.map(stat => ({
        assigneeId: stat.assigned_to,
        assigneeName: stat.assignee?.name,
        assigneeUsername: stat.assignee?.username,
        taskCount: stat.dataValues.count
      }))
    };
  }

  // 静态方法：获取今日任务
  static findTodayTasks() {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
    
    return this.findAll({
      where: {
        due_date: {
          [sequelize.Sequelize.Op.between]: [startOfDay, endOfDay]
        }
      },
      order: [['priority', 'DESC'], ['due_date', 'ASC']]
    });
  }

  // 静态方法：获取任务完成趋势
  static async getCompletionTrend(days = 30) {
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    
    const query = `
      SELECT 
        DATE(completed_at) as date,
        COUNT(*) as completed_count
      FROM tasks 
      WHERE status = 'completed' 
        AND completed_at >= :startDate
      GROUP BY DATE(completed_at)
      ORDER BY date ASC
    `;
    
    const results = await sequelize.query(query, {
      replacements: { startDate },
      type: sequelize.QueryTypes.SELECT
    });
    
    return results;
  }
}

// 定义模型
Task.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  
  title: {
    type: DataTypes.STRING(200),
    allowNull: false,
    validate: {
      len: [1, 200]
    }
  },
  
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  type: {
    type: DataTypes.ENUM('planting', 'irrigation', 'fertilization', 'harvesting', 'maintenance', 'inspection', 'other'),
    allowNull: false
  },
  
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    allowNull: false,
    defaultValue: 'medium'
  },
  
  status: {
    type: DataTypes.ENUM('pending', 'in_progress', 'completed', 'cancelled'),
    allowNull: false,
    defaultValue: 'pending'
  },
  
  field_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'fields',
      key: 'id'
    }
  },
  
  crop_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'crops',
      key: 'id'
    }
  },
  
  assigned_to: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  
  start_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  due_date: {
    type: DataTypes.DATE,
    allowNull: false
  },
  
  estimated_duration: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '预计耗时（小时）'
  },
  
  actual_duration: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '实际耗时（小时）'
  },
  
  started_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  completed_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  cancelled_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  completion_notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  cancellation_reason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  progress_records: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: '进度记录'
  },
  
  requirements: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {},
    comment: '任务要求和规格'
  },
  
  resources_needed: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: '所需资源'
  },
  
  attachments: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: [],
    comment: '附件文件'
  },
  
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  sequelize,
  modelName: 'Task',
  tableName: 'tasks',
  timestamps: true,
  
  // 索引
  indexes: [
    {
      fields: ['status']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['type']
    },
    {
      fields: ['assigned_to']
    },
    {
      fields: ['field_id']
    },
    {
      fields: ['crop_id']
    },
    {
      fields: ['due_date']
    },
    {
      fields: ['start_date']
    },
    {
      fields: ['completed_at']
    },
    {
      fields: ['status', 'due_date']
    },
    {
      fields: ['assigned_to', 'status']
    }
  ],
  
  // 作用域
  scopes: {
    pending: {
      where: { status: 'pending' }
    },
    
    inProgress: {
      where: { status: 'in_progress' }
    },
    
    completed: {
      where: { status: 'completed' }
    },
    
    overdue: {
      where: {
        status: ['pending', 'in_progress'],
        due_date: { [sequelize.Sequelize.Op.lt]: new Date() }
      }
    },
    
    dueSoon: (days = 3) => ({
      where: {
        status: ['pending', 'in_progress'],
        due_date: {
          [sequelize.Sequelize.Op.between]: [
            new Date(),
            new Date(Date.now() + days * 24 * 60 * 60 * 1000)
          ]
        }
      }
    }),
    
    byPriority: (priority) => ({
      where: { priority }
    }),
    
    byType: (type) => ({
      where: { type }
    }),
    
    byAssignee: (assigneeId) => ({
      where: { assigned_to: assigneeId }
    }),
    
    byField: (fieldId) => ({
      where: { field_id: fieldId }
    }),
    
    today: {
      where: {
        due_date: {
          [sequelize.Sequelize.Op.between]: [
            new Date(new Date().setHours(0, 0, 0, 0)),
            new Date(new Date().setHours(23, 59, 59, 999))
          ]
        }
      }
    }
  }
});

module.exports = Task;
