const { DataTypes, Model } = require('sequelize');
const { sequelize } = require('../config/database');

class Alert extends Model {
  // 实例方法：标记为已读
  async markAsRead(userId) {
    this.is_read = true;
    this.read_at = new Date();
    this.read_by = userId;
    await this.save();
    return this;
  }

  // 实例方法：解决预警
  async resolve(resolution, userId) {
    this.status = 'resolved';
    this.resolution = resolution;
    this.resolved_at = new Date();
    this.resolved_by = userId;
    
    // 同时标记为已读
    if (!this.is_read) {
      this.is_read = true;
      this.read_at = new Date();
      this.read_by = userId;
    }
    
    await this.save();
    return this;
  }

  // 实例方法：重新激活预警
  async reactivate() {
    this.status = 'active';
    this.resolved_at = null;
    this.resolved_by = null;
    this.resolution = null;
    await this.save();
    return this;
  }

  // 实例方法：检查是否为重复预警
  async isDuplicate() {
    const duplicateWindow = 5 * 60 * 1000; // 5分钟内的重复预警
    const since = new Date(Date.now() - duplicateWindow);
    
    const duplicate = await Alert.findOne({
      where: {
        fingerprint: this.fingerprint,
        created_at: { [sequelize.Sequelize.Op.gte]: since },
        id: { [sequelize.Sequelize.Op.ne]: this.id }
      }
    });
    
    return !!duplicate;
  }

  // 实例方法：获取相关预警
  async getRelatedAlerts() {
    const whereClause = {
      id: { [sequelize.Sequelize.Op.ne]: this.id }
    };
    
    if (this.device_id) {
      whereClause.device_id = this.device_id;
    } else if (this.field_id) {
      whereClause.field_id = this.field_id;
    } else {
      whereClause.type = this.type;
    }
    
    return await Alert.findAll({
      where: whereClause,
      order: [['created_at', 'DESC']],
      limit: 10,
      include: [
        { association: 'device', attributes: ['id', 'name', 'type'] },
        { association: 'field', attributes: ['id', 'name'] },
        { association: 'creator', attributes: ['id', 'name', 'username'] }
      ]
    });
  }

  // 虚拟属性：是否活跃
  get isActive() {
    return this.status === 'active';
  }

  // 虚拟属性：是否已解决
  get isResolved() {
    return this.status === 'resolved';
  }

  // 虚拟属性：是否严重
  get isCritical() {
    return this.level === 'critical';
  }

  // 虚拟属性：预警年龄（毫秒）
  get age() {
    return Date.now() - this.created_at.getTime();
  }

  // 虚拟属性：解决时间（毫秒）
  get resolutionTime() {
    if (this.resolved_at && this.created_at) {
      return this.resolved_at.getTime() - this.created_at.getTime();
    }
    return null;
  }

  // 静态方法：查找活跃预警
  static findActive() {
    return this.findAll({
      where: { status: 'active' },
      order: [['created_at', 'DESC']]
    });
  }

  // 静态方法：查找未读预警
  static findUnread() {
    return this.findAll({
      where: { is_read: false },
      order: [['created_at', 'DESC']]
    });
  }

  // 静态方法：按级别查找
  static findByLevel(level) {
    return this.findAll({
      where: { level },
      order: [['created_at', 'DESC']]
    });
  }

  // 静态方法：按类型查找
  static findByType(type) {
    return this.findAll({
      where: { type },
      order: [['created_at', 'DESC']]
    });
  }

  // 静态方法：按设备查找
  static findByDevice(deviceId) {
    return this.findAll({
      where: { device_id: deviceId },
      order: [['created_at', 'DESC']]
    });
  }

  // 静态方法：按地块查找
  static findByField(fieldId) {
    return this.findAll({
      where: { field_id: fieldId },
      order: [['created_at', 'DESC']]
    });
  }

  // 静态方法：查找最近预警
  static findRecent(hours = 24) {
    const since = new Date(Date.now() - hours * 60 * 60 * 1000);
    return this.findAll({
      where: {
        created_at: { [sequelize.Sequelize.Op.gte]: since }
      },
      order: [['created_at', 'DESC']]
    });
  }

  // 静态方法：获取预警统计
  static async getStats() {
    const total = await this.count();
    const active = await this.count({ where: { status: 'active' } });
    const resolved = await this.count({ where: { status: 'resolved' } });
    const unread = await this.count({ where: { is_read: false } });
    
    // 按级别统计
    const levelStats = await this.findAll({
      attributes: [
        'level',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['level'],
      raw: true
    });

    // 按类型统计
    const typeStats = await this.findAll({
      attributes: [
        'type',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['type'],
      raw: true
    });

    // 最近7天的预警趋势
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const recentTrend = await sequelize.query(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as count
      FROM alerts 
      WHERE created_at >= :sevenDaysAgo
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `, {
      replacements: { sevenDaysAgo },
      type: sequelize.QueryTypes.SELECT
    });

    return {
      total,
      active,
      resolved,
      unread,
      levelDistribution: levelStats,
      typeDistribution: typeStats,
      recentTrend
    };
  }
}

// 定义模型
Alert.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  
  title: {
    type: DataTypes.STRING(200),
    allowNull: false,
    validate: {
      len: [1, 200]
    }
  },
  
  message: {
    type: DataTypes.TEXT,
    allowNull: false,
    validate: {
      len: [1, 1000]
    }
  },
  
  type: {
    type: DataTypes.ENUM('device', 'sensor', 'weather', 'system', 'security', 'maintenance'),
    allowNull: false
  },
  
  level: {
    type: DataTypes.ENUM('critical', 'warning', 'info'),
    allowNull: false,
    defaultValue: 'info'
  },
  
  status: {
    type: DataTypes.ENUM('active', 'resolved', 'dismissed'),
    allowNull: false,
    defaultValue: 'active'
  },
  
  source: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  
  device_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'devices',
      key: 'id'
    }
  },
  
  field_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'fields',
      key: 'id'
    }
  },
  
  data: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {}
  },
  
  threshold_parameter: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  
  threshold_value: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  
  threshold_operator: {
    type: DataTypes.ENUM('>', '<', '>=', '<=', '==', '!='),
    allowNull: true,
    defaultValue: '>'
  },
  
  threshold_unit: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  
  is_read: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  
  read_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  read_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  
  resolution: {
    type: DataTypes.TEXT,
    allowNull: true,
    validate: {
      len: [0, 500]
    }
  },
  
  resolved_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  resolved_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  
  notification_sent: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  
  notification_sent_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  fingerprint: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  sequelize,
  modelName: 'Alert',
  tableName: 'alerts',
  timestamps: true,
  
  // 索引
  indexes: [
    {
      fields: ['type', 'level']
    },
    {
      fields: ['status', 'created_at']
    },
    {
      fields: ['device_id', 'created_at']
    },
    {
      fields: ['field_id', 'created_at']
    },
    {
      fields: ['is_read', 'created_at']
    },
    {
      fields: ['fingerprint', 'created_at']
    },
    {
      fields: ['level']
    },
    {
      fields: ['created_at']
    }
  ],
  
  // 钩子函数
  hooks: {
    beforeCreate: (alert) => {
      // 生成指纹用于去重
      if (!alert.fingerprint) {
        const components = [
          alert.type,
          alert.device_id?.toString() || '',
          alert.field_id?.toString() || '',
          alert.threshold_parameter || ''
        ];
        alert.fingerprint = components.join('|');
      }
    }
  },
  
  // 作用域
  scopes: {
    active: {
      where: { status: 'active' }
    },
    
    unread: {
      where: { is_read: false }
    },
    
    critical: {
      where: { level: 'critical' }
    },
    
    byLevel: (level) => ({
      where: { level }
    }),
    
    byType: (type) => ({
      where: { type }
    }),
    
    recent: (hours = 24) => ({
      where: {
        created_at: {
          [sequelize.Sequelize.Op.gte]: new Date(Date.now() - hours * 60 * 60 * 1000)
        }
      }
    })
  }
});

module.exports = Alert;
