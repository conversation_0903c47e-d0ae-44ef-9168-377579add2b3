const { DataTypes, Model } = require('sequelize');
const { sequelize } = require('../config/database');

class Field extends Model {
  // 实例方法：计算利用率
  getUtilizationRate() {
    if (!this.area || this.area === 0) return 0;
    return Math.round((this.used_area / this.area) * 100);
  }

  // 实例方法：检查是否可用
  isAvailable() {
    return this.status === 'active' && this.getUtilizationRate() < 100;
  }

  // 实例方法：获取地理位置信息
  getLocationInfo() {
    if (!this.coordinates) return null;
    
    try {
      const coords = JSON.parse(this.coordinates);
      return {
        type: 'Polygon',
        coordinates: coords,
        center: this.calculateCenter(coords)
      };
    } catch (error) {
      return null;
    }
  }

  // 实例方法：计算中心点
  calculateCenter(coordinates) {
    if (!coordinates || !coordinates[0]) return null;
    
    const points = coordinates[0];
    let totalLat = 0;
    let totalLng = 0;
    
    points.forEach(point => {
      totalLat += point[1];
      totalLng += point[0];
    });
    
    return [
      totalLng / points.length,
      totalLat / points.length
    ];
  }

  // 实例方法：更新使用面积
  async updateUsedArea(area) {
    if (area > this.area) {
      throw new Error('使用面积不能超过总面积');
    }
    
    this.used_area = area;
    await this.save();
  }

  // 静态方法：按状态查找
  static async findByStatus(status) {
    return await this.findAll({
      where: { status },
      order: [['created_at', 'DESC']]
    });
  }

  // 静态方法：按区域查找
  static async findByLocation(location) {
    return await this.findAll({
      where: {
        location: {
          [sequelize.Sequelize.Op.like]: `%${location}%`
        }
      }
    });
  }

  // 静态方法：获取统计信息
  static async getStats() {
    const total = await this.count();
    const active = await this.count({ where: { status: 'active' } });
    const inactive = await this.count({ where: { status: 'inactive' } });
    
    // 总面积统计
    const areaStats = await this.findAll({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('area')), 'total_area'],
        [sequelize.fn('SUM', sequelize.col('used_area')), 'used_area'],
        [sequelize.fn('AVG', sequelize.col('area')), 'avg_area']
      ],
      where: { status: 'active' },
      raw: true
    });

    // 按土壤类型统计
    const soilTypeStats = await this.findAll({
      attributes: [
        'soil_type',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
        [sequelize.fn('SUM', sequelize.col('area')), 'total_area']
      ],
      where: { 
        status: 'active',
        soil_type: { [sequelize.Sequelize.Op.ne]: null }
      },
      group: ['soil_type'],
      raw: true
    });

    // 按地理位置统计
    const locationStats = await this.findAll({
      attributes: [
        'location',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
        [sequelize.fn('SUM', sequelize.col('area')), 'total_area']
      ],
      where: { status: 'active' },
      group: ['location'],
      raw: true
    });

    const stats = areaStats[0] || {};
    const utilizationRate = stats.total_area > 0 
      ? Math.round((stats.used_area / stats.total_area) * 100) 
      : 0;

    return {
      total,
      active,
      inactive,
      totalArea: parseFloat(stats.total_area) || 0,
      usedArea: parseFloat(stats.used_area) || 0,
      avgArea: parseFloat(stats.avg_area) || 0,
      utilizationRate,
      soilTypeDistribution: soilTypeStats,
      locationDistribution: locationStats
    };
  }

  // 静态方法：搜索地块
  static async search(query) {
    return await this.findAll({
      where: {
        [sequelize.Sequelize.Op.or]: [
          { name: { [sequelize.Sequelize.Op.like]: `%${query}%` } },
          { code: { [sequelize.Sequelize.Op.like]: `%${query}%` } },
          { location: { [sequelize.Sequelize.Op.like]: `%${query}%` } },
          { description: { [sequelize.Sequelize.Op.like]: `%${query}%` } }
        ]
      },
      order: [['name', 'ASC']]
    });
  }
}

// 定义模型
Field.init({
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      len: [1, 100]
    }
  },
  
  code: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    validate: {
      len: [1, 50]
    }
  },
  
  location: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  
  area: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0.01
    }
  },
  
  used_area: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  
  soil_type: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  
  ph_level: {
    type: DataTypes.DECIMAL(3, 1),
    allowNull: true,
    validate: {
      min: 0,
      max: 14
    }
  },
  
  fertility_level: {
    type: DataTypes.ENUM('low', 'medium', 'high'),
    allowNull: true
  },
  
  irrigation_type: {
    type: DataTypes.ENUM('drip', 'sprinkler', 'flood', 'manual'),
    allowNull: true
  },
  
  coordinates: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'GeoJSON格式的地理坐标'
  },
  
  elevation: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: true,
    comment: '海拔高度(米)'
  },
  
  slope: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: '坡度(度)'
  },
  
  drainage: {
    type: DataTypes.ENUM('excellent', 'good', 'fair', 'poor'),
    allowNull: true
  },
  
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'maintenance'),
    allowNull: false,
    defaultValue: 'active'
  },
  
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  images: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '地块图片URL数组'
  },
  
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  
  updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  }
}, {
  sequelize,
  modelName: 'Field',
  tableName: 'fields',
  timestamps: true,
  
  // 索引
  indexes: [
    {
      unique: true,
      fields: ['code']
    },
    {
      fields: ['status']
    },
    {
      fields: ['location']
    },
    {
      fields: ['soil_type']
    },
    {
      fields: ['area']
    },
    {
      fields: ['created_at']
    }
  ],
  
  // 钩子函数
  hooks: {
    beforeCreate: async (field) => {
      // 验证代码唯一性
      const existingField = await Field.findOne({
        where: { code: field.code }
      });
      if (existingField) {
        throw new Error('地块代码已存在');
      }
    },
    
    beforeUpdate: async (field) => {
      // 验证代码唯一性
      if (field.changed('code')) {
        const existingField = await Field.findOne({
          where: {
            code: field.code,
            id: { [sequelize.Sequelize.Op.ne]: field.id }
          }
        });
        if (existingField) {
          throw new Error('地块代码已被其他地块使用');
        }
      }
      
      // 验证使用面积不超过总面积
      if (field.used_area > field.area) {
        throw new Error('使用面积不能超过总面积');
      }
    }
  },
  
  // 作用域
  scopes: {
    active: {
      where: { status: 'active' }
    },
    
    available: {
      where: {
        status: 'active',
        [sequelize.Sequelize.Op.and]: sequelize.literal('used_area < area')
      }
    },
    
    byLocation: (location) => ({
      where: {
        location: { [sequelize.Sequelize.Op.like]: `%${location}%` }
      }
    }),
    
    bySoilType: (soilType) => ({
      where: { soil_type: soilType }
    }),
    
    largeFields: {
      where: {
        area: { [sequelize.Sequelize.Op.gte]: 10 }
      }
    }
  }
});

module.exports = Field;
