const mysql = require('mysql2/promise');
const { Sequelize } = require('sequelize');
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  username: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'farm_management',
  dialect: 'mysql',
  timezone: '+08:00',
  
  // 连接池配置
  pool: {
    max: 20,          // 最大连接数
    min: 0,           // 最小连接数
    acquire: 30000,   // 获取连接超时时间
    idle: 10000       // 连接空闲时间
  },
  
  // 查询配置
  define: {
    timestamps: true,           // 自动添加createdAt和updatedAt
    underscored: true,         // 使用下划线命名
    freezeTableName: true,     // 禁用表名复数化
    charset: 'utf8mb4',        // 字符集
    collate: 'utf8mb4_unicode_ci'
  },
  
  // 日志配置
  logging: process.env.NODE_ENV === 'development' ? console.log : false,
  
  // 其他配置
  dialectOptions: {
    charset: 'utf8mb4',
    supportBigNumbers: true,
    bigNumberStrings: true,
    dateStrings: true,
    typeCast: true
  }
};

// 创建Sequelize实例
const sequelize = new Sequelize(dbConfig);

// 测试数据库连接
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    console.log('✅ MySQL数据库连接成功');
    return true;
  } catch (error) {
    console.error('❌ MySQL数据库连接失败:', error.message);
    return false;
  }
};

// 创建数据库（如果不存在）
const createDatabase = async () => {
  const connection = await mysql.createConnection({
    host: dbConfig.host,
    port: dbConfig.port,
    user: dbConfig.username,
    password: dbConfig.password
  });
  
  try {
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbConfig.database}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    console.log(`✅ 数据库 ${dbConfig.database} 创建成功或已存在`);
  } catch (error) {
    console.error('❌ 创建数据库失败:', error.message);
    throw error;
  } finally {
    await connection.end();
  }
};

// 同步数据库模型
const syncDatabase = async (force = false) => {
  try {
    if (force) {
      console.log('⚠️  强制同步数据库，将删除所有现有数据');
    }
    
    await sequelize.sync({ force });
    console.log('✅ 数据库模型同步成功');
  } catch (error) {
    console.error('❌ 数据库模型同步失败:', error.message);
    throw error;
  }
};

// 初始化数据库
const initDatabase = async () => {
  try {
    // 1. 创建数据库
    await createDatabase();
    
    // 2. 测试连接
    const connected = await testConnection();
    if (!connected) {
      throw new Error('数据库连接失败');
    }
    
    // 3. 同步模型
    await syncDatabase(process.env.DB_FORCE_SYNC === 'true');
    
    console.log('🎉 数据库初始化完成');
    return true;
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    throw error;
  }
};

// 关闭数据库连接
const closeDatabase = async () => {
  try {
    await sequelize.close();
    console.log('✅ 数据库连接已关闭');
  } catch (error) {
    console.error('❌ 关闭数据库连接失败:', error.message);
  }
};

// 执行原生SQL查询
const executeQuery = async (sql, replacements = {}) => {
  try {
    const [results, metadata] = await sequelize.query(sql, {
      replacements,
      type: Sequelize.QueryTypes.SELECT
    });
    return results;
  } catch (error) {
    console.error('❌ SQL查询执行失败:', error.message);
    throw error;
  }
};

// 事务处理
const transaction = async (callback) => {
  const t = await sequelize.transaction();
  try {
    const result = await callback(t);
    await t.commit();
    return result;
  } catch (error) {
    await t.rollback();
    throw error;
  }
};

// 数据库健康检查
const healthCheck = async () => {
  try {
    await sequelize.authenticate();
    const [results] = await sequelize.query('SELECT 1 as status');
    return {
      status: 'healthy',
      timestamp: new Date(),
      version: await sequelize.databaseVersion(),
      connectionCount: sequelize.connectionManager.pool.size
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      timestamp: new Date(),
      error: error.message
    };
  }
};

// 获取数据库统计信息
const getStats = async () => {
  try {
    const stats = await executeQuery(`
      SELECT 
        table_name,
        table_rows,
        data_length,
        index_length,
        (data_length + index_length) as total_size
      FROM information_schema.tables 
      WHERE table_schema = :database
    `, { database: dbConfig.database });
    
    return {
      tables: stats,
      totalSize: stats.reduce((sum, table) => sum + (table.total_size || 0), 0),
      totalRows: stats.reduce((sum, table) => sum + (table.table_rows || 0), 0)
    };
  } catch (error) {
    console.error('❌ 获取数据库统计信息失败:', error.message);
    return null;
  }
};

// 备份数据库
const backupDatabase = async (backupPath) => {
  const { exec } = require('child_process');
  const path = require('path');
  
  return new Promise((resolve, reject) => {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `farm_management_backup_${timestamp}.sql`;
    const fullPath = path.join(backupPath || './backups', filename);
    
    const command = `mysqldump -h ${dbConfig.host} -P ${dbConfig.port} -u ${dbConfig.username} -p${dbConfig.password} ${dbConfig.database} > ${fullPath}`;
    
    exec(command, (error, stdout, stderr) => {
      if (error) {
        reject(new Error(`备份失败: ${error.message}`));
      } else {
        resolve({
          filename,
          path: fullPath,
          timestamp: new Date(),
          size: require('fs').statSync(fullPath).size
        });
      }
    });
  });
};

module.exports = {
  sequelize,
  testConnection,
  createDatabase,
  syncDatabase,
  initDatabase,
  closeDatabase,
  executeQuery,
  transaction,
  healthCheck,
  getStats,
  backupDatabase,
  
  // Sequelize相关
  Sequelize,
  Op: Sequelize.Op,
  DataTypes: Sequelize.DataTypes
};
