@echo off
echo ========================================
echo 启动农场智慧管理系统 - 第三阶段
echo ========================================
echo.

echo 正在启动后端服务...
cd backend
start "后端服务" cmd /k "npm run dev"
cd ..

echo 等待后端服务启动...
timeout /t 3 /nobreak > nul

echo 正在启动前端应用...
cd frontend
start "前端应用" cmd /k "npm run dev"
cd ..

echo 等待前端应用启动...
timeout /t 3 /nobreak > nul

echo 正在启动数据大屏...
cd dashboard
start "数据大屏" cmd /k "npm run dev"
cd ..

echo.
echo ========================================
echo 所有服务启动完成！
echo ========================================
echo 后端API: http://localhost:5000
echo 前端应用: http://localhost:3000
echo 数据大屏: http://localhost:3001
echo ========================================
echo.
echo 按任意键退出...
pause > nul
