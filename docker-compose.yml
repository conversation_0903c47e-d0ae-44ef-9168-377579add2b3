version: '3.8'

services:
  # MongoDB 数据库
  mongodb:
    image: mongo:6.0
    container_name: farm-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: farmpassword
      MONGO_INITDB_DATABASE: farm_management
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./docker/mongodb/init:/docker-entrypoint-initdb.d
    networks:
      - farm-network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis 缓存 (可选)
  redis:
    image: redis:7-alpine
    container_name: farm-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - farm-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 农场管理系统后端
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: farm-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 5000
      MONGODB_URI: ***************************************************************************
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: your_production_jwt_secret_here
      JWT_EXPIRE: 7d
      CORS_ORIGIN: http://localhost:3000,http://localhost:3001
      UPLOAD_PATH: /app/uploads
      LOG_LEVEL: info
    ports:
      - "5000:5000"
    volumes:
      - uploads_data:/app/uploads
      - logs_data:/app/logs
      - backups_data:/app/backups
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - farm-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: farm-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - ./docker/ssl:/etc/nginx/ssl
      - uploads_data:/var/www/uploads
    depends_on:
      - backend
    networks:
      - farm-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MQTT Broker (用于IoT设备通信)
  mosquitto:
    image: eclipse-mosquitto:2
    container_name: farm-mosquitto
    restart: unless-stopped
    ports:
      - "1883:1883"
      - "9001:9001"
    volumes:
      - ./docker/mosquitto/mosquitto.conf:/mosquitto/config/mosquitto.conf
      - mosquitto_data:/mosquitto/data
      - mosquitto_logs:/mosquitto/log
    networks:
      - farm-network

  # 监控服务 (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: farm-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - farm-network

  # Grafana 监控面板 (可选)
  grafana:
    image: grafana/grafana:latest
    container_name: farm-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - farm-network

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  uploads_data:
    driver: local
  logs_data:
    driver: local
  backups_data:
    driver: local
  mosquitto_data:
    driver: local
  mosquitto_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  farm-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
