# 多阶段构建 - 前端构建阶段
FROM node:18-alpine AS frontend-builder

# 设置工作目录
WORKDIR /app/frontend

# 复制前端package文件
COPY frontend/package*.json ./

# 安装前端依赖
RUN npm ci --only=production

# 复制前端源码
COPY frontend/ ./

# 构建前端应用
RUN npm run build

# 多阶段构建 - 数据大屏构建阶段
FROM node:18-alpine AS dashboard-builder

# 设置工作目录
WORKDIR /app/dashboard

# 复制数据大屏package文件
COPY dashboard/package*.json ./

# 安装数据大屏依赖
RUN npm ci --only=production

# 复制数据大屏源码
COPY dashboard/ ./

# 构建数据大屏应用
RUN npm run build

# 生产阶段
FROM node:18-alpine AS production

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=5000

# 创建应用用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S farmapp -u 1001

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apk add --no-cache \
    dumb-init \
    curl \
    && rm -rf /var/cache/apk/*

# 复制后端package文件
COPY backend/package*.json ./

# 安装后端生产依赖
RUN npm ci --only=production && npm cache clean --force

# 复制后端源码
COPY backend/ ./

# 复制构建好的前端文件
COPY --from=frontend-builder /app/frontend/dist ./public/frontend

# 复制构建好的数据大屏文件
COPY --from=dashboard-builder /app/dashboard/dist ./public/dashboard

# 创建必要的目录
RUN mkdir -p uploads logs backups && \
    chown -R farmapp:nodejs /app

# 切换到应用用户
USER farmapp

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:$PORT/health || exit 1

# 暴露端口
EXPOSE $PORT

# 使用dumb-init作为PID 1
ENTRYPOINT ["dumb-init", "--"]

# 启动应用
CMD ["node", "src/app.js"]
