#!/bin/bash

echo "========================================"
echo "安装移动端开发环境和依赖"
echo "========================================"
echo

echo "检查Node.js环境..."
if ! command -v node &> /dev/null; then
    echo "❌ 未检测到Node.js，请先安装Node.js"
    echo "下载地址: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js环境检查通过"
echo

echo "检查是否已安装Expo CLI..."
if ! command -v expo &> /dev/null; then
    echo "正在安装Expo CLI..."
    npm install -g @expo/cli
    if [ $? -ne 0 ]; then
        echo "❌ Expo CLI安装失败"
        exit 1
    fi
    echo "✅ Expo CLI安装成功"
else
    echo "✅ Expo CLI已安装"
fi
echo

echo "进入移动端目录..."
cd mobile
if [ $? -ne 0 ]; then
    echo "❌ 移动端目录不存在"
    exit 1
fi

echo "安装移动端依赖包..."
npm install
if [ $? -ne 0 ]; then
    echo "❌ 依赖包安装失败"
    exit 1
fi

echo "✅ 移动端依赖包安装成功"
echo

echo "安装视频处理依赖..."
npm install fluent-ffmpeg
if [ $? -ne 0 ]; then
    echo "⚠️ 视频处理依赖安装失败，视频功能可能受限"
fi

echo
echo "========================================"
echo "移动端环境安装完成！"
echo "========================================"
echo
echo "📱 支持的平台:"
echo "- Android (需要Android Studio)"
echo "- iOS (需要Xcode，仅限macOS)"
echo "- Web浏览器"
echo
echo "🚀 启动命令:"
echo "- 启动开发服务器: npm start"
echo "- 启动Android模拟器: npm run android"
echo "- 启动iOS模拟器: npm run ios"
echo "- 启动Web版本: npm run web"
echo
echo "📋 开发工具推荐:"
echo "- Expo Go App (手机扫码调试)"
echo "- Android Studio (Android开发)"
echo "- Xcode (iOS开发，仅限macOS)"
echo
echo "========================================"
echo
