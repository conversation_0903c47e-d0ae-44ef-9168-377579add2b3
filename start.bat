@echo off
chcp 65001
echo ========================================
echo 农场智慧管理系统启动脚本
echo ========================================
echo.

echo 正在检查环境...

:: 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Node.js，请先安装Node.js 16.0.0或更高版本
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js 已安装

:: 检查MongoDB
mongod --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  未检测到MongoDB，请确保MongoDB已安装并启动
    echo 下载地址: https://www.mongodb.com/try/download/community
)

echo.
echo 选择启动模式:
echo 1. 完整启动 (后端 + 前端 + 大屏)
echo 2. 仅启动后端
echo 3. 仅启动前端
echo 4. 仅启动大屏
echo 5. 安装依赖
echo 6. 初始化数据库
echo 7. 退出
echo.

set /p choice=请输入选择 (1-7):

if "%choice%"=="1" goto start_all
if "%choice%"=="2" goto start_backend
if "%choice%"=="3" goto start_frontend
if "%choice%"=="4" goto start_dashboard
if "%choice%"=="5" goto install_deps
if "%choice%"=="6" goto init_db
if "%choice%"=="7" goto exit

echo 无效选择，请重新运行脚本
pause
exit /b 1

:install_deps
echo.
echo 正在安装依赖...
echo.

echo 安装后端依赖...
cd backend
call npm install
if %errorlevel% neq 0 (
    echo ❌ 后端依赖安装失败
    pause
    exit /b 1
)
cd ..

echo 安装前端依赖...
cd frontend
call npm install
if %errorlevel% neq 0 (
    echo ❌ 前端依赖安装失败
    pause
    exit /b 1
)
cd ..

echo 安装大屏依赖...
cd dashboard
call npm install
if %errorlevel% neq 0 (
    echo ❌ 大屏依赖安装失败
    pause
    exit /b 1
)
cd ..

echo.
echo ✅ 所有依赖安装完成！
pause
goto start

:init_db
echo.
echo 正在初始化数据库...
echo.

cd backend
call npm run init-db
if %errorlevel% neq 0 (
    echo ❌ 数据库初始化失败
    pause
    exit /b 1
)
cd ..

echo.
echo ✅ 数据库初始化完成！
echo.
echo 📋 默认账号信息:
echo 管理员: admin / 123456
echo 经理: manager / 123456
echo 工人: worker / 123456
echo.
pause
goto start

:start_all
echo.
echo 正在启动完整系统...
echo.

:: 复制环境配置文件
if not exist "backend\.env" (
    copy "backend\.env.example" "backend\.env"
    echo ✅ 已创建后端环境配置文件，请根据需要修改 backend\.env
)

:: 启动后端
echo 启动后端服务...
start "农场管理系统-后端" cmd /k "cd backend && npm run dev"

:: 等待后端启动
timeout /t 3 /nobreak >nul

:: 启动前端
echo 启动前端服务...
start "农场管理系统-前端" cmd /k "cd frontend && npm run dev"

:: 启动大屏
echo 启动数据大屏...
start "农场管理系统-大屏" cmd /k "cd dashboard && npm run dev"

echo.
echo ✅ 系统启动完成！
echo.
echo 📱 前端地址: http://localhost:3000
echo 🖥️  大屏地址: http://localhost:3001
echo 🔧 后端API: http://localhost:5000
echo.
echo 默认管理员账号:
echo 用户名: admin
echo 密码: 123456
echo.
pause
goto exit

:start_backend
echo.
echo 正在启动后端服务...

if not exist "backend\.env" (
    copy "backend\.env.example" "backend\.env"
    echo ✅ 已创建后端环境配置文件
)

cd backend
call npm run dev
cd ..
goto exit

:start_frontend
echo.
echo 正在启动前端服务...
cd frontend
call npm run dev
cd ..
goto exit

:start_dashboard
echo.
echo 正在启动数据大屏...
cd dashboard
call npm run dev
cd ..
goto exit

:exit
echo.
echo 感谢使用农场智慧管理系统！
pause
