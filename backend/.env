# 应用配置
NODE_ENV=development
PORT=5000
APP_NAME=农场智慧管理系统
APP_VERSION=1.0.0

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/farm_management
MONGODB_OPTIONS={}

# JWT配置
JWT_SECRET=your_jwt_secret_key_here_please_change_in_production
JWT_EXPIRE=7d
JWT_REFRESH_SECRET=your_jwt_refresh_secret_key_here
JWT_REFRESH_EXPIRE=30d

# 密码加密
BCRYPT_ROUNDS=12

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/jpg,image/png,image/gif,application/pdf

# 天气API配置
WEATHER_API_KEY=your_openweathermap_api_key_here
WEATHER_API_URL=https://api.openweathermap.org/data/2.5

# 邮件配置
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_SECURE=false
MAIL_USER=<EMAIL>
MAIL_PASS=your_email_password
MAIL_FROM=农场管理系统 <<EMAIL>>

# Redis配置（可选，用于缓存和会话）
REDIS_HOST=localhost
REDIS_PORT=6380
REDIS_PASSWORD=123456
REDIS_DB=2

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# CORS配置
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_CREDENTIALS=true

# 安全配置
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# 开发配置
DEBUG=true
ENABLE_SWAGGER=true
