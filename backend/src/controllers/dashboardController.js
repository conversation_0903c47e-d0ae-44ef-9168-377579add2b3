const Field = require('../models/Field');
const Crop = require('../models/Crop');
const FarmingOperation = require('../models/FarmingOperation');
const Harvest = require('../models/Harvest');
const Material = require('../models/Material');
const Weather = require('../models/Weather');
const SensorData = require('../models/SensorData');
const moment = require('moment');

class DashboardController {
  // 获取仪表盘概览数据
  async getOverview(req, res) {
    try {
      const currentYear = new Date().getFullYear();
      const currentMonth = new Date().getMonth() + 1;

      // 基础统计
      const [
        totalFields,
        totalCrops,
        activeCrops,
        totalOperations,
        totalHarvests,
        totalMaterials
      ] = await Promise.all([
        Field.countDocuments({ status: 'active' }),
        Crop.countDocuments(),
        Crop.countDocuments({ status: 'active' }),
        FarmingOperation.countDocuments(),
        Harvest.countDocuments(),
        Material.countDocuments()
      ]);

      // 本月统计
      const monthStart = moment().startOf('month').toDate();
      const monthEnd = moment().endOf('month').toDate();

      const [
        monthOperations,
        monthHarvests,
        monthCosts
      ] = await Promise.all([
        FarmingOperation.countDocuments({
          operationDate: { $gte: monthStart, $lte: monthEnd }
        }),
        Harvest.countDocuments({
          harvestDate: { $gte: monthStart, $lte: monthEnd }
        }),
        FarmingOperation.aggregate([
          {
            $match: {
              operationDate: { $gte: monthStart, $lte: monthEnd }
            }
          },
          {
            $group: {
              _id: null,
              totalCost: { $sum: '$cost' }
            }
          }
        ])
      ]);

      // 地块面积统计
      const fieldAreaStats = await Field.aggregate([
        {
          $group: {
            _id: null,
            totalArea: { $sum: '$area' },
            activeArea: {
              $sum: { $cond: [{ $eq: ['$status', 'active'] }, '$area', 0] }
            }
          }
        }
      ]);

      // 作物类别统计
      const cropCategoryStats = await Crop.aggregate([
        {
          $group: {
            _id: '$category',
            count: { $sum: 1 },
            area: { $sum: '$plantingArea' }
          }
        },
        { $sort: { count: -1 } }
      ]);

      // 本年收获统计
      const yearStart = moment().startOf('year').toDate();
      const yearEnd = moment().endOf('year').toDate();

      const harvestStats = await Harvest.aggregate([
        {
          $match: {
            harvestDate: { $gte: yearStart, $lte: yearEnd }
          }
        },
        {
          $group: {
            _id: null,
            totalYield: { $sum: '$actualYield' },
            totalRevenue: { $sum: '$revenue' },
            avgQuality: { $avg: '$qualityGrade' }
          }
        }
      ]);

      res.json({
        success: true,
        data: {
          overview: {
            totalFields,
            totalCrops,
            activeCrops,
            totalOperations,
            totalHarvests,
            totalMaterials,
            totalArea: fieldAreaStats[0]?.totalArea || 0,
            activeArea: fieldAreaStats[0]?.activeArea || 0
          },
          monthly: {
            operations: monthOperations,
            harvests: monthHarvests,
            costs: monthCosts[0]?.totalCost || 0
          },
          yearly: {
            totalYield: harvestStats[0]?.totalYield || 0,
            totalRevenue: harvestStats[0]?.totalRevenue || 0,
            avgQuality: harvestStats[0]?.avgQuality || 0
          },
          cropCategories: cropCategoryStats
        }
      });
    } catch (error) {
      console.error('获取仪表盘概览数据错误:', error);
      res.status(500).json({
        success: false,
        message: '获取仪表盘数据失败',
        error: error.message
      });
    }
  }

  // 获取趋势数据
  async getTrends(req, res) {
    try {
      const { period = '30d' } = req.query;
      
      let startDate, groupFormat;
      switch (period) {
        case '7d':
          startDate = moment().subtract(7, 'days').toDate();
          groupFormat = '%Y-%m-%d';
          break;
        case '30d':
          startDate = moment().subtract(30, 'days').toDate();
          groupFormat = '%Y-%m-%d';
          break;
        case '12m':
          startDate = moment().subtract(12, 'months').toDate();
          groupFormat = '%Y-%m';
          break;
        default:
          startDate = moment().subtract(30, 'days').toDate();
          groupFormat = '%Y-%m-%d';
      }

      // 农事操作趋势
      const operationTrends = await FarmingOperation.aggregate([
        {
          $match: {
            operationDate: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: {
              date: { $dateToString: { format: groupFormat, date: '$operationDate' } },
              type: '$type'
            },
            count: { $sum: 1 },
            cost: { $sum: '$cost' }
          }
        },
        { $sort: { '_id.date': 1 } }
      ]);

      // 收获趋势
      const harvestTrends = await Harvest.aggregate([
        {
          $match: {
            harvestDate: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: { $dateToString: { format: groupFormat, date: '$harvestDate' } },
            yield: { $sum: '$actualYield' },
            revenue: { $sum: '$revenue' },
            count: { $sum: 1 }
          }
        },
        { $sort: { '_id': 1 } }
      ]);

      // 天气趋势
      const weatherTrends = await Weather.aggregate([
        {
          $match: {
            date: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: { $dateToString: { format: groupFormat, date: '$date' } },
            avgTemp: { $avg: '$temperature' },
            avgHumidity: { $avg: '$humidity' },
            totalRainfall: { $sum: '$rainfall' }
          }
        },
        { $sort: { '_id': 1 } }
      ]);

      res.json({
        success: true,
        data: {
          operations: operationTrends,
          harvests: harvestTrends,
          weather: weatherTrends
        }
      });
    } catch (error) {
      console.error('获取趋势数据错误:', error);
      res.status(500).json({
        success: false,
        message: '获取趋势数据失败',
        error: error.message
      });
    }
  }

  // 获取实时数据
  async getRealTimeData(req, res) {
    try {
      const now = new Date();
      const oneHourAgo = moment().subtract(1, 'hour').toDate();

      // 最新传感器数据
      const latestSensorData = await SensorData.find({
        timestamp: { $gte: oneHourAgo }
      })
      .sort({ timestamp: -1 })
      .limit(50)
      .populate('device', 'name type location');

      // 今日操作
      const todayStart = moment().startOf('day').toDate();
      const todayEnd = moment().endOf('day').toDate();

      const todayOperations = await FarmingOperation.find({
        operationDate: { $gte: todayStart, $lte: todayEnd }
      })
      .populate('crop', 'name variety')
      .populate('operator', 'name')
      .sort({ operationDate: -1 })
      .limit(10);

      // 最新天气数据
      const latestWeather = await Weather.findOne()
        .sort({ date: -1, createdAt: -1 });

      // 设备状态统计
      const deviceStats = await SensorData.aggregate([
        {
          $match: {
            timestamp: { $gte: oneHourAgo }
          }
        },
        {
          $group: {
            _id: '$device',
            lastUpdate: { $max: '$timestamp' },
            dataCount: { $sum: 1 }
          }
        },
        {
          $lookup: {
            from: 'devices',
            localField: '_id',
            foreignField: '_id',
            as: 'deviceInfo'
          }
        }
      ]);

      // 预警信息
      const alerts = [];
      
      // 检查设备离线
      const offlineDevices = deviceStats.filter(device => {
        const lastUpdate = moment(device.lastUpdate);
        return moment().diff(lastUpdate, 'minutes') > 30;
      });

      offlineDevices.forEach(device => {
        alerts.push({
          type: 'warning',
          title: '设备离线',
          message: `设备 ${device.deviceInfo[0]?.name} 已离线超过30分钟`,
          timestamp: new Date()
        });
      });

      // 检查异常数据
      const recentSensorData = await SensorData.find({
        timestamp: { $gte: oneHourAgo }
      }).sort({ timestamp: -1 });

      recentSensorData.forEach(data => {
        if (data.temperature && (data.temperature > 40 || data.temperature < -10)) {
          alerts.push({
            type: 'error',
            title: '温度异常',
            message: `检测到异常温度: ${data.temperature}°C`,
            timestamp: data.timestamp
          });
        }
        
        if (data.soilMoisture && data.soilMoisture < 20) {
          alerts.push({
            type: 'warning',
            title: '土壤干燥',
            message: `土壤湿度过低: ${data.soilMoisture}%`,
            timestamp: data.timestamp
          });
        }
      });

      res.json({
        success: true,
        data: {
          sensorData: latestSensorData,
          todayOperations,
          weather: latestWeather,
          deviceStats: deviceStats.length,
          alerts: alerts.slice(0, 10) // 最多返回10条预警
        }
      });
    } catch (error) {
      console.error('获取实时数据错误:', error);
      res.status(500).json({
        success: false,
        message: '获取实时数据失败',
        error: error.message
      });
    }
  }

  // 获取地块分布数据
  async getFieldDistribution(req, res) {
    try {
      // 按状态分布
      const statusDistribution = await Field.aggregate([
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
            area: { $sum: '$area' }
          }
        }
      ]);

      // 按土壤类型分布
      const soilTypeDistribution = await Field.aggregate([
        {
          $group: {
            _id: '$soilType',
            count: { $sum: 1 },
            area: { $sum: '$area' }
          }
        }
      ]);

      // 按灌溉方式分布
      const irrigationDistribution = await Field.aggregate([
        {
          $group: {
            _id: '$irrigation',
            count: { $sum: 1 },
            area: { $sum: '$area' }
          }
        }
      ]);

      // 地块利用率
      const utilizationStats = await Field.aggregate([
        {
          $lookup: {
            from: 'crops',
            localField: '_id',
            foreignField: 'field',
            as: 'crops'
          }
        },
        {
          $addFields: {
            activeCrops: {
              $filter: {
                input: '$crops',
                cond: { $eq: ['$$this.status', 'active'] }
              }
            }
          }
        },
        {
          $addFields: {
            usedArea: { $sum: '$activeCrops.plantingArea' },
            utilizationRate: {
              $cond: [
                { $gt: ['$area', 0] },
                { $divide: [{ $sum: '$activeCrops.plantingArea' }, '$area'] },
                0
              ]
            }
          }
        },
        {
          $group: {
            _id: null,
            totalArea: { $sum: '$area' },
            usedArea: { $sum: '$usedArea' },
            avgUtilization: { $avg: '$utilizationRate' }
          }
        }
      ]);

      res.json({
        success: true,
        data: {
          statusDistribution,
          soilTypeDistribution,
          irrigationDistribution,
          utilization: utilizationStats[0] || {
            totalArea: 0,
            usedArea: 0,
            avgUtilization: 0
          }
        }
      });
    } catch (error) {
      console.error('获取地块分布数据错误:', error);
      res.status(500).json({
        success: false,
        message: '获取地块分布数据失败',
        error: error.message
      });
    }
  }
}

module.exports = new DashboardController();
