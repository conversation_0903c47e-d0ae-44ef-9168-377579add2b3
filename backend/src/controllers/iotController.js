const Device = require('../models/Device');
const SensorData = require('../models/SensorData');
const iotService = require('../services/iotService');
const { validationResult } = require('express-validator');
const moment = require('moment');

class IoTController {
  // 获取设备列表
  async getDevices(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        });
      }

      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      // 构建查询条件
      const query = {};
      
      if (req.query.type) {
        query.type = req.query.type;
      }
      
      if (req.query.status) {
        query.status = req.query.status;
      }
      
      if (req.query.field) {
        query.field = req.query.field;
      }
      
      if (req.query.search) {
        query.$or = [
          { name: { $regex: req.query.search, $options: 'i' } },
          { deviceId: { $regex: req.query.search, $options: 'i' } },
          { description: { $regex: req.query.search, $options: 'i' } }
        ];
      }

      const devices = await Device.find(query)
        .populate('field', 'name code')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await Device.countDocuments(query);

      // 添加设备在线状态
      const devicesWithStatus = await Promise.all(
        devices.map(async (device) => {
          const deviceObj = device.toObject();
          deviceObj.isOnline = await this.checkDeviceOnline(device.deviceId);
          deviceObj.lastDataTime = await this.getLastDataTime(device._id);
          return deviceObj;
        })
      );

      res.json({
        success: true,
        data: {
          devices: devicesWithStatus,
          pagination: {
            current: page,
            pageSize: limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });
    } catch (error) {
      console.error('获取设备列表错误:', error);
      res.status(500).json({
        success: false,
        message: '获取设备列表失败',
        error: error.message
      });
    }
  }

  // 获取设备详情
  async getDeviceById(req, res) {
    try {
      const device = await Device.findById(req.params.id)
        .populate('field', 'name code area location');

      if (!device) {
        return res.status(404).json({
          success: false,
          message: '设备不存在'
        });
      }

      const deviceObj = device.toObject();
      deviceObj.isOnline = await this.checkDeviceOnline(device.deviceId);
      deviceObj.lastDataTime = await this.getLastDataTime(device._id);

      // 获取最近的传感器数据
      const recentData = await SensorData.find({ device: device._id })
        .sort({ timestamp: -1 })
        .limit(10);

      deviceObj.recentData = recentData;

      res.json({
        success: true,
        data: deviceObj
      });
    } catch (error) {
      console.error('获取设备详情错误:', error);
      res.status(500).json({
        success: false,
        message: '获取设备详情失败',
        error: error.message
      });
    }
  }

  // 创建设备
  async createDevice(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '数据验证失败',
          errors: errors.array()
        });
      }

      // 检查设备ID是否已存在
      const existingDevice = await Device.findOne({ deviceId: req.body.deviceId });
      if (existingDevice) {
        return res.status(400).json({
          success: false,
          message: '设备ID已存在'
        });
      }

      const device = new Device(req.body);
      await device.save();

      // 注册到IoT服务
      try {
        await iotService.registerDevice(device);
      } catch (iotError) {
        console.warn('IoT服务注册设备失败:', iotError.message);
      }

      await device.populate('field', 'name code');

      res.status(201).json({
        success: true,
        message: '设备创建成功',
        data: device
      });
    } catch (error) {
      console.error('创建设备错误:', error);
      res.status(500).json({
        success: false,
        message: '创建设备失败',
        error: error.message
      });
    }
  }

  // 更新设备
  async updateDevice(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '数据验证失败',
          errors: errors.array()
        });
      }

      const deviceId = req.params.id;

      // 检查设备是否存在
      const existingDevice = await Device.findById(deviceId);
      if (!existingDevice) {
        return res.status(404).json({
          success: false,
          message: '设备不存在'
        });
      }

      // 如果更新设备ID，检查是否与其他设备冲突
      if (req.body.deviceId && req.body.deviceId !== existingDevice.deviceId) {
        const duplicateDevice = await Device.findOne({
          deviceId: req.body.deviceId,
          _id: { $ne: deviceId }
        });

        if (duplicateDevice) {
          return res.status(400).json({
            success: false,
            message: '设备ID已存在'
          });
        }
      }

      const device = await Device.findByIdAndUpdate(
        deviceId,
        { ...req.body, updatedAt: new Date() },
        { new: true, runValidators: true }
      ).populate('field', 'name code');

      // 更新IoT服务中的设备信息
      try {
        await iotService.updateDevice(device);
      } catch (iotError) {
        console.warn('IoT服务更新设备失败:', iotError.message);
      }

      res.json({
        success: true,
        message: '设备更新成功',
        data: device
      });
    } catch (error) {
      console.error('更新设备错误:', error);
      res.status(500).json({
        success: false,
        message: '更新设备失败',
        error: error.message
      });
    }
  }

  // 删除设备
  async deleteDevice(req, res) {
    try {
      const deviceId = req.params.id;

      // 检查设备是否存在
      const device = await Device.findById(deviceId);
      if (!device) {
        return res.status(404).json({
          success: false,
          message: '设备不存在'
        });
      }

      // 检查是否有关联的传感器数据
      const dataCount = await SensorData.countDocuments({ device: deviceId });
      if (dataCount > 0) {
        return res.status(400).json({
          success: false,
          message: '该设备下还有传感器数据，无法删除'
        });
      }

      // 从IoT服务中注销设备
      try {
        await iotService.unregisterDevice(device.deviceId);
      } catch (iotError) {
        console.warn('IoT服务注销设备失败:', iotError.message);
      }

      await Device.findByIdAndDelete(deviceId);

      res.json({
        success: true,
        message: '设备删除成功'
      });
    } catch (error) {
      console.error('删除设备错误:', error);
      res.status(500).json({
        success: false,
        message: '删除设备失败',
        error: error.message
      });
    }
  }

  // 获取传感器数据
  async getSensorData(req, res) {
    try {
      const { deviceId } = req.params;
      const { startTime, endTime, dataType } = req.query;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 100;
      const skip = (page - 1) * limit;

      // 构建查询条件
      const query = { device: deviceId };
      
      if (startTime && endTime) {
        query.timestamp = {
          $gte: new Date(startTime),
          $lte: new Date(endTime)
        };
      }

      if (dataType) {
        query[`data.${dataType}`] = { $exists: true };
      }

      const sensorData = await SensorData.find(query)
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit);

      const total = await SensorData.countDocuments(query);

      res.json({
        success: true,
        data: {
          sensorData,
          pagination: {
            current: page,
            pageSize: limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });
    } catch (error) {
      console.error('获取传感器数据错误:', error);
      res.status(500).json({
        success: false,
        message: '获取传感器数据失败',
        error: error.message
      });
    }
  }

  // 设备控制
  async controlDevice(req, res) {
    try {
      const { deviceId } = req.params;
      const { command, parameters } = req.body;

      if (!command) {
        return res.status(400).json({
          success: false,
          message: '缺少控制命令'
        });
      }

      // 检查设备是否存在
      const device = await Device.findById(deviceId);
      if (!device) {
        return res.status(404).json({
          success: false,
          message: '设备不存在'
        });
      }

      // 发送控制命令
      const result = await iotService.sendCommand(device.deviceId, command, parameters);

      res.json({
        success: true,
        message: '控制命令发送成功',
        data: result
      });
    } catch (error) {
      console.error('设备控制错误:', error);
      res.status(500).json({
        success: false,
        message: '设备控制失败',
        error: error.message
      });
    }
  }

  // 获取设备统计
  async getDeviceStats(req, res) {
    try {
      const stats = await Device.aggregate([
        {
          $group: {
            _id: null,
            totalDevices: { $sum: 1 },
            activeDevices: {
              $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
            },
            inactiveDevices: {
              $sum: { $cond: [{ $eq: ['$status', 'inactive'] }, 1, 0] }
            },
            maintenanceDevices: {
              $sum: { $cond: [{ $eq: ['$status', 'maintenance'] }, 1, 0] }
            }
          }
        }
      ]);

      const typeStats = await Device.aggregate([
        {
          $group: {
            _id: '$type',
            count: { $sum: 1 }
          }
        },
        { $sort: { count: -1 } }
      ]);

      // 获取在线设备数量
      const onlineDevices = await this.getOnlineDeviceCount();

      res.json({
        success: true,
        data: {
          overview: {
            ...(stats[0] || {
              totalDevices: 0,
              activeDevices: 0,
              inactiveDevices: 0,
              maintenanceDevices: 0
            }),
            onlineDevices
          },
          typeStats
        }
      });
    } catch (error) {
      console.error('获取设备统计错误:', error);
      res.status(500).json({
        success: false,
        message: '获取设备统计失败',
        error: error.message
      });
    }
  }

  // 检查设备在线状态
  async checkDeviceOnline(deviceId) {
    try {
      return await iotService.isDeviceOnline(deviceId);
    } catch (error) {
      return false;
    }
  }

  // 获取设备最后数据时间
  async getLastDataTime(deviceId) {
    try {
      const lastData = await SensorData.findOne({ device: deviceId })
        .sort({ timestamp: -1 })
        .select('timestamp');
      
      return lastData ? lastData.timestamp : null;
    } catch (error) {
      return null;
    }
  }

  // 获取在线设备数量
  async getOnlineDeviceCount() {
    try {
      const devices = await Device.find({ status: 'active' }).select('deviceId');
      let onlineCount = 0;
      
      for (const device of devices) {
        if (await this.checkDeviceOnline(device.deviceId)) {
          onlineCount++;
        }
      }
      
      return onlineCount;
    } catch (error) {
      return 0;
    }
  }
}

module.exports = new IoTController();
