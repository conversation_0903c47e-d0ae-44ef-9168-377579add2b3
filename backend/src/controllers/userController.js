const User = require('../models/User');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { validationResult } = require('express-validator');
const { successResponse, errorResponse } = require('../utils/response');
const logger = require('../utils/logger');

// 获取用户列表
exports.getUsers = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      role,
      status,
      department
    } = req.query;

    // 构建查询条件
    const query = {};
    
    if (search) {
      query.$or = [
        { username: { $regex: search, $options: 'i' } },
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (role) query.role = role;
    if (status) query.status = status;
    if (department) query.department = { $regex: department, $options: 'i' };

    // 分页查询
    const skip = (page - 1) * limit;
    const users = await User.find(query)
      .select('-password')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await User.countDocuments(query);

    const pagination = {
      current: parseInt(page),
      pageSize: parseInt(limit),
      total,
      pages: Math.ceil(total / limit)
    };

    logger.info(`获取用户列表成功，共${total}条记录`);
    
    return successResponse(res, {
      users,
      pagination
    }, '获取用户列表成功');
  } catch (error) {
    logger.error('获取用户列表失败:', error);
    return errorResponse(res, '获取用户列表失败', 500);
  }
};

// 获取用户详情
exports.getUserById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const user = await User.findById(id).select('-password');
    if (!user) {
      return errorResponse(res, '用户不存在', 404);
    }

    logger.info(`获取用户详情成功: ${user.username}`);
    return successResponse(res, { user }, '获取用户详情成功');
  } catch (error) {
    logger.error('获取用户详情失败:', error);
    return errorResponse(res, '获取用户详情失败', 500);
  }
};

// 创建用户
exports.createUser = async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return errorResponse(res, '输入数据验证失败', 400, errors.array());
    }

    const {
      username,
      name,
      email,
      phone,
      password,
      role = 'worker',
      department,
      status = 'active'
    } = req.body;

    // 检查用户名是否已存在
    const existingUser = await User.findOne({
      $or: [{ username }, { email }]
    });
    
    if (existingUser) {
      return errorResponse(res, '用户名或邮箱已存在', 400);
    }

    // 加密密码
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // 创建用户
    const user = new User({
      username,
      name,
      email,
      phone,
      password: hashedPassword,
      role,
      department,
      status,
      createdBy: req.user.id
    });

    await user.save();

    // 返回用户信息（不包含密码）
    const userResponse = user.toObject();
    delete userResponse.password;

    logger.info(`创建用户成功: ${username}`);
    return successResponse(res, { user: userResponse }, '创建用户成功', 201);
  } catch (error) {
    logger.error('创建用户失败:', error);
    return errorResponse(res, '创建用户失败', 500);
  }
};

// 更新用户
exports.updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      email,
      phone,
      role,
      department,
      status
    } = req.body;

    // 检查用户是否存在
    const user = await User.findById(id);
    if (!user) {
      return errorResponse(res, '用户不存在', 404);
    }

    // 检查邮箱是否被其他用户使用
    if (email && email !== user.email) {
      const existingUser = await User.findOne({ email, _id: { $ne: id } });
      if (existingUser) {
        return errorResponse(res, '邮箱已被其他用户使用', 400);
      }
    }

    // 更新用户信息
    const updateData = {
      ...(name && { name }),
      ...(email && { email }),
      ...(phone && { phone }),
      ...(role && { role }),
      ...(department && { department }),
      ...(status && { status }),
      updatedAt: new Date(),
      updatedBy: req.user.id
    };

    const updatedUser = await User.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).select('-password');

    logger.info(`更新用户成功: ${updatedUser.username}`);
    return successResponse(res, { user: updatedUser }, '更新用户成功');
  } catch (error) {
    logger.error('更新用户失败:', error);
    return errorResponse(res, '更新用户失败', 500);
  }
};

// 删除用户
exports.deleteUser = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查用户是否存在
    const user = await User.findById(id);
    if (!user) {
      return errorResponse(res, '用户不存在', 404);
    }

    // 不能删除自己
    if (id === req.user.id) {
      return errorResponse(res, '不能删除自己的账户', 400);
    }

    // 软删除：标记为已删除
    await User.findByIdAndUpdate(id, {
      status: 'deleted',
      deletedAt: new Date(),
      deletedBy: req.user.id
    });

    logger.info(`删除用户成功: ${user.username}`);
    return successResponse(res, null, '删除用户成功');
  } catch (error) {
    logger.error('删除用户失败:', error);
    return errorResponse(res, '删除用户失败', 500);
  }
};

// 重置用户密码
exports.resetPassword = async (req, res) => {
  try {
    const { id } = req.params;
    const { newPassword } = req.body;

    if (!newPassword || newPassword.length < 6) {
      return errorResponse(res, '新密码长度不能少于6位', 400);
    }

    // 检查用户是否存在
    const user = await User.findById(id);
    if (!user) {
      return errorResponse(res, '用户不存在', 404);
    }

    // 加密新密码
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

    // 更新密码
    await User.findByIdAndUpdate(id, {
      password: hashedPassword,
      passwordChangedAt: new Date(),
      updatedBy: req.user.id
    });

    logger.info(`重置用户密码成功: ${user.username}`);
    return successResponse(res, null, '重置密码成功');
  } catch (error) {
    logger.error('重置密码失败:', error);
    return errorResponse(res, '重置密码失败', 500);
  }
};

// 切换用户状态
exports.toggleUserStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!['active', 'inactive'].includes(status)) {
      return errorResponse(res, '无效的状态值', 400);
    }

    // 检查用户是否存在
    const user = await User.findById(id);
    if (!user) {
      return errorResponse(res, '用户不存在', 404);
    }

    // 不能禁用自己
    if (id === req.user.id && status === 'inactive') {
      return errorResponse(res, '不能禁用自己的账户', 400);
    }

    // 更新状态
    const updatedUser = await User.findByIdAndUpdate(
      id,
      {
        status,
        updatedAt: new Date(),
        updatedBy: req.user.id
      },
      { new: true }
    ).select('-password');

    logger.info(`切换用户状态成功: ${user.username} -> ${status}`);
    return successResponse(res, { user: updatedUser }, '切换用户状态成功');
  } catch (error) {
    logger.error('切换用户状态失败:', error);
    return errorResponse(res, '切换用户状态失败', 500);
  }
};

// 获取用户统计信息
exports.getUserStats = async (req, res) => {
  try {
    const totalUsers = await User.countDocuments({ status: { $ne: 'deleted' } });
    const activeUsers = await User.countDocuments({ status: 'active' });
    const inactiveUsers = await User.countDocuments({ status: 'inactive' });
    
    // 按角色统计
    const roleStats = await User.aggregate([
      { $match: { status: { $ne: 'deleted' } } },
      { $group: { _id: '$role', count: { $sum: 1 } } }
    ]);

    // 按部门统计
    const departmentStats = await User.aggregate([
      { $match: { status: { $ne: 'deleted' }, department: { $exists: true, $ne: '' } } },
      { $group: { _id: '$department', count: { $sum: 1 } } }
    ]);

    // 最近登录统计
    const recentLogins = await User.find({
      lastLoginAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
    }).countDocuments();

    const stats = {
      total: totalUsers,
      active: activeUsers,
      inactive: inactiveUsers,
      recentLogins,
      roleDistribution: roleStats,
      departmentDistribution: departmentStats
    };

    logger.info('获取用户统计信息成功');
    return successResponse(res, { stats }, '获取用户统计信息成功');
  } catch (error) {
    logger.error('获取用户统计信息失败:', error);
    return errorResponse(res, '获取用户统计信息失败', 500);
  }
};
