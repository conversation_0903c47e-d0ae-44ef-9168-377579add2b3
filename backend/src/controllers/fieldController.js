const Field = require('../models/Field');
const Crop = require('../models/Crop');
const { validationResult } = require('express-validator');

class FieldController {
  // 获取地块列表
  async getFields(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        });
      }

      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      // 构建查询条件
      const query = {};
      
      if (req.query.status) {
        query.status = req.query.status;
      }
      
      if (req.query.soilType) {
        query.soilType = req.query.soilType;
      }
      
      if (req.query.search) {
        query.$or = [
          { name: { $regex: req.query.search, $options: 'i' } },
          { code: { $regex: req.query.search, $options: 'i' } },
          { description: { $regex: req.query.search, $options: 'i' } }
        ];
      }

      const fields = await Field.find(query)
        .populate('manager', 'name username')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await Field.countDocuments(query);

      // 获取每个地块的作物统计
      const fieldsWithStats = await Promise.all(
        fields.map(async (field) => {
          const cropCount = await Crop.countDocuments({ 
            field: field._id, 
            status: 'active' 
          });
          
          const fieldObj = field.toObject();
          fieldObj.cropCount = cropCount;
          return fieldObj;
        })
      );

      res.json({
        success: true,
        data: {
          fields: fieldsWithStats,
          pagination: {
            current: page,
            pageSize: limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });
    } catch (error) {
      console.error('获取地块列表错误:', error);
      res.status(500).json({
        success: false,
        message: '获取地块列表失败',
        error: error.message
      });
    }
  }

  // 获取地块详情
  async getFieldById(req, res) {
    try {
      const field = await Field.findById(req.params.id)
        .populate('manager', 'name username email phone');

      if (!field) {
        return res.status(404).json({
          success: false,
          message: '地块不存在'
        });
      }

      // 获取地块相关的作物信息
      const crops = await Crop.find({ field: field._id })
        .populate('planter', 'name username')
        .sort({ createdAt: -1 });

      const fieldObj = field.toObject();
      fieldObj.crops = crops;

      res.json({
        success: true,
        data: fieldObj
      });
    } catch (error) {
      console.error('获取地块详情错误:', error);
      res.status(500).json({
        success: false,
        message: '获取地块详情失败',
        error: error.message
      });
    }
  }

  // 创建地块
  async createField(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '数据验证失败',
          errors: errors.array()
        });
      }

      // 检查地块编号是否已存在
      const existingField = await Field.findOne({ code: req.body.code });
      if (existingField) {
        return res.status(400).json({
          success: false,
          message: '地块编号已存在'
        });
      }

      const fieldData = {
        ...req.body,
        manager: req.user.userId
      };

      const field = new Field(fieldData);
      await field.save();

      await field.populate('manager', 'name username');

      res.status(201).json({
        success: true,
        message: '地块创建成功',
        data: field
      });
    } catch (error) {
      console.error('创建地块错误:', error);
      res.status(500).json({
        success: false,
        message: '创建地块失败',
        error: error.message
      });
    }
  }

  // 更新地块
  async updateField(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '数据验证失败',
          errors: errors.array()
        });
      }

      const fieldId = req.params.id;

      // 检查地块是否存在
      const existingField = await Field.findById(fieldId);
      if (!existingField) {
        return res.status(404).json({
          success: false,
          message: '地块不存在'
        });
      }

      // 如果更新编号，检查是否与其他地块冲突
      if (req.body.code && req.body.code !== existingField.code) {
        const duplicateField = await Field.findOne({ 
          code: req.body.code,
          _id: { $ne: fieldId }
        });
        
        if (duplicateField) {
          return res.status(400).json({
            success: false,
            message: '地块编号已存在'
          });
        }
      }

      const field = await Field.findByIdAndUpdate(
        fieldId,
        { ...req.body, updatedAt: new Date() },
        { new: true, runValidators: true }
      ).populate('manager', 'name username');

      res.json({
        success: true,
        message: '地块更新成功',
        data: field
      });
    } catch (error) {
      console.error('更新地块错误:', error);
      res.status(500).json({
        success: false,
        message: '更新地块失败',
        error: error.message
      });
    }
  }

  // 删除地块
  async deleteField(req, res) {
    try {
      const fieldId = req.params.id;

      // 检查地块是否存在
      const field = await Field.findById(fieldId);
      if (!field) {
        return res.status(404).json({
          success: false,
          message: '地块不存在'
        });
      }

      // 检查是否有关联的作物
      const cropCount = await Crop.countDocuments({ 
        field: fieldId, 
        status: { $in: ['active', 'completed'] }
      });

      if (cropCount > 0) {
        return res.status(400).json({
          success: false,
          message: '该地块下还有作物记录，无法删除'
        });
      }

      await Field.findByIdAndDelete(fieldId);

      res.json({
        success: true,
        message: '地块删除成功'
      });
    } catch (error) {
      console.error('删除地块错误:', error);
      res.status(500).json({
        success: false,
        message: '删除地块失败',
        error: error.message
      });
    }
  }

  // 获取地块统计信息
  async getFieldStats(req, res) {
    try {
      const stats = await Field.aggregate([
        {
          $group: {
            _id: null,
            totalFields: { $sum: 1 },
            totalArea: { $sum: '$area' },
            activeFields: {
              $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
            },
            inactiveFields: {
              $sum: { $cond: [{ $eq: ['$status', 'inactive'] }, 1, 0] }
            },
            maintenanceFields: {
              $sum: { $cond: [{ $eq: ['$status', 'maintenance'] }, 1, 0] }
            }
          }
        }
      ]);

      const soilTypeStats = await Field.aggregate([
        {
          $group: {
            _id: '$soilType',
            count: { $sum: 1 },
            area: { $sum: '$area' }
          }
        },
        { $sort: { count: -1 } }
      ]);

      res.json({
        success: true,
        data: {
          overview: stats[0] || {
            totalFields: 0,
            totalArea: 0,
            activeFields: 0,
            inactiveFields: 0,
            maintenanceFields: 0
          },
          soilTypeStats
        }
      });
    } catch (error) {
      console.error('获取地块统计错误:', error);
      res.status(500).json({
        success: false,
        message: '获取地块统计失败',
        error: error.message
      });
    }
  }
}

module.exports = new FieldController();
