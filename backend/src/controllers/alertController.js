const Alert = require('../models/Alert');
const Device = require('../models/Device');
const { validationResult } = require('express-validator');
const { successResponse, errorResponse } = require('../utils/response');
const logger = require('../utils/logger');
const notificationService = require('../services/notificationService');

// 获取预警列表
exports.getAlerts = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      type,
      level,
      status,
      source,
      startDate,
      endDate
    } = req.query;

    // 构建查询条件
    const query = {};
    
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { message: { $regex: search, $options: 'i' } },
        { source: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (type) query.type = type;
    if (level) query.level = level;
    if (status) query.status = status;
    if (source) query.source = source;
    
    // 时间范围过滤
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate);
      if (endDate) query.createdAt.$lte = new Date(endDate);
    }

    // 分页查询
    const skip = (page - 1) * limit;
    const alerts = await Alert.find(query)
      .populate('deviceId', 'name type')
      .populate('fieldId', 'name')
      .populate('createdBy', 'name username')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Alert.countDocuments(query);

    const pagination = {
      current: parseInt(page),
      pageSize: parseInt(limit),
      total,
      pages: Math.ceil(total / limit)
    };

    logger.info(`获取预警列表成功，共${total}条记录`);
    
    return successResponse(res, {
      alerts,
      pagination
    }, '获取预警列表成功');
  } catch (error) {
    logger.error('获取预警列表失败:', error);
    return errorResponse(res, '获取预警列表失败', 500);
  }
};

// 获取预警详情
exports.getAlertById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const alert = await Alert.findById(id)
      .populate('deviceId', 'name type location')
      .populate('fieldId', 'name location')
      .populate('createdBy', 'name username')
      .populate('resolvedBy', 'name username');
      
    if (!alert) {
      return errorResponse(res, '预警不存在', 404);
    }

    logger.info(`获取预警详情成功: ${alert.title}`);
    return successResponse(res, { alert }, '获取预警详情成功');
  } catch (error) {
    logger.error('获取预警详情失败:', error);
    return errorResponse(res, '获取预警详情失败', 500);
  }
};

// 创建预警
exports.createAlert = async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return errorResponse(res, '输入数据验证失败', 400, errors.array());
    }

    const {
      title,
      message,
      type,
      level = 'info',
      source,
      deviceId,
      fieldId,
      data,
      threshold
    } = req.body;

    // 创建预警
    const alert = new Alert({
      title,
      message,
      type,
      level,
      source,
      deviceId,
      fieldId,
      data,
      threshold,
      status: 'active',
      createdBy: req.user.id
    });

    await alert.save();

    // 填充关联数据
    await alert.populate([
      { path: 'deviceId', select: 'name type' },
      { path: 'fieldId', select: 'name' },
      { path: 'createdBy', select: 'name username' }
    ]);

    // 发送通知
    try {
      await notificationService.sendAlertNotification(alert);
    } catch (notifyError) {
      logger.error('发送预警通知失败:', notifyError);
    }

    logger.info(`创建预警成功: ${title}`);
    return successResponse(res, { alert }, '创建预警成功', 201);
  } catch (error) {
    logger.error('创建预警失败:', error);
    return errorResponse(res, '创建预警失败', 500);
  }
};

// 更新预警
exports.updateAlert = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title,
      message,
      type,
      level,
      source,
      data,
      threshold
    } = req.body;

    // 检查预警是否存在
    const alert = await Alert.findById(id);
    if (!alert) {
      return errorResponse(res, '预警不存在', 404);
    }

    // 更新预警信息
    const updateData = {
      ...(title && { title }),
      ...(message && { message }),
      ...(type && { type }),
      ...(level && { level }),
      ...(source && { source }),
      ...(data && { data }),
      ...(threshold && { threshold }),
      updatedAt: new Date(),
      updatedBy: req.user.id
    };

    const updatedAlert = await Alert.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate([
      { path: 'deviceId', select: 'name type' },
      { path: 'fieldId', select: 'name' },
      { path: 'createdBy', select: 'name username' }
    ]);

    logger.info(`更新预警成功: ${updatedAlert.title}`);
    return successResponse(res, { alert: updatedAlert }, '更新预警成功');
  } catch (error) {
    logger.error('更新预警失败:', error);
    return errorResponse(res, '更新预警失败', 500);
  }
};

// 标记预警为已读
exports.markAsRead = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查预警是否存在
    const alert = await Alert.findById(id);
    if (!alert) {
      return errorResponse(res, '预警不存在', 404);
    }

    // 标记为已读
    const updatedAlert = await Alert.findByIdAndUpdate(
      id,
      {
        isRead: true,
        readAt: new Date(),
        readBy: req.user.id
      },
      { new: true }
    );

    logger.info(`标记预警已读成功: ${alert.title}`);
    return successResponse(res, { alert: updatedAlert }, '标记已读成功');
  } catch (error) {
    logger.error('标记预警已读失败:', error);
    return errorResponse(res, '标记已读失败', 500);
  }
};

// 批量标记为已读
exports.markAllAsRead = async (req, res) => {
  try {
    const { alertIds } = req.body;

    if (!alertIds || !Array.isArray(alertIds) || alertIds.length === 0) {
      return errorResponse(res, '请提供有效的预警ID列表', 400);
    }

    // 批量更新
    const result = await Alert.updateMany(
      { _id: { $in: alertIds }, isRead: false },
      {
        isRead: true,
        readAt: new Date(),
        readBy: req.user.id
      }
    );

    logger.info(`批量标记预警已读成功，共${result.modifiedCount}条`);
    return successResponse(res, { 
      modifiedCount: result.modifiedCount 
    }, '批量标记已读成功');
  } catch (error) {
    logger.error('批量标记预警已读失败:', error);
    return errorResponse(res, '批量标记已读失败', 500);
  }
};

// 解决预警
exports.resolveAlert = async (req, res) => {
  try {
    const { id } = req.params;
    const { resolution } = req.body;

    if (!resolution || resolution.trim().length === 0) {
      return errorResponse(res, '请提供解决方案', 400);
    }

    // 检查预警是否存在
    const alert = await Alert.findById(id);
    if (!alert) {
      return errorResponse(res, '预警不存在', 404);
    }

    // 解决预警
    const updatedAlert = await Alert.findByIdAndUpdate(
      id,
      {
        status: 'resolved',
        resolution: resolution.trim(),
        resolvedAt: new Date(),
        resolvedBy: req.user.id,
        isRead: true,
        readAt: alert.readAt || new Date(),
        readBy: alert.readBy || req.user.id
      },
      { new: true }
    ).populate([
      { path: 'resolvedBy', select: 'name username' }
    ]);

    logger.info(`解决预警成功: ${alert.title}`);
    return successResponse(res, { alert: updatedAlert }, '解决预警成功');
  } catch (error) {
    logger.error('解决预警失败:', error);
    return errorResponse(res, '解决预警失败', 500);
  }
};

// 删除预警
exports.deleteAlert = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查预警是否存在
    const alert = await Alert.findById(id);
    if (!alert) {
      return errorResponse(res, '预警不存在', 404);
    }

    // 删除预警
    await Alert.findByIdAndDelete(id);

    logger.info(`删除预警成功: ${alert.title}`);
    return successResponse(res, null, '删除预警成功');
  } catch (error) {
    logger.error('删除预警失败:', error);
    return errorResponse(res, '删除预警失败', 500);
  }
};

// 获取预警统计信息
exports.getAlertStats = async (req, res) => {
  try {
    const totalAlerts = await Alert.countDocuments();
    const activeAlerts = await Alert.countDocuments({ status: 'active' });
    const resolvedAlerts = await Alert.countDocuments({ status: 'resolved' });
    const unreadAlerts = await Alert.countDocuments({ isRead: false });
    
    // 按级别统计
    const levelStats = await Alert.aggregate([
      { $group: { _id: '$level', count: { $sum: 1 } } }
    ]);

    // 按类型统计
    const typeStats = await Alert.aggregate([
      { $group: { _id: '$type', count: { $sum: 1 } } }
    ]);

    // 最近7天的预警趋势
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const recentTrend = await Alert.aggregate([
      { $match: { createdAt: { $gte: sevenDaysAgo } } },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    const stats = {
      total: totalAlerts,
      active: activeAlerts,
      resolved: resolvedAlerts,
      unread: unreadAlerts,
      levelDistribution: levelStats,
      typeDistribution: typeStats,
      recentTrend
    };

    logger.info('获取预警统计信息成功');
    return successResponse(res, { stats }, '获取预警统计信息成功');
  } catch (error) {
    logger.error('获取预警统计信息失败:', error);
    return errorResponse(res, '获取预警统计信息失败', 500);
  }
};

// 获取未读预警数量
exports.getUnreadCount = async (req, res) => {
  try {
    const count = await Alert.countDocuments({ isRead: false });
    
    logger.info(`获取未读预警数量成功: ${count}`);
    return successResponse(res, { count }, '获取未读预警数量成功');
  } catch (error) {
    logger.error('获取未读预警数量失败:', error);
    return errorResponse(res, '获取未读预警数量失败', 500);
  }
};
