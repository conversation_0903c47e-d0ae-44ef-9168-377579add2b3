const Camera = require('../models/Camera');
const VideoRecord = require('../models/VideoRecord');
const videoService = require('../services/videoService');
const { validationResult } = require('express-validator');
const path = require('path');
const fs = require('fs');

class VideoController {
  // 获取摄像头列表
  async getCameras(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        });
      }

      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      // 构建查询条件
      const query = {};
      
      if (req.query.status) {
        query.status = req.query.status;
      }
      
      if (req.query.field) {
        query.field = req.query.field;
      }
      
      if (req.query.search) {
        query.$or = [
          { name: { $regex: req.query.search, $options: 'i' } },
          { description: { $regex: req.query.search, $options: 'i' } }
        ];
      }

      const cameras = await Camera.find(query)
        .populate('field', 'name code')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await Camera.countDocuments(query);

      // 添加摄像头在线状态
      const camerasWithStatus = await Promise.all(
        cameras.map(async (camera) => {
          const cameraObj = camera.toObject();
          cameraObj.isOnline = await this.checkCameraOnline(camera._id);
          cameraObj.isRecording = await this.checkCameraRecording(camera._id);
          return cameraObj;
        })
      );

      res.json({
        success: true,
        data: {
          cameras: camerasWithStatus,
          pagination: {
            current: page,
            pageSize: limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });
    } catch (error) {
      console.error('获取摄像头列表错误:', error);
      res.status(500).json({
        success: false,
        message: '获取摄像头列表失败',
        error: error.message
      });
    }
  }

  // 获取摄像头详情
  async getCameraById(req, res) {
    try {
      const camera = await Camera.findById(req.params.id)
        .populate('field', 'name code area location');

      if (!camera) {
        return res.status(404).json({
          success: false,
          message: '摄像头不存在'
        });
      }

      const cameraObj = camera.toObject();
      cameraObj.isOnline = await this.checkCameraOnline(camera._id);
      cameraObj.isRecording = await this.checkCameraRecording(camera._id);

      // 获取最近的录像记录
      const recentRecords = await VideoRecord.find({ camera: camera._id })
        .sort({ startTime: -1 })
        .limit(10);

      cameraObj.recentRecords = recentRecords;

      res.json({
        success: true,
        data: cameraObj
      });
    } catch (error) {
      console.error('获取摄像头详情错误:', error);
      res.status(500).json({
        success: false,
        message: '获取摄像头详情失败',
        error: error.message
      });
    }
  }

  // 创建摄像头
  async createCamera(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '数据验证失败',
          errors: errors.array()
        });
      }

      const camera = new Camera(req.body);
      await camera.save();

      // 注册到视频服务
      try {
        await videoService.registerCamera(camera);
      } catch (videoError) {
        console.warn('视频服务注册摄像头失败:', videoError.message);
      }

      await camera.populate('field', 'name code');

      res.status(201).json({
        success: true,
        message: '摄像头创建成功',
        data: camera
      });
    } catch (error) {
      console.error('创建摄像头错误:', error);
      res.status(500).json({
        success: false,
        message: '创建摄像头失败',
        error: error.message
      });
    }
  }

  // 更新摄像头
  async updateCamera(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '数据验证失败',
          errors: errors.array()
        });
      }

      const cameraId = req.params.id;

      // 检查摄像头是否存在
      const existingCamera = await Camera.findById(cameraId);
      if (!existingCamera) {
        return res.status(404).json({
          success: false,
          message: '摄像头不存在'
        });
      }

      const camera = await Camera.findByIdAndUpdate(
        cameraId,
        { ...req.body, updatedAt: new Date() },
        { new: true, runValidators: true }
      ).populate('field', 'name code');

      // 更新视频服务中的摄像头信息
      try {
        await videoService.updateCamera(camera);
      } catch (videoError) {
        console.warn('视频服务更新摄像头失败:', videoError.message);
      }

      res.json({
        success: true,
        message: '摄像头更新成功',
        data: camera
      });
    } catch (error) {
      console.error('更新摄像头错误:', error);
      res.status(500).json({
        success: false,
        message: '更新摄像头失败',
        error: error.message
      });
    }
  }

  // 删除摄像头
  async deleteCamera(req, res) {
    try {
      const cameraId = req.params.id;

      // 检查摄像头是否存在
      const camera = await Camera.findById(cameraId);
      if (!camera) {
        return res.status(404).json({
          success: false,
          message: '摄像头不存在'
        });
      }

      // 停止录像
      try {
        await videoService.stopRecording(cameraId);
      } catch (error) {
        console.warn('停止录像失败:', error.message);
      }

      // 从视频服务中注销摄像头
      try {
        await videoService.unregisterCamera(camera._id);
      } catch (videoError) {
        console.warn('视频服务注销摄像头失败:', videoError.message);
      }

      await Camera.findByIdAndDelete(cameraId);

      res.json({
        success: true,
        message: '摄像头删除成功'
      });
    } catch (error) {
      console.error('删除摄像头错误:', error);
      res.status(500).json({
        success: false,
        message: '删除摄像头失败',
        error: error.message
      });
    }
  }

  // 获取实时流
  async getLiveStream(req, res) {
    try {
      const { cameraId } = req.params;

      const camera = await Camera.findById(cameraId);
      if (!camera) {
        return res.status(404).json({
          success: false,
          message: '摄像头不存在'
        });
      }

      const streamUrl = await videoService.getLiveStreamUrl(cameraId);

      res.json({
        success: true,
        data: {
          streamUrl,
          camera: camera.name
        }
      });
    } catch (error) {
      console.error('获取实时流错误:', error);
      res.status(500).json({
        success: false,
        message: '获取实时流失败',
        error: error.message
      });
    }
  }

  // 开始录像
  async startRecording(req, res) {
    try {
      const { cameraId } = req.params;
      const { duration, quality } = req.body;

      const camera = await Camera.findById(cameraId);
      if (!camera) {
        return res.status(404).json({
          success: false,
          message: '摄像头不存在'
        });
      }

      const recordingId = await videoService.startRecording(cameraId, {
        duration,
        quality: quality || 'medium'
      });

      res.json({
        success: true,
        message: '录像开始成功',
        data: { recordingId }
      });
    } catch (error) {
      console.error('开始录像错误:', error);
      res.status(500).json({
        success: false,
        message: '开始录像失败',
        error: error.message
      });
    }
  }

  // 停止录像
  async stopRecording(req, res) {
    try {
      const { cameraId } = req.params;

      const camera = await Camera.findById(cameraId);
      if (!camera) {
        return res.status(404).json({
          success: false,
          message: '摄像头不存在'
        });
      }

      await videoService.stopRecording(cameraId);

      res.json({
        success: true,
        message: '录像停止成功'
      });
    } catch (error) {
      console.error('停止录像错误:', error);
      res.status(500).json({
        success: false,
        message: '停止录像失败',
        error: error.message
      });
    }
  }

  // 获取录像列表
  async getVideoRecords(req, res) {
    try {
      const { cameraId } = req.params;
      const { startDate, endDate } = req.query;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 20;
      const skip = (page - 1) * limit;

      // 构建查询条件
      const query = { camera: cameraId };
      
      if (startDate && endDate) {
        query.startTime = {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        };
      }

      const records = await VideoRecord.find(query)
        .sort({ startTime: -1 })
        .skip(skip)
        .limit(limit);

      const total = await VideoRecord.countDocuments(query);

      res.json({
        success: true,
        data: {
          records,
          pagination: {
            current: page,
            pageSize: limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });
    } catch (error) {
      console.error('获取录像列表错误:', error);
      res.status(500).json({
        success: false,
        message: '获取录像列表失败',
        error: error.message
      });
    }
  }

  // 播放录像
  async playRecord(req, res) {
    try {
      const { recordId } = req.params;

      const record = await VideoRecord.findById(recordId);
      if (!record) {
        return res.status(404).json({
          success: false,
          message: '录像记录不存在'
        });
      }

      // 检查文件是否存在
      if (!fs.existsSync(record.filePath)) {
        return res.status(404).json({
          success: false,
          message: '录像文件不存在'
        });
      }

      const playbackUrl = await videoService.getPlaybackUrl(recordId);

      res.json({
        success: true,
        data: {
          playbackUrl,
          record: {
            id: record._id,
            startTime: record.startTime,
            endTime: record.endTime,
            duration: record.duration,
            fileSize: record.fileSize
          }
        }
      });
    } catch (error) {
      console.error('播放录像错误:', error);
      res.status(500).json({
        success: false,
        message: '播放录像失败',
        error: error.message
      });
    }
  }

  // 下载录像
  async downloadRecord(req, res) {
    try {
      const { recordId } = req.params;

      const record = await VideoRecord.findById(recordId);
      if (!record) {
        return res.status(404).json({
          success: false,
          message: '录像记录不存在'
        });
      }

      // 检查文件是否存在
      if (!fs.existsSync(record.filePath)) {
        return res.status(404).json({
          success: false,
          message: '录像文件不存在'
        });
      }

      const fileName = path.basename(record.filePath);
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      res.setHeader('Content-Type', 'video/mp4');

      const fileStream = fs.createReadStream(record.filePath);
      fileStream.pipe(res);
    } catch (error) {
      console.error('下载录像错误:', error);
      res.status(500).json({
        success: false,
        message: '下载录像失败',
        error: error.message
      });
    }
  }

  // 删除录像
  async deleteRecord(req, res) {
    try {
      const { recordId } = req.params;

      const record = await VideoRecord.findById(recordId);
      if (!record) {
        return res.status(404).json({
          success: false,
          message: '录像记录不存在'
        });
      }

      // 删除文件
      if (fs.existsSync(record.filePath)) {
        fs.unlinkSync(record.filePath);
      }

      await VideoRecord.findByIdAndDelete(recordId);

      res.json({
        success: true,
        message: '录像删除成功'
      });
    } catch (error) {
      console.error('删除录像错误:', error);
      res.status(500).json({
        success: false,
        message: '删除录像失败',
        error: error.message
      });
    }
  }

  // 检查摄像头在线状态
  async checkCameraOnline(cameraId) {
    try {
      return await videoService.isCameraOnline(cameraId);
    } catch (error) {
      return false;
    }
  }

  // 检查摄像头录像状态
  async checkCameraRecording(cameraId) {
    try {
      return await videoService.isRecording(cameraId);
    } catch (error) {
      return false;
    }
  }
}

module.exports = new VideoController();
