const Weather = require('../models/Weather');
const weatherService = require('../services/weatherService');
const { validationResult } = require('express-validator');
const moment = require('moment');

class WeatherController {
  // 获取当前天气
  async getCurrentWeather(req, res) {
    try {
      const { lat, lon } = req.query;
      
      if (!lat || !lon) {
        return res.status(400).json({
          success: false,
          message: '缺少经纬度参数'
        });
      }

      // 先从数据库获取最新的天气数据
      const latestWeather = await Weather.findOne({
        location: {
          $near: {
            $geometry: {
              type: 'Point',
              coordinates: [parseFloat(lon), parseFloat(lat)]
            },
            $maxDistance: 10000 // 10公里范围内
          }
        }
      }).sort({ date: -1, createdAt: -1 });

      // 如果数据库中有1小时内的数据，直接返回
      if (latestWeather && moment().diff(moment(latestWeather.createdAt), 'hours') < 1) {
        return res.json({
          success: true,
          data: latestWeather,
          source: 'database'
        });
      }

      // 否则从天气API获取最新数据
      try {
        const weatherData = await weatherService.getCurrentWeather(lat, lon);
        
        // 保存到数据库
        const weather = new Weather({
          ...weatherData,
          location: {
            type: 'Point',
            coordinates: [parseFloat(lon), parseFloat(lat)]
          }
        });
        
        await weather.save();

        res.json({
          success: true,
          data: weather,
          source: 'api'
        });
      } catch (apiError) {
        // 如果API调用失败，返回数据库中最新的数据
        if (latestWeather) {
          return res.json({
            success: true,
            data: latestWeather,
            source: 'database_fallback',
            message: '天气API暂时不可用，返回历史数据'
          });
        }
        
        throw apiError;
      }
    } catch (error) {
      console.error('获取当前天气错误:', error);
      res.status(500).json({
        success: false,
        message: '获取天气信息失败',
        error: error.message
      });
    }
  }

  // 获取天气预报
  async getWeatherForecast(req, res) {
    try {
      const { lat, lon, days = 7 } = req.query;
      
      if (!lat || !lon) {
        return res.status(400).json({
          success: false,
          message: '缺少经纬度参数'
        });
      }

      const forecastData = await weatherService.getWeatherForecast(lat, lon, days);

      res.json({
        success: true,
        data: forecastData
      });
    } catch (error) {
      console.error('获取天气预报错误:', error);
      res.status(500).json({
        success: false,
        message: '获取天气预报失败',
        error: error.message
      });
    }
  }

  // 获取历史天气数据
  async getWeatherHistory(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        });
      }

      const { lat, lon, startDate, endDate } = req.query;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 30;
      const skip = (page - 1) * limit;

      // 构建查询条件
      const query = {};
      
      if (lat && lon) {
        query.location = {
          $near: {
            $geometry: {
              type: 'Point',
              coordinates: [parseFloat(lon), parseFloat(lat)]
            },
            $maxDistance: 10000 // 10公里范围内
          }
        };
      }

      if (startDate && endDate) {
        query.date = {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        };
      }

      const weatherData = await Weather.find(query)
        .sort({ date: -1 })
        .skip(skip)
        .limit(limit);

      const total = await Weather.countDocuments(query);

      res.json({
        success: true,
        data: {
          weather: weatherData,
          pagination: {
            current: page,
            pageSize: limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });
    } catch (error) {
      console.error('获取历史天气数据错误:', error);
      res.status(500).json({
        success: false,
        message: '获取历史天气数据失败',
        error: error.message
      });
    }
  }

  // 获取天气统计
  async getWeatherStats(req, res) {
    try {
      const { lat, lon, period = '30d' } = req.query;
      
      let startDate;
      switch (period) {
        case '7d':
          startDate = moment().subtract(7, 'days').toDate();
          break;
        case '30d':
          startDate = moment().subtract(30, 'days').toDate();
          break;
        case '90d':
          startDate = moment().subtract(90, 'days').toDate();
          break;
        default:
          startDate = moment().subtract(30, 'days').toDate();
      }

      // 构建查询条件
      const matchQuery = {
        date: { $gte: startDate }
      };

      if (lat && lon) {
        matchQuery.location = {
          $near: {
            $geometry: {
              type: 'Point',
              coordinates: [parseFloat(lon), parseFloat(lat)]
            },
            $maxDistance: 10000
          }
        };
      }

      const stats = await Weather.aggregate([
        { $match: matchQuery },
        {
          $group: {
            _id: null,
            avgTemperature: { $avg: '$temperature' },
            maxTemperature: { $max: '$temperature' },
            minTemperature: { $min: '$temperature' },
            avgHumidity: { $avg: '$humidity' },
            totalRainfall: { $sum: '$rainfall' },
            avgWindSpeed: { $avg: '$windSpeed' },
            rainyDays: {
              $sum: { $cond: [{ $gt: ['$rainfall', 0] }, 1, 0] }
            },
            totalDays: { $sum: 1 }
          }
        }
      ]);

      // 按天统计
      const dailyStats = await Weather.aggregate([
        { $match: matchQuery },
        {
          $group: {
            _id: {
              $dateToString: { format: '%Y-%m-%d', date: '$date' }
            },
            avgTemp: { $avg: '$temperature' },
            maxTemp: { $max: '$temperature' },
            minTemp: { $min: '$temperature' },
            totalRainfall: { $sum: '$rainfall' },
            avgHumidity: { $avg: '$humidity' }
          }
        },
        { $sort: { '_id': 1 } }
      ]);

      // 天气类型统计
      const weatherTypeStats = await Weather.aggregate([
        { $match: matchQuery },
        {
          $group: {
            _id: '$condition',
            count: { $sum: 1 }
          }
        },
        { $sort: { count: -1 } }
      ]);

      res.json({
        success: true,
        data: {
          overview: stats[0] || {
            avgTemperature: 0,
            maxTemperature: 0,
            minTemperature: 0,
            avgHumidity: 0,
            totalRainfall: 0,
            avgWindSpeed: 0,
            rainyDays: 0,
            totalDays: 0
          },
          dailyStats,
          weatherTypeStats
        }
      });
    } catch (error) {
      console.error('获取天气统计错误:', error);
      res.status(500).json({
        success: false,
        message: '获取天气统计失败',
        error: error.message
      });
    }
  }

  // 获取农业气象建议
  async getAgriculturalAdvice(req, res) {
    try {
      const { lat, lon } = req.query;
      
      if (!lat || !lon) {
        return res.status(400).json({
          success: false,
          message: '缺少经纬度参数'
        });
      }

      // 获取当前天气和预报
      const currentWeather = await weatherService.getCurrentWeather(lat, lon);
      const forecast = await weatherService.getWeatherForecast(lat, lon, 3);

      // 生成农业建议
      const advice = this.generateAgriculturalAdvice(currentWeather, forecast);

      res.json({
        success: true,
        data: {
          currentWeather,
          forecast,
          advice
        }
      });
    } catch (error) {
      console.error('获取农业气象建议错误:', error);
      res.status(500).json({
        success: false,
        message: '获取农业气象建议失败',
        error: error.message
      });
    }
  }

  // 生成农业建议
  generateAgriculturalAdvice(current, forecast) {
    const advice = [];

    // 温度建议
    if (current.temperature > 35) {
      advice.push({
        type: 'warning',
        category: '高温预警',
        message: '当前温度过高，建议增加灌溉频次，避免中午时段作业',
        priority: 'high'
      });
    } else if (current.temperature < 5) {
      advice.push({
        type: 'warning',
        category: '低温预警',
        message: '当前温度较低，注意作物防冻，可考虑覆盖保温',
        priority: 'high'
      });
    }

    // 湿度建议
    if (current.humidity > 80) {
      advice.push({
        type: 'info',
        category: '湿度管理',
        message: '湿度较高，注意通风，预防病虫害发生',
        priority: 'medium'
      });
    } else if (current.humidity < 30) {
      advice.push({
        type: 'info',
        category: '湿度管理',
        message: '空气干燥，建议增加灌溉，保持土壤湿润',
        priority: 'medium'
      });
    }

    // 降雨建议
    if (current.rainfall > 10) {
      advice.push({
        type: 'info',
        category: '降雨管理',
        message: '有降雨，暂停灌溉作业，注意田间排水',
        priority: 'medium'
      });
    }

    // 风速建议
    if (current.windSpeed > 15) {
      advice.push({
        type: 'warning',
        category: '大风预警',
        message: '风力较大，暂停喷药作业，加固农业设施',
        priority: 'high'
      });
    }

    // 预报建议
    if (forecast && forecast.length > 0) {
      const tomorrowWeather = forecast[0];
      
      if (tomorrowWeather.rainfall > 5) {
        advice.push({
          type: 'info',
          category: '明日预报',
          message: '明日有降雨，今日可适当减少灌溉',
          priority: 'low'
        });
      }
      
      if (tomorrowWeather.temperature > current.temperature + 5) {
        advice.push({
          type: 'info',
          category: '明日预报',
          message: '明日气温上升，注意调整作业时间',
          priority: 'low'
        });
      }
    }

    return advice;
  }

  // 手动添加天气数据
  async addWeatherData(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '数据验证失败',
          errors: errors.array()
        });
      }

      const weather = new Weather(req.body);
      await weather.save();

      res.status(201).json({
        success: true,
        message: '天气数据添加成功',
        data: weather
      });
    } catch (error) {
      console.error('添加天气数据错误:', error);
      res.status(500).json({
        success: false,
        message: '添加天气数据失败',
        error: error.message
      });
    }
  }
}

module.exports = new WeatherController();
