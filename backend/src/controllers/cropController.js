const Crop = require('../models/Crop');
const Field = require('../models/Field');
const FarmingOperation = require('../models/FarmingOperation');
const { validationResult } = require('express-validator');

class CropController {
  // 获取作物列表
  async getCrops(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        });
      }

      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      // 构建查询条件
      const query = {};
      
      if (req.query.field) {
        query.field = req.query.field;
      }
      
      if (req.query.category) {
        query.category = req.query.category;
      }
      
      if (req.query.growthStage) {
        query.growthStage = req.query.growthStage;
      }
      
      if (req.query.status) {
        query.status = req.query.status;
      }
      
      if (req.query.season) {
        query.season = req.query.season;
      }
      
      if (req.query.year) {
        query.year = parseInt(req.query.year);
      }
      
      if (req.query.search) {
        query.$or = [
          { name: { $regex: req.query.search, $options: 'i' } },
          { variety: { $regex: req.query.search, $options: 'i' } },
          { description: { $regex: req.query.search, $options: 'i' } }
        ];
      }

      const crops = await Crop.find(query)
        .populate('field', 'name code area')
        .populate('planter', 'name username')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await Crop.countDocuments(query);

      res.json({
        success: true,
        data: {
          crops,
          pagination: {
            current: page,
            pageSize: limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });
    } catch (error) {
      console.error('获取作物列表错误:', error);
      res.status(500).json({
        success: false,
        message: '获取作物列表失败',
        error: error.message
      });
    }
  }

  // 获取作物详情
  async getCropById(req, res) {
    try {
      const crop = await Crop.findById(req.params.id)
        .populate('field', 'name code area soilType')
        .populate('planter', 'name username email phone');

      if (!crop) {
        return res.status(404).json({
          success: false,
          message: '作物不存在'
        });
      }

      // 获取相关的农事操作记录
      const operations = await FarmingOperation.find({ crop: crop._id })
        .populate('operator', 'name username')
        .sort({ operationDate: -1 });

      const cropObj = crop.toObject();
      cropObj.operations = operations;

      res.json({
        success: true,
        data: cropObj
      });
    } catch (error) {
      console.error('获取作物详情错误:', error);
      res.status(500).json({
        success: false,
        message: '获取作物详情失败',
        error: error.message
      });
    }
  }

  // 创建作物
  async createCrop(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '数据验证失败',
          errors: errors.array()
        });
      }

      // 检查地块是否存在
      const field = await Field.findById(req.body.field);
      if (!field) {
        return res.status(400).json({
          success: false,
          message: '地块不存在'
        });
      }

      // 检查种植面积是否超过地块面积
      if (req.body.plantingArea > field.area) {
        return res.status(400).json({
          success: false,
          message: '种植面积不能超过地块面积'
        });
      }

      const cropData = {
        ...req.body,
        planter: req.user.userId
      };

      const crop = new Crop(cropData);
      await crop.save();

      await crop.populate('field', 'name code area');
      await crop.populate('planter', 'name username');

      res.status(201).json({
        success: true,
        message: '作物创建成功',
        data: crop
      });
    } catch (error) {
      console.error('创建作物错误:', error);
      res.status(500).json({
        success: false,
        message: '创建作物失败',
        error: error.message
      });
    }
  }

  // 更新作物
  async updateCrop(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '数据验证失败',
          errors: errors.array()
        });
      }

      const cropId = req.params.id;

      // 检查作物是否存在
      const existingCrop = await Crop.findById(cropId);
      if (!existingCrop) {
        return res.status(404).json({
          success: false,
          message: '作物不存在'
        });
      }

      // 如果更新地块，检查新地块是否存在
      if (req.body.field && req.body.field !== existingCrop.field.toString()) {
        const field = await Field.findById(req.body.field);
        if (!field) {
          return res.status(400).json({
            success: false,
            message: '地块不存在'
          });
        }

        // 检查种植面积是否超过新地块面积
        const plantingArea = req.body.plantingArea || existingCrop.plantingArea;
        if (plantingArea > field.area) {
          return res.status(400).json({
            success: false,
            message: '种植面积不能超过地块面积'
          });
        }
      }

      const crop = await Crop.findByIdAndUpdate(
        cropId,
        { ...req.body, updatedAt: new Date() },
        { new: true, runValidators: true }
      ).populate('field', 'name code area')
       .populate('planter', 'name username');

      res.json({
        success: true,
        message: '作物更新成功',
        data: crop
      });
    } catch (error) {
      console.error('更新作物错误:', error);
      res.status(500).json({
        success: false,
        message: '更新作物失败',
        error: error.message
      });
    }
  }

  // 删除作物
  async deleteCrop(req, res) {
    try {
      const cropId = req.params.id;

      // 检查作物是否存在
      const crop = await Crop.findById(cropId);
      if (!crop) {
        return res.status(404).json({
          success: false,
          message: '作物不存在'
        });
      }

      // 检查是否有关联的农事操作记录
      const operationCount = await FarmingOperation.countDocuments({ crop: cropId });
      if (operationCount > 0) {
        return res.status(400).json({
          success: false,
          message: '该作物下还有农事操作记录，无法删除'
        });
      }

      await Crop.findByIdAndDelete(cropId);

      res.json({
        success: true,
        message: '作物删除成功'
      });
    } catch (error) {
      console.error('删除作物错误:', error);
      res.status(500).json({
        success: false,
        message: '删除作物失败',
        error: error.message
      });
    }
  }

  // 获取作物统计信息
  async getCropStats(req, res) {
    try {
      const stats = await Crop.aggregate([
        {
          $group: {
            _id: null,
            totalCrops: { $sum: 1 },
            totalPlantingArea: { $sum: '$plantingArea' },
            activeCrops: {
              $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
            },
            completedCrops: {
              $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
            },
            failedCrops: {
              $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
            }
          }
        }
      ]);

      const categoryStats = await Crop.aggregate([
        {
          $group: {
            _id: '$category',
            count: { $sum: 1 },
            area: { $sum: '$plantingArea' }
          }
        },
        { $sort: { count: -1 } }
      ]);

      const growthStageStats = await Crop.aggregate([
        {
          $match: { status: 'active' }
        },
        {
          $group: {
            _id: '$growthStage',
            count: { $sum: 1 }
          }
        },
        { $sort: { count: -1 } }
      ]);

      res.json({
        success: true,
        data: {
          overview: stats[0] || {
            totalCrops: 0,
            totalPlantingArea: 0,
            activeCrops: 0,
            completedCrops: 0,
            failedCrops: 0
          },
          categoryStats,
          growthStageStats
        }
      });
    } catch (error) {
      console.error('获取作物统计错误:', error);
      res.status(500).json({
        success: false,
        message: '获取作物统计失败',
        error: error.message
      });
    }
  }
}

module.exports = new CropController();
