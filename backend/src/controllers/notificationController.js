const Notification = require('../models/Notification');
const NotificationTemplate = require('../models/NotificationTemplate');
const notificationService = require('../services/notificationService');
const { validationResult } = require('express-validator');

class NotificationController {
  // 获取通知列表
  async getNotifications(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        });
      }

      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      // 构建查询条件
      const query = {};
      
      // 只显示当前用户相关的通知
      query.$or = [
        { 'recipients.userId': req.user.userId },
        { createdBy: req.user.userId }
      ];
      
      if (req.query.status) {
        query.status = req.query.status;
      }
      
      if (req.query.type) {
        query.type = req.query.type;
      }
      
      if (req.query.level) {
        query.level = req.query.level;
      }
      
      if (req.query.read !== undefined) {
        const isRead = req.query.read === 'true';
        query['recipients.readAt'] = isRead ? { $exists: true } : { $exists: false };
      }

      const notifications = await Notification.find(query)
        .populate('template', 'name type')
        .populate('createdBy', 'name username')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await Notification.countDocuments(query);

      // 添加当前用户的读取状态
      const notificationsWithReadStatus = notifications.map(notification => {
        const notificationObj = notification.toObject();
        const recipient = notification.recipients.find(r => 
          r.userId.toString() === req.user.userId
        );
        notificationObj.isRead = !!recipient?.readAt;
        notificationObj.readAt = recipient?.readAt;
        return notificationObj;
      });

      res.json({
        success: true,
        data: {
          notifications: notificationsWithReadStatus,
          pagination: {
            current: page,
            pageSize: limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });
    } catch (error) {
      console.error('获取通知列表错误:', error);
      res.status(500).json({
        success: false,
        message: '获取通知列表失败',
        error: error.message
      });
    }
  }

  // 获取通知详情
  async getNotificationById(req, res) {
    try {
      const notification = await Notification.findById(req.params.id)
        .populate('template', 'name type description')
        .populate('createdBy', 'name username');

      if (!notification) {
        return res.status(404).json({
          success: false,
          message: '通知不存在'
        });
      }

      // 检查用户是否有权限查看此通知
      const hasPermission = notification.recipients.some(r => 
        r.userId.toString() === req.user.userId
      ) || notification.createdBy.toString() === req.user.userId;

      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: '无权限查看此通知'
        });
      }

      const notificationObj = notification.toObject();
      const recipient = notification.recipients.find(r => 
        r.userId.toString() === req.user.userId
      );
      notificationObj.isRead = !!recipient?.readAt;
      notificationObj.readAt = recipient?.readAt;

      res.json({
        success: true,
        data: notificationObj
      });
    } catch (error) {
      console.error('获取通知详情错误:', error);
      res.status(500).json({
        success: false,
        message: '获取通知详情失败',
        error: error.message
      });
    }
  }

  // 发送通知
  async sendNotification(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '数据验证失败',
          errors: errors.array()
        });
      }

      const { templateCode, recipients, variables = {}, priority, scheduledAt } = req.body;

      const notification = await notificationService.sendNotification(
        templateCode,
        variables,
        recipients,
        {
          priority,
          scheduledAt: scheduledAt ? new Date(scheduledAt) : undefined,
          createdBy: req.user.userId
        }
      );

      res.status(201).json({
        success: true,
        message: '通知发送成功',
        data: notification
      });
    } catch (error) {
      console.error('发送通知错误:', error);
      res.status(500).json({
        success: false,
        message: '发送通知失败',
        error: error.message
      });
    }
  }

  // 标记通知为已读
  async markAsRead(req, res) {
    try {
      const { notificationId } = req.params;

      const notification = await Notification.findById(notificationId);
      if (!notification) {
        return res.status(404).json({
          success: false,
          message: '通知不存在'
        });
      }

      // 查找当前用户的接收记录
      const recipientIndex = notification.recipients.findIndex(r => 
        r.userId.toString() === req.user.userId
      );

      if (recipientIndex === -1) {
        return res.status(403).json({
          success: false,
          message: '无权限操作此通知'
        });
      }

      // 标记为已读
      if (!notification.recipients[recipientIndex].readAt) {
        notification.recipients[recipientIndex].readAt = new Date();
        await notification.save();
      }

      res.json({
        success: true,
        message: '通知已标记为已读'
      });
    } catch (error) {
      console.error('标记通知已读错误:', error);
      res.status(500).json({
        success: false,
        message: '标记通知已读失败',
        error: error.message
      });
    }
  }

  // 批量标记为已读
  async markAllAsRead(req, res) {
    try {
      const { notificationIds } = req.body;

      if (!notificationIds || !Array.isArray(notificationIds)) {
        return res.status(400).json({
          success: false,
          message: '通知ID列表不能为空'
        });
      }

      const result = await Notification.updateMany(
        {
          _id: { $in: notificationIds },
          'recipients.userId': req.user.userId,
          'recipients.readAt': { $exists: false }
        },
        {
          $set: { 'recipients.$.readAt': new Date() }
        }
      );

      res.json({
        success: true,
        message: `已标记 ${result.modifiedCount} 条通知为已读`
      });
    } catch (error) {
      console.error('批量标记已读错误:', error);
      res.status(500).json({
        success: false,
        message: '批量标记已读失败',
        error: error.message
      });
    }
  }

  // 删除通知
  async deleteNotification(req, res) {
    try {
      const { notificationId } = req.params;

      const notification = await Notification.findById(notificationId);
      if (!notification) {
        return res.status(404).json({
          success: false,
          message: '通知不存在'
        });
      }

      // 只有创建者可以删除通知
      if (notification.createdBy.toString() !== req.user.userId) {
        return res.status(403).json({
          success: false,
          message: '无权限删除此通知'
        });
      }

      await Notification.findByIdAndDelete(notificationId);

      res.json({
        success: true,
        message: '通知删除成功'
      });
    } catch (error) {
      console.error('删除通知错误:', error);
      res.status(500).json({
        success: false,
        message: '删除通知失败',
        error: error.message
      });
    }
  }

  // 获取未读通知数量
  async getUnreadCount(req, res) {
    try {
      const count = await Notification.countDocuments({
        'recipients.userId': req.user.userId,
        'recipients.readAt': { $exists: false }
      });

      res.json({
        success: true,
        data: { count }
      });
    } catch (error) {
      console.error('获取未读通知数量错误:', error);
      res.status(500).json({
        success: false,
        message: '获取未读通知数量失败',
        error: error.message
      });
    }
  }

  // 获取通知模板列表
  async getTemplates(req, res) {
    try {
      const templates = await NotificationTemplate.find({ status: 'active' })
        .select('code name type level description')
        .sort({ category: 1, name: 1 });

      res.json({
        success: true,
        data: templates
      });
    } catch (error) {
      console.error('获取通知模板列表错误:', error);
      res.status(500).json({
        success: false,
        message: '获取通知模板列表失败',
        error: error.message
      });
    }
  }

  // 创建通知模板
  async createTemplate(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '数据验证失败',
          errors: errors.array()
        });
      }

      // 检查模板代码是否已存在
      const existingTemplate = await NotificationTemplate.findOne({ 
        code: req.body.code 
      });

      if (existingTemplate) {
        return res.status(400).json({
          success: false,
          message: '模板代码已存在'
        });
      }

      const template = new NotificationTemplate({
        ...req.body,
        createdBy: req.user.userId
      });

      await template.save();

      res.status(201).json({
        success: true,
        message: '通知模板创建成功',
        data: template
      });
    } catch (error) {
      console.error('创建通知模板错误:', error);
      res.status(500).json({
        success: false,
        message: '创建通知模板失败',
        error: error.message
      });
    }
  }

  // 获取通知统计
  async getNotificationStats(req, res) {
    try {
      const userId = req.user.userId;

      const stats = await Notification.aggregate([
        {
          $match: {
            'recipients.userId': userId
          }
        },
        {
          $group: {
            _id: null,
            totalNotifications: { $sum: 1 },
            unreadNotifications: {
              $sum: {
                $cond: [
                  {
                    $eq: [
                      {
                        $size: {
                          $filter: {
                            input: '$recipients',
                            cond: {
                              $and: [
                                { $eq: ['$$this.userId', userId] },
                                { $not: { $ifNull: ['$$this.readAt', false] } }
                              ]
                            }
                          }
                        }
                      },
                      1
                    ]
                  },
                  1,
                  0
                ]
              }
            }
          }
        }
      ]);

      const typeStats = await Notification.aggregate([
        {
          $match: {
            'recipients.userId': userId
          }
        },
        {
          $group: {
            _id: '$type',
            count: { $sum: 1 }
          }
        },
        { $sort: { count: -1 } }
      ]);

      res.json({
        success: true,
        data: {
          overview: stats[0] || {
            totalNotifications: 0,
            unreadNotifications: 0
          },
          typeStats
        }
      });
    } catch (error) {
      console.error('获取通知统计错误:', error);
      res.status(500).json({
        success: false,
        message: '获取通知统计失败',
        error: error.message
      });
    }
  }
}

module.exports = new NotificationController();
