const Report = require('../models/Report');
const ReportTemplate = require('../models/ReportTemplate');
const reportService = require('../services/reportService');
const { validationResult } = require('express-validator');
const path = require('path');
const fs = require('fs');

class ReportController {
  // 获取报表模板列表
  async getTemplates(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        });
      }

      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      // 构建查询条件
      const query = {};
      
      if (req.query.category) {
        query.category = req.query.category;
      }
      
      if (req.query.status) {
        query.status = req.query.status;
      }
      
      if (req.query.search) {
        query.$or = [
          { name: { $regex: req.query.search, $options: 'i' } },
          { description: { $regex: req.query.search, $options: 'i' } }
        ];
      }

      const templates = await ReportTemplate.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await ReportTemplate.countDocuments(query);

      res.json({
        success: true,
        data: {
          templates,
          pagination: {
            current: page,
            pageSize: limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });
    } catch (error) {
      console.error('获取报表模板列表错误:', error);
      res.status(500).json({
        success: false,
        message: '获取报表模板列表失败',
        error: error.message
      });
    }
  }

  // 获取报表模板详情
  async getTemplateById(req, res) {
    try {
      const template = await ReportTemplate.findById(req.params.id);

      if (!template) {
        return res.status(404).json({
          success: false,
          message: '报表模板不存在'
        });
      }

      res.json({
        success: true,
        data: template
      });
    } catch (error) {
      console.error('获取报表模板详情错误:', error);
      res.status(500).json({
        success: false,
        message: '获取报表模板详情失败',
        error: error.message
      });
    }
  }

  // 创建报表模板
  async createTemplate(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '数据验证失败',
          errors: errors.array()
        });
      }

      const template = new ReportTemplate({
        ...req.body,
        createdBy: req.user.userId
      });

      await template.save();

      res.status(201).json({
        success: true,
        message: '报表模板创建成功',
        data: template
      });
    } catch (error) {
      console.error('创建报表模板错误:', error);
      res.status(500).json({
        success: false,
        message: '创建报表模板失败',
        error: error.message
      });
    }
  }

  // 更新报表模板
  async updateTemplate(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '数据验证失败',
          errors: errors.array()
        });
      }

      const templateId = req.params.id;

      const template = await ReportTemplate.findByIdAndUpdate(
        templateId,
        { ...req.body, updatedAt: new Date() },
        { new: true, runValidators: true }
      );

      if (!template) {
        return res.status(404).json({
          success: false,
          message: '报表模板不存在'
        });
      }

      res.json({
        success: true,
        message: '报表模板更新成功',
        data: template
      });
    } catch (error) {
      console.error('更新报表模板错误:', error);
      res.status(500).json({
        success: false,
        message: '更新报表模板失败',
        error: error.message
      });
    }
  }

  // 删除报表模板
  async deleteTemplate(req, res) {
    try {
      const templateId = req.params.id;

      // 检查是否有关联的报表
      const reportCount = await Report.countDocuments({ template: templateId });
      if (reportCount > 0) {
        return res.status(400).json({
          success: false,
          message: '该模板下还有报表记录，无法删除'
        });
      }

      const template = await ReportTemplate.findByIdAndDelete(templateId);
      if (!template) {
        return res.status(404).json({
          success: false,
          message: '报表模板不存在'
        });
      }

      res.json({
        success: true,
        message: '报表模板删除成功'
      });
    } catch (error) {
      console.error('删除报表模板错误:', error);
      res.status(500).json({
        success: false,
        message: '删除报表模板失败',
        error: error.message
      });
    }
  }

  // 生成报表
  async generateReport(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '数据验证失败',
          errors: errors.array()
        });
      }

      const { templateId, parameters = {}, name, title, description } = req.body;

      const report = await reportService.generateReport(
        templateId,
        parameters,
        req.user.userId,
        {
          name,
          title,
          description,
          generationType: 'manual'
        }
      );

      res.status(201).json({
        success: true,
        message: '报表生成任务已创建',
        data: report
      });
    } catch (error) {
      console.error('生成报表错误:', error);
      res.status(500).json({
        success: false,
        message: '生成报表失败',
        error: error.message
      });
    }
  }

  // 获取报表列表
  async getReports(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        });
      }

      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      // 构建查询条件
      const query = {};
      
      if (req.query.template) {
        query.template = req.query.template;
      }
      
      if (req.query.status) {
        query.status = req.query.status;
      }
      
      if (req.query.generationType) {
        query.generationType = req.query.generationType;
      }
      
      if (req.query.search) {
        query.$or = [
          { name: { $regex: req.query.search, $options: 'i' } },
          { title: { $regex: req.query.search, $options: 'i' } },
          { description: { $regex: req.query.search, $options: 'i' } }
        ];
      }

      const reports = await Report.find(query)
        .populate('template', 'name category')
        .populate('createdBy', 'name username')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await Report.countDocuments(query);

      res.json({
        success: true,
        data: {
          reports,
          pagination: {
            current: page,
            pageSize: limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });
    } catch (error) {
      console.error('获取报表列表错误:', error);
      res.status(500).json({
        success: false,
        message: '获取报表列表失败',
        error: error.message
      });
    }
  }

  // 获取报表详情
  async getReportById(req, res) {
    try {
      const report = await Report.findById(req.params.id)
        .populate('template', 'name category description')
        .populate('createdBy', 'name username');

      if (!report) {
        return res.status(404).json({
          success: false,
          message: '报表不存在'
        });
      }

      res.json({
        success: true,
        data: report
      });
    } catch (error) {
      console.error('获取报表详情错误:', error);
      res.status(500).json({
        success: false,
        message: '获取报表详情失败',
        error: error.message
      });
    }
  }

  // 下载报表
  async downloadReport(req, res) {
    try {
      const { reportId } = req.params;
      const { format = 'pdf' } = req.query;

      const report = await Report.findById(reportId);
      if (!report) {
        return res.status(404).json({
          success: false,
          message: '报表不存在'
        });
      }

      if (report.status !== 'completed') {
        return res.status(400).json({
          success: false,
          message: '报表尚未生成完成'
        });
      }

      const filePath = report.files.find(file => file.format === format)?.path;
      if (!filePath || !fs.existsSync(filePath)) {
        return res.status(404).json({
          success: false,
          message: '报表文件不存在'
        });
      }

      const fileName = path.basename(filePath);
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      
      // 设置正确的Content-Type
      const contentTypes = {
        pdf: 'application/pdf',
        excel: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        word: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        html: 'text/html'
      };
      res.setHeader('Content-Type', contentTypes[format] || 'application/octet-stream');

      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);
    } catch (error) {
      console.error('下载报表错误:', error);
      res.status(500).json({
        success: false,
        message: '下载报表失败',
        error: error.message
      });
    }
  }

  // 预览报表
  async previewReport(req, res) {
    try {
      const { reportId } = req.params;

      const report = await Report.findById(reportId);
      if (!report) {
        return res.status(404).json({
          success: false,
          message: '报表不存在'
        });
      }

      if (report.status !== 'completed') {
        return res.status(400).json({
          success: false,
          message: '报表尚未生成完成'
        });
      }

      const previewUrl = await reportService.getPreviewUrl(reportId);

      res.json({
        success: true,
        data: {
          previewUrl,
          report: {
            id: report._id,
            name: report.name,
            title: report.title,
            status: report.status
          }
        }
      });
    } catch (error) {
      console.error('预览报表错误:', error);
      res.status(500).json({
        success: false,
        message: '预览报表失败',
        error: error.message
      });
    }
  }

  // 删除报表
  async deleteReport(req, res) {
    try {
      const reportId = req.params.id;

      const report = await Report.findById(reportId);
      if (!report) {
        return res.status(404).json({
          success: false,
          message: '报表不存在'
        });
      }

      // 删除报表文件
      if (report.files && report.files.length > 0) {
        report.files.forEach(file => {
          if (fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
          }
        });
      }

      await Report.findByIdAndDelete(reportId);

      res.json({
        success: true,
        message: '报表删除成功'
      });
    } catch (error) {
      console.error('删除报表错误:', error);
      res.status(500).json({
        success: false,
        message: '删除报表失败',
        error: error.message
      });
    }
  }

  // 获取报表统计
  async getReportStats(req, res) {
    try {
      const stats = await Report.aggregate([
        {
          $group: {
            _id: null,
            totalReports: { $sum: 1 },
            completedReports: {
              $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
            },
            processingReports: {
              $sum: { $cond: [{ $eq: ['$status', 'processing'] }, 1, 0] }
            },
            failedReports: {
              $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
            }
          }
        }
      ]);

      const templateStats = await Report.aggregate([
        {
          $lookup: {
            from: 'reporttemplates',
            localField: 'template',
            foreignField: '_id',
            as: 'templateInfo'
          }
        },
        {
          $unwind: '$templateInfo'
        },
        {
          $group: {
            _id: '$templateInfo.category',
            count: { $sum: 1 }
          }
        },
        { $sort: { count: -1 } }
      ]);

      res.json({
        success: true,
        data: {
          overview: stats[0] || {
            totalReports: 0,
            completedReports: 0,
            processingReports: 0,
            failedReports: 0
          },
          templateStats
        }
      });
    } catch (error) {
      console.error('获取报表统计错误:', error);
      res.status(500).json({
        success: false,
        message: '获取报表统计失败',
        error: error.message
      });
    }
  }
}

module.exports = new ReportController();
