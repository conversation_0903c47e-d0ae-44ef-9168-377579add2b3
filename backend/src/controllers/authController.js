const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
const { validationResult } = require('express-validator');

class AuthController {
  // 用户注册
  async register(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '数据验证失败',
          errors: errors.array()
        });
      }

      const { username, email, password, name, phone, role, department } = req.body;

      // 检查用户是否已存在
      const existingUser = await User.findOne({
        $or: [{ username }, { email }]
      });

      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: '用户名或邮箱已存在'
        });
      }

      // 创建新用户
      const user = new User({
        username,
        email,
        password,
        name,
        phone,
        role: role || 'worker',
        department
      });

      await user.save();

      // 生成JWT令牌
      const token = this.generateToken(user._id);

      res.status(201).json({
        success: true,
        message: '注册成功',
        data: {
          user: this.sanitizeUser(user),
          token
        }
      });
    } catch (error) {
      console.error('注册错误:', error);
      res.status(500).json({
        success: false,
        message: '注册失败',
        error: error.message
      });
    }
  }

  // 用户登录
  async login(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '数据验证失败',
          errors: errors.array()
        });
      }

      const { username, password } = req.body;

      // 查找用户（支持用户名或邮箱登录）
      const user = await User.findOne({
        $or: [{ username }, { email: username }],
        status: 'active'
      });

      if (!user) {
        return res.status(401).json({
          success: false,
          message: '用户名或密码错误'
        });
      }

      // 验证密码
      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        return res.status(401).json({
          success: false,
          message: '用户名或密码错误'
        });
      }

      // 更新最后登录时间
      user.lastLoginAt = new Date();
      await user.save();

      // 生成JWT令牌
      const token = this.generateToken(user._id);

      res.json({
        success: true,
        message: '登录成功',
        data: {
          user: this.sanitizeUser(user),
          token
        }
      });
    } catch (error) {
      console.error('登录错误:', error);
      res.status(500).json({
        success: false,
        message: '登录失败',
        error: error.message
      });
    }
  }

  // 获取当前用户信息
  async getCurrentUser(req, res) {
    try {
      const user = await User.findById(req.user.userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      res.json({
        success: true,
        data: this.sanitizeUser(user)
      });
    } catch (error) {
      console.error('获取用户信息错误:', error);
      res.status(500).json({
        success: false,
        message: '获取用户信息失败',
        error: error.message
      });
    }
  }

  // 更新用户信息
  async updateProfile(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '数据验证失败',
          errors: errors.array()
        });
      }

      const { name, phone, email, department, avatar } = req.body;
      const userId = req.user.userId;

      // 检查邮箱是否被其他用户使用
      if (email) {
        const existingUser = await User.findOne({
          email,
          _id: { $ne: userId }
        });

        if (existingUser) {
          return res.status(400).json({
            success: false,
            message: '邮箱已被其他用户使用'
          });
        }
      }

      const updateData = {};
      if (name) updateData.name = name;
      if (phone) updateData.phone = phone;
      if (email) updateData.email = email;
      if (department) updateData.department = department;
      if (avatar) updateData.avatar = avatar;

      const user = await User.findByIdAndUpdate(
        userId,
        updateData,
        { new: true, runValidators: true }
      );

      res.json({
        success: true,
        message: '更新成功',
        data: this.sanitizeUser(user)
      });
    } catch (error) {
      console.error('更新用户信息错误:', error);
      res.status(500).json({
        success: false,
        message: '更新用户信息失败',
        error: error.message
      });
    }
  }

  // 修改密码
  async changePassword(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '数据验证失败',
          errors: errors.array()
        });
      }

      const { oldPassword, newPassword } = req.body;
      const userId = req.user.userId;

      const user = await User.findById(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      // 验证旧密码
      const isOldPasswordValid = await user.comparePassword(oldPassword);
      if (!isOldPasswordValid) {
        return res.status(400).json({
          success: false,
          message: '原密码错误'
        });
      }

      // 更新密码
      user.password = newPassword;
      await user.save();

      res.json({
        success: true,
        message: '密码修改成功'
      });
    } catch (error) {
      console.error('修改密码错误:', error);
      res.status(500).json({
        success: false,
        message: '修改密码失败',
        error: error.message
      });
    }
  }

  // 生成JWT令牌
  generateToken(userId) {
    return jwt.sign(
      { userId },
      process.env.JWT_SECRET || 'fallback_secret',
      { expiresIn: process.env.JWT_EXPIRE || '7d' }
    );
  }

  // 清理用户敏感信息
  sanitizeUser(user) {
    const userObj = user.toObject();
    delete userObj.password;
    return userObj;
  }
}

module.exports = new AuthController();
