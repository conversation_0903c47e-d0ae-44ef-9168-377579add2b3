const EventEmitter = require('events');
const path = require('path');
const fs = require('fs');
const ExcelJS = require('exceljs');
const PDFDocument = require('pdfkit');
const puppeteer = require('puppeteer');
const ReportTemplate = require('../models/ReportTemplate');
const Report = require('../models/Report');
const mongoose = require('mongoose');

class ReportService extends EventEmitter {
  constructor() {
    super();
    this.isRunning = false;
    this.activeJobs = new Map();
    this.config = {
      outputPath: process.env.REPORT_OUTPUT_PATH || './storage/reports',
      tempPath: process.env.REPORT_TEMP_PATH || './storage/temp/reports',
      maxConcurrentJobs: parseInt(process.env.MAX_CONCURRENT_REPORTS) || 5,
      jobTimeout: parseInt(process.env.REPORT_JOB_TIMEOUT) || 300000, // 5分钟
      retentionDays: parseInt(process.env.REPORT_RETENTION_DAYS) || 30
    };
    
    this.ensureDirectories();
  }

  // 启动报表服务
  async start() {
    try {
      console.log('启动报表生成服务...');
      this.isRunning = true;
      
      // 启动定时任务检查
      this.startScheduleChecker();
      
      // 启动清理任务
      this.startCleanupTask();
      
      console.log('报表生成服务启动成功');
      this.emit('service_started');
      
    } catch (error) {
      console.error('报表生成服务启动失败:', error);
      throw error;
    }
  }

  // 停止报表服务
  async stop() {
    try {
      console.log('停止报表生成服务...');
      this.isRunning = false;
      
      // 取消所有活跃任务
      for (const [jobId, job] of this.activeJobs) {
        await this.cancelJob(jobId);
      }
      
      // 清理定时器
      if (this.scheduleTimer) {
        clearInterval(this.scheduleTimer);
      }
      
      if (this.cleanupTimer) {
        clearInterval(this.cleanupTimer);
      }
      
      console.log('报表生成服务已停止');
      this.emit('service_stopped');
      
    } catch (error) {
      console.error('报表生成服务停止失败:', error);
    }
  }

  // 确保目录存在
  ensureDirectories() {
    const dirs = [
      this.config.outputPath,
      this.config.tempPath,
      path.join(this.config.outputPath, 'pdf'),
      path.join(this.config.outputPath, 'excel'),
      path.join(this.config.outputPath, 'word'),
      path.join(this.config.outputPath, 'csv'),
      path.join(this.config.outputPath, 'html')
    ];
    
    dirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
  }

  // 生成报表
  async generateReport(templateId, parameters = {}, userId, options = {}) {
    try {
      // 检查并发限制
      if (this.activeJobs.size >= this.config.maxConcurrentJobs) {
        throw new Error('报表生成队列已满，请稍后重试');
      }

      // 获取模板
      const template = await ReportTemplate.findById(templateId);
      if (!template) {
        throw new Error('报表模板不存在');
      }

      // 创建报表实例
      const report = new Report({
        template: templateId,
        name: options.name || `${template.name}_${new Date().toISOString().split('T')[0]}`,
        title: options.title || template.name,
        description: options.description,
        parameters: parameters,
        generationType: options.generationType || 'manual',
        priority: options.priority || 'normal',
        createdBy: userId
      });

      await report.save();

      // 启动生成任务
      const jobId = report._id.toString();
      this.startGenerationJob(jobId, template, report, parameters);

      return report;

    } catch (error) {
      console.error('生成报表失败:', error);
      throw error;
    }
  }

  // 启动生成任务
  async startGenerationJob(jobId, template, report, parameters) {
    try {
      // 添加到活跃任务
      this.activeJobs.set(jobId, {
        report: report,
        template: template,
        startTime: new Date(),
        status: 'running'
      });

      // 更新报表状态
      report.status = 'generating';
      report.progress.startTime = new Date();
      await report.save();

      console.log(`开始生成报表: ${report.name}`);

      // 执行生成步骤
      await this.executeGenerationSteps(jobId, template, report, parameters);

    } catch (error) {
      console.error(`报表生成失败 ${jobId}:`, error);
      await this.handleGenerationError(jobId, error);
    }
  }

  // 执行生成步骤
  async executeGenerationSteps(jobId, template, report, parameters) {
    try {
      // 步骤1: 查询数据
      await report.updateProgress(10, '正在查询数据...');
      const startQueryTime = Date.now();
      const data = await this.queryData(template, parameters);
      const queryTime = Date.now() - startQueryTime;

      // 步骤2: 处理数据
      await report.updateProgress(30, '正在处理数据...');
      const processedData = await this.processData(template, data);

      // 步骤3: 生成输出文件
      await report.updateProgress(50, '正在生成报表文件...');
      const startRenderTime = Date.now();
      const outputs = await this.generateOutputs(template, report, processedData);
      const renderTime = Date.now() - startRenderTime;

      // 步骤4: 完成
      await report.updateProgress(100, '报表生成完成');

      // 更新性能指标
      report.performance = {
        queryTime: queryTime,
        renderTime: renderTime,
        totalTime: Date.now() - report.progress.startTime,
        memoryUsage: process.memoryUsage().heapUsed / 1024 / 1024,
        recordCount: data.length
      };

      // 保存数据和输出
      report.data = {
        rawData: data.slice(0, 1000), // 只保存前1000条用于预览
        summary: this.calculateSummary(template, processedData),
        statistics: {
          totalRecords: data.length,
          totalPages: Math.ceil(data.length / 50),
          generatedAt: new Date(),
          dataSource: template.dataSource.mainTable
        }
      };

      await report.markAsCompleted(outputs);

      // 移除活跃任务
      this.activeJobs.delete(jobId);

      console.log(`报表生成完成: ${report.name}`);
      this.emit('report_completed', { reportId: jobId, report: report });

    } catch (error) {
      throw error;
    }
  }

  // 查询数据
  async queryData(template, parameters) {
    try {
      const { dataSource } = template;
      const { dateRange, filters } = parameters;

      // 构建查询条件
      let query = {};

      // 添加日期范围过滤
      if (dateRange && dateRange.start && dateRange.end) {
        query.createdAt = {
          $gte: new Date(dateRange.start),
          $lte: new Date(dateRange.end)
        };
      }

      // 添加自定义过滤条件
      if (filters) {
        Object.assign(query, filters);
      }

      // 添加模板定义的过滤条件
      if (dataSource.filters) {
        dataSource.filters.forEach(filter => {
          if (filter.required || parameters[filter.field] !== undefined) {
            const value = parameters[filter.field] || filter.value;
            
            switch (filter.operator) {
              case '=':
                query[filter.field] = value;
                break;
              case '!=':
                query[filter.field] = { $ne: value };
                break;
              case '>':
                query[filter.field] = { $gt: value };
                break;
              case '<':
                query[filter.field] = { $lt: value };
                break;
              case '>=':
                query[filter.field] = { $gte: value };
                break;
              case '<=':
                query[filter.field] = { $lte: value };
                break;
              case 'like':
                query[filter.field] = { $regex: value, $options: 'i' };
                break;
              case 'in':
                query[filter.field] = { $in: Array.isArray(value) ? value : [value] };
                break;
              case 'between':
                if (Array.isArray(value) && value.length === 2) {
                  query[filter.field] = { $gte: value[0], $lte: value[1] };
                }
                break;
            }
          }
        });
      }

      // 执行查询
      const Model = mongoose.model(dataSource.mainTable);
      let queryBuilder = Model.find(query);

      // 添加关联查询
      if (dataSource.joinTables) {
        dataSource.joinTables.forEach(join => {
          queryBuilder = queryBuilder.populate(join.table);
        });
      }

      // 添加排序
      if (template.orderBy && template.orderBy.length > 0) {
        const sort = {};
        template.orderBy.forEach(order => {
          sort[order.field] = order.direction === 'desc' ? -1 : 1;
        });
        queryBuilder = queryBuilder.sort(sort);
      }

      const data = await queryBuilder.exec();
      return data;

    } catch (error) {
      console.error('查询数据失败:', error);
      throw new Error(`数据查询失败: ${error.message}`);
    }
  }

  // 处理数据
  async processData(template, rawData) {
    try {
      const { fields, groupBy } = template;
      
      // 如果没有分组，直接返回格式化后的数据
      if (!groupBy || groupBy.length === 0) {
        return rawData.map(item => this.formatDataItem(item, fields));
      }

      // 执行分组处理
      const grouped = {};
      
      rawData.forEach(item => {
        const groupKey = groupBy.map(group => 
          this.getFieldValue(item, group.field)
        ).join('|');
        
        if (!grouped[groupKey]) {
          grouped[groupKey] = [];
        }
        
        grouped[groupKey].push(this.formatDataItem(item, fields));
      });

      // 计算聚合值
      const result = [];
      
      Object.keys(grouped).forEach(groupKey => {
        const groupData = grouped[groupKey];
        const aggregatedItem = this.calculateAggregations(groupData, fields);
        
        // 添加分组信息
        const groupValues = groupKey.split('|');
        groupBy.forEach((group, index) => {
          aggregatedItem[group.field] = groupValues[index];
        });
        
        result.push(aggregatedItem);
      });

      return result;

    } catch (error) {
      console.error('处理数据失败:', error);
      throw new Error(`数据处理失败: ${error.message}`);
    }
  }

  // 格式化数据项
  formatDataItem(item, fields) {
    const formatted = {};
    
    fields.forEach(field => {
      const value = this.getFieldValue(item, field.name);
      formatted[field.name] = this.formatFieldValue(value, field);
    });
    
    return formatted;
  }

  // 获取字段值
  getFieldValue(item, fieldName) {
    const keys = fieldName.split('.');
    let value = item;
    
    for (const key of keys) {
      if (value && typeof value === 'object') {
        value = value[key];
      } else {
        value = undefined;
        break;
      }
    }
    
    return value;
  }

  // 格式化字段值
  formatFieldValue(value, field) {
    if (value === null || value === undefined) {
      return '';
    }
    
    switch (field.dataType) {
      case 'number':
        return typeof value === 'number' ? value : parseFloat(value) || 0;
      case 'currency':
        return typeof value === 'number' ? value.toFixed(2) : '0.00';
      case 'date':
        return value instanceof Date ? value.toISOString().split('T')[0] : value;
      case 'boolean':
        return Boolean(value);
      default:
        return String(value);
    }
  }

  // 计算聚合值
  calculateAggregations(groupData, fields) {
    const result = {};
    
    fields.forEach(field => {
      if (field.aggregation === 'none') {
        // 取第一个值
        result[field.name] = groupData[0] ? groupData[0][field.name] : '';
      } else {
        const values = groupData.map(item => item[field.name]).filter(v => v !== null && v !== undefined);
        
        switch (field.aggregation) {
          case 'sum':
            result[field.name] = values.reduce((sum, val) => sum + (parseFloat(val) || 0), 0);
            break;
          case 'avg':
            result[field.name] = values.length > 0 ? 
              values.reduce((sum, val) => sum + (parseFloat(val) || 0), 0) / values.length : 0;
            break;
          case 'count':
            result[field.name] = values.length;
            break;
          case 'max':
            result[field.name] = values.length > 0 ? Math.max(...values.map(v => parseFloat(v) || 0)) : 0;
            break;
          case 'min':
            result[field.name] = values.length > 0 ? Math.min(...values.map(v => parseFloat(v) || 0)) : 0;
            break;
          default:
            result[field.name] = values[0] || '';
        }
      }
    });
    
    return result;
  }

  // 计算汇总信息
  calculateSummary(template, data) {
    const summary = {
      totalRecords: data.length,
      aggregations: {}
    };
    
    // 计算数值字段的汇总
    template.fields.forEach(field => {
      if (field.dataType === 'number' || field.dataType === 'currency') {
        const values = data.map(item => parseFloat(item[field.name]) || 0);
        
        summary.aggregations[field.name] = {
          sum: values.reduce((sum, val) => sum + val, 0),
          avg: values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0,
          max: values.length > 0 ? Math.max(...values) : 0,
          min: values.length > 0 ? Math.min(...values) : 0,
          count: values.length
        };
      }
    });
    
    return summary;
  }

  // 生成输出文件
  async generateOutputs(template, report, data) {
    const outputs = [];
    
    for (const format of template.outputFormats) {
      try {
        let output;
        
        switch (format.type) {
          case 'excel':
            output = await this.generateExcel(template, report, data);
            break;
          case 'pdf':
            output = await this.generatePDF(template, report, data);
            break;
          case 'csv':
            output = await this.generateCSV(template, report, data);
            break;
          case 'html':
            output = await this.generateHTML(template, report, data);
            break;
          default:
            continue;
        }
        
        if (output) {
          outputs.push(output);
        }
        
      } catch (error) {
        console.error(`生成${format.type}格式失败:`, error);
        // 继续生成其他格式
      }
    }
    
    return outputs;
  }

  // 生成Excel文件
  async generateExcel(template, report, data) {
    try {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet(template.name);
      
      // 设置列
      const columns = template.getFieldsForDisplay().map(field => ({
        header: field.label,
        key: field.name,
        width: field.width / 10 || 15
      }));
      
      worksheet.columns = columns;
      
      // 添加数据
      data.forEach(item => {
        worksheet.addRow(item);
      });
      
      // 设置样式
      worksheet.getRow(1).font = { bold: true };
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };
      
      // 保存文件
      const filename = `${report.name}_${Date.now()}.xlsx`;
      const filePath = path.join(this.config.outputPath, 'excel', filename);
      
      await workbook.xlsx.writeFile(filePath);
      
      const stats = fs.statSync(filePath);
      
      return {
        format: 'excel',
        filename: filename,
        filePath: filePath,
        fileSize: stats.size,
        downloadUrl: `/api/reports/${report._id}/download/excel`,
        generatedAt: new Date()
      };
      
    } catch (error) {
      console.error('生成Excel失败:', error);
      throw error;
    }
  }

  // 生成PDF文件
  async generatePDF(template, report, data) {
    try {
      const filename = `${report.name}_${Date.now()}.pdf`;
      const filePath = path.join(this.config.outputPath, 'pdf', filename);
      
      const doc = new PDFDocument();
      doc.pipe(fs.createWriteStream(filePath));
      
      // 添加标题
      doc.fontSize(18).text(report.title, { align: 'center' });
      doc.moveDown();
      
      // 添加生成时间
      doc.fontSize(10).text(`生成时间: ${new Date().toLocaleString()}`, { align: 'right' });
      doc.moveDown();
      
      // 添加表格
      const fields = template.getFieldsForDisplay();
      const tableTop = doc.y;
      const itemHeight = 20;
      
      // 表头
      let x = 50;
      fields.forEach(field => {
        doc.fontSize(10).text(field.label, x, tableTop, { width: field.width || 100 });
        x += field.width || 100;
      });
      
      // 数据行
      let y = tableTop + itemHeight;
      data.slice(0, 50).forEach(item => { // 限制PDF中的行数
        x = 50;
        fields.forEach(field => {
          const value = item[field.name] || '';
          doc.fontSize(9).text(String(value), x, y, { width: field.width || 100 });
          x += field.width || 100;
        });
        y += itemHeight;
        
        if (y > 700) { // 换页
          doc.addPage();
          y = 50;
        }
      });
      
      doc.end();
      
      // 等待文件写入完成
      await new Promise((resolve) => {
        doc.on('end', resolve);
      });
      
      const stats = fs.statSync(filePath);
      
      return {
        format: 'pdf',
        filename: filename,
        filePath: filePath,
        fileSize: stats.size,
        downloadUrl: `/api/reports/${report._id}/download/pdf`,
        generatedAt: new Date()
      };
      
    } catch (error) {
      console.error('生成PDF失败:', error);
      throw error;
    }
  }

  // 生成CSV文件
  async generateCSV(template, report, data) {
    try {
      const fields = template.getFieldsForDisplay();
      
      // 构建CSV内容
      let csvContent = '';
      
      // 添加表头
      csvContent += fields.map(field => `"${field.label}"`).join(',') + '\n';
      
      // 添加数据
      data.forEach(item => {
        const row = fields.map(field => {
          const value = item[field.name] || '';
          return `"${String(value).replace(/"/g, '""')}"`;
        }).join(',');
        csvContent += row + '\n';
      });
      
      // 保存文件
      const filename = `${report.name}_${Date.now()}.csv`;
      const filePath = path.join(this.config.outputPath, 'csv', filename);
      
      fs.writeFileSync(filePath, csvContent, 'utf8');
      
      const stats = fs.statSync(filePath);
      
      return {
        format: 'csv',
        filename: filename,
        filePath: filePath,
        fileSize: stats.size,
        downloadUrl: `/api/reports/${report._id}/download/csv`,
        generatedAt: new Date()
      };
      
    } catch (error) {
      console.error('生成CSV失败:', error);
      throw error;
    }
  }

  // 生成HTML文件
  async generateHTML(template, report, data) {
    try {
      const fields = template.getFieldsForDisplay();
      
      // 构建HTML内容
      let htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>${report.title}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { color: #333; text-align: center; }
            .info { text-align: right; color: #666; margin-bottom: 20px; }
            table { width: 100%; border-collapse: collapse; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            tr:nth-child(even) { background-color: #f9f9f9; }
          </style>
        </head>
        <body>
          <h1>${report.title}</h1>
          <div class="info">生成时间: ${new Date().toLocaleString()}</div>
          <table>
            <thead>
              <tr>
                ${fields.map(field => `<th>${field.label}</th>`).join('')}
              </tr>
            </thead>
            <tbody>
              ${data.map(item => `
                <tr>
                  ${fields.map(field => `<td>${item[field.name] || ''}</td>`).join('')}
                </tr>
              `).join('')}
            </tbody>
          </table>
        </body>
        </html>
      `;
      
      // 保存文件
      const filename = `${report.name}_${Date.now()}.html`;
      const filePath = path.join(this.config.outputPath, 'html', filename);
      
      fs.writeFileSync(filePath, htmlContent, 'utf8');
      
      const stats = fs.statSync(filePath);
      
      return {
        format: 'html',
        filename: filename,
        filePath: filePath,
        fileSize: stats.size,
        downloadUrl: `/api/reports/${report._id}/download/html`,
        generatedAt: new Date()
      };
      
    } catch (error) {
      console.error('生成HTML失败:', error);
      throw error;
    }
  }

  // 处理生成错误
  async handleGenerationError(jobId, error) {
    try {
      const job = this.activeJobs.get(jobId);
      if (job && job.report) {
        await job.report.markAsFailed(error);
      }
      
      this.activeJobs.delete(jobId);
      
      console.error(`报表生成失败 ${jobId}:`, error);
      this.emit('report_failed', { reportId: jobId, error: error });
      
    } catch (err) {
      console.error('处理生成错误失败:', err);
    }
  }

  // 取消任务
  async cancelJob(jobId) {
    try {
      const job = this.activeJobs.get(jobId);
      if (job) {
        job.status = 'cancelled';
        
        if (job.report) {
          job.report.status = 'cancelled';
          await job.report.save();
        }
        
        this.activeJobs.delete(jobId);
        console.log(`报表生成任务已取消: ${jobId}`);
      }
    } catch (error) {
      console.error('取消任务失败:', error);
    }
  }

  // 启动定时任务检查
  startScheduleChecker() {
    this.scheduleTimer = setInterval(async () => {
      if (!this.isRunning) return;
      
      try {
        const scheduledTemplates = await ReportTemplate.getScheduledTemplates();
        
        for (const template of scheduledTemplates) {
          await this.executeScheduledReport(template);
        }
        
      } catch (error) {
        console.error('定时任务检查失败:', error);
      }
    }, 60000); // 每分钟检查一次
  }

  // 执行定时报表
  async executeScheduledReport(template) {
    try {
      console.log(`执行定时报表: ${template.name}`);
      
      // 生成报表
      await this.generateReport(
        template._id,
        {},
        template.createdBy,
        {
          generationType: 'scheduled',
          name: `${template.name}_${new Date().toISOString().split('T')[0]}`,
          title: template.name
        }
      );
      
      // 更新下次执行时间
      await template.updateNextRun();
      
    } catch (error) {
      console.error(`执行定时报表失败 ${template.name}:`, error);
    }
  }

  // 启动清理任务
  startCleanupTask() {
    this.cleanupTimer = setInterval(async () => {
      if (!this.isRunning) return;
      
      try {
        await this.cleanupExpiredReports();
      } catch (error) {
        console.error('清理过期报表失败:', error);
      }
    }, 24 * 60 * 60 * 1000); // 每天执行一次
  }

  // 清理过期报表
  async cleanupExpiredReports() {
    try {
      const expiredReports = await Report.getReportsForCleanup();
      
      for (const report of expiredReports) {
        // 删除文件
        report.outputs.forEach(output => {
          if (output.filePath && fs.existsSync(output.filePath)) {
            fs.unlinkSync(output.filePath);
          }
        });
        
        // 删除记录
        await Report.findByIdAndDelete(report._id);
      }
      
      console.log(`清理了 ${expiredReports.length} 个过期报表`);
      
    } catch (error) {
      console.error('清理过期报表失败:', error);
    }
  }

  // 获取服务状态
  getServiceStatus() {
    return {
      isRunning: this.isRunning,
      activeJobs: this.activeJobs.size,
      maxConcurrentJobs: this.config.maxConcurrentJobs,
      config: this.config
    };
  }

  // 获取任务状态
  getJobStatus(jobId) {
    return this.activeJobs.get(jobId) || null;
  }
}

module.exports = new ReportService();
