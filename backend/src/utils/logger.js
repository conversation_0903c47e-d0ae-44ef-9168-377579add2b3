/**
 * 日志工具
 */

const fs = require('fs');
const path = require('path');
const moment = require('moment');

class Logger {
  constructor() {
    this.logDir = path.join(process.cwd(), 'logs');
    this.ensureLogDir();
  }

  /**
   * 确保日志目录存在
   */
  ensureLogDir() {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  /**
   * 获取日志文件路径
   * @param {string} level 日志级别
   * @returns {string}
   */
  getLogFilePath(level) {
    const date = moment().format('YYYY-MM-DD');
    return path.join(this.logDir, `${level}-${date}.log`);
  }

  /**
   * 格式化日志消息
   * @param {string} level 日志级别
   * @param {string} message 消息
   * @param {object} meta 元数据
   * @returns {string}
   */
  formatMessage(level, message, meta = {}) {
    const timestamp = moment().format('YYYY-MM-DD HH:mm:ss');
    const metaStr = Object.keys(meta).length > 0 ? ` | ${JSON.stringify(meta)}` : '';
    return `[${timestamp}] [${level.toUpperCase()}] ${message}${metaStr}\n`;
  }

  /**
   * 写入日志文件
   * @param {string} level 日志级别
   * @param {string} message 消息
   * @param {object} meta 元数据
   */
  writeToFile(level, message, meta = {}) {
    try {
      const logFile = this.getLogFilePath(level);
      const formattedMessage = this.formatMessage(level, message, meta);
      fs.appendFileSync(logFile, formattedMessage);
    } catch (error) {
      console.error('写入日志文件失败:', error);
    }
  }

  /**
   * 输出到控制台
   * @param {string} level 日志级别
   * @param {string} message 消息
   * @param {object} meta 元数据
   */
  logToConsole(level, message, meta = {}) {
    const timestamp = moment().format('YYYY-MM-DD HH:mm:ss');
    const colors = {
      error: '\x1b[31m',   // 红色
      warn: '\x1b[33m',    // 黄色
      info: '\x1b[36m',    // 青色
      debug: '\x1b[35m',   // 紫色
      reset: '\x1b[0m'     // 重置
    };

    const color = colors[level] || colors.reset;
    const resetColor = colors.reset;
    
    console.log(`${color}[${timestamp}] [${level.toUpperCase()}] ${message}${resetColor}`);
    
    if (Object.keys(meta).length > 0) {
      console.log(`${color}Meta:${resetColor}`, meta);
    }
  }

  /**
   * 记录错误日志
   * @param {string} message 消息
   * @param {object} meta 元数据
   */
  error(message, meta = {}) {
    this.logToConsole('error', message, meta);
    this.writeToFile('error', message, meta);
  }

  /**
   * 记录警告日志
   * @param {string} message 消息
   * @param {object} meta 元数据
   */
  warn(message, meta = {}) {
    this.logToConsole('warn', message, meta);
    this.writeToFile('warn', message, meta);
  }

  /**
   * 记录信息日志
   * @param {string} message 消息
   * @param {object} meta 元数据
   */
  info(message, meta = {}) {
    this.logToConsole('info', message, meta);
    this.writeToFile('info', message, meta);
  }

  /**
   * 记录调试日志
   * @param {string} message 消息
   * @param {object} meta 元数据
   */
  debug(message, meta = {}) {
    if (process.env.NODE_ENV === 'development') {
      this.logToConsole('debug', message, meta);
      this.writeToFile('debug', message, meta);
    }
  }

  /**
   * 记录HTTP请求日志
   * @param {object} req 请求对象
   * @param {object} res 响应对象
   * @param {number} duration 请求耗时
   */
  logRequest(req, res, duration) {
    const message = `${req.method} ${req.originalUrl} - ${res.statusCode} - ${duration}ms`;
    const meta = {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.userId
    };

    if (res.statusCode >= 400) {
      this.error(message, meta);
    } else {
      this.info(message, meta);
    }
  }

  /**
   * 记录数据库操作日志
   * @param {string} operation 操作类型
   * @param {string} collection 集合名
   * @param {object} query 查询条件
   * @param {number} duration 耗时
   */
  logDatabase(operation, collection, query = {}, duration = 0) {
    const message = `DB ${operation} on ${collection} - ${duration}ms`;
    const meta = { query };
    this.debug(message, meta);
  }

  /**
   * 清理旧日志文件
   * @param {number} days 保留天数
   */
  cleanOldLogs(days = 30) {
    try {
      const files = fs.readdirSync(this.logDir);
      const cutoffDate = moment().subtract(days, 'days');

      files.forEach(file => {
        const filePath = path.join(this.logDir, file);
        const stats = fs.statSync(filePath);
        const fileDate = moment(stats.mtime);

        if (fileDate.isBefore(cutoffDate)) {
          fs.unlinkSync(filePath);
          this.info(`删除旧日志文件: ${file}`);
        }
      });
    } catch (error) {
      this.error('清理旧日志文件失败', { error: error.message });
    }
  }

  /**
   * 获取日志统计信息
   * @returns {object}
   */
  getLogStats() {
    try {
      const files = fs.readdirSync(this.logDir);
      const stats = {
        totalFiles: files.length,
        totalSize: 0,
        filesByLevel: {}
      };

      files.forEach(file => {
        const filePath = path.join(this.logDir, file);
        const fileStats = fs.statSync(filePath);
        stats.totalSize += fileStats.size;

        const level = file.split('-')[0];
        if (!stats.filesByLevel[level]) {
          stats.filesByLevel[level] = 0;
        }
        stats.filesByLevel[level]++;
      });

      return stats;
    } catch (error) {
      this.error('获取日志统计失败', { error: error.message });
      return null;
    }
  }
}

// 创建全局日志实例
const logger = new Logger();

// 定期清理旧日志（每天执行一次）
setInterval(() => {
  logger.cleanOldLogs();
}, 24 * 60 * 60 * 1000);

module.exports = logger;
