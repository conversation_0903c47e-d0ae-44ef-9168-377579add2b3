/**
 * 工具函数集合
 */

const crypto = require('crypto');
const moment = require('moment');

/**
 * 生成随机字符串
 * @param {number} length 长度
 * @param {string} charset 字符集
 * @returns {string}
 */
const generateRandomString = (length = 8, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') => {
  let result = '';
  for (let i = 0; i < length; i++) {
    result += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return result;
};

/**
 * 生成唯一ID
 * @param {string} prefix 前缀
 * @returns {string}
 */
const generateUniqueId = (prefix = '') => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 5);
  return `${prefix}${timestamp}${random}`.toUpperCase();
};

/**
 * 格式化日期
 * @param {Date|string} date 日期
 * @param {string} format 格式
 * @returns {string}
 */
const formatDate = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
  return moment(date).format(format);
};

/**
 * 计算两个日期之间的天数
 * @param {Date|string} startDate 开始日期
 * @param {Date|string} endDate 结束日期
 * @returns {number}
 */
const daysBetween = (startDate, endDate) => {
  return moment(endDate).diff(moment(startDate), 'days');
};

/**
 * 验证邮箱格式
 * @param {string} email 邮箱
 * @returns {boolean}
 */
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * 验证手机号格式
 * @param {string} phone 手机号
 * @returns {boolean}
 */
const isValidPhone = (phone) => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

/**
 * 深度克隆对象
 * @param {any} obj 要克隆的对象
 * @returns {any}
 */
const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  if (typeof obj === 'object') {
    const clonedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
};

/**
 * 移除对象中的空值
 * @param {object} obj 对象
 * @returns {object}
 */
const removeEmptyValues = (obj) => {
  const result = {};
  for (const key in obj) {
    if (obj[key] !== null && obj[key] !== undefined && obj[key] !== '') {
      result[key] = obj[key];
    }
  }
  return result;
};

/**
 * 分页计算
 * @param {number} page 当前页
 * @param {number} limit 每页数量
 * @returns {object}
 */
const getPagination = (page = 1, limit = 10) => {
  const offset = (page - 1) * limit;
  return { offset, limit: parseInt(limit) };
};

/**
 * 计算分页信息
 * @param {number} total 总数
 * @param {number} page 当前页
 * @param {number} limit 每页数量
 * @returns {object}
 */
const getPaginationInfo = (total, page, limit) => {
  return {
    current: parseInt(page),
    pageSize: parseInt(limit),
    total: parseInt(total),
    pages: Math.ceil(total / limit)
  };
};

/**
 * 生成MD5哈希
 * @param {string} text 文本
 * @returns {string}
 */
const generateMD5 = (text) => {
  return crypto.createHash('md5').update(text).digest('hex');
};

/**
 * 生成SHA256哈希
 * @param {string} text 文本
 * @returns {string}
 */
const generateSHA256 = (text) => {
  return crypto.createHash('sha256').update(text).digest('hex');
};

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string}
 */
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 获取文件扩展名
 * @param {string} filename 文件名
 * @returns {string}
 */
const getFileExtension = (filename) => {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
};

/**
 * 生成随机颜色
 * @returns {string}
 */
const generateRandomColor = () => {
  return '#' + Math.floor(Math.random() * 16777215).toString(16);
};

/**
 * 数组去重
 * @param {Array} arr 数组
 * @returns {Array}
 */
const uniqueArray = (arr) => {
  return [...new Set(arr)];
};

/**
 * 数组分组
 * @param {Array} arr 数组
 * @param {string|Function} key 分组键或函数
 * @returns {object}
 */
const groupBy = (arr, key) => {
  return arr.reduce((groups, item) => {
    const group = typeof key === 'function' ? key(item) : item[key];
    groups[group] = groups[group] || [];
    groups[group].push(item);
    return groups;
  }, {});
};

/**
 * 延迟执行
 * @param {number} ms 毫秒
 * @returns {Promise}
 */
const sleep = (ms) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * 重试函数
 * @param {Function} fn 要重试的函数
 * @param {number} retries 重试次数
 * @param {number} delay 延迟时间
 * @returns {Promise}
 */
const retry = async (fn, retries = 3, delay = 1000) => {
  try {
    return await fn();
  } catch (error) {
    if (retries > 0) {
      await sleep(delay);
      return retry(fn, retries - 1, delay);
    }
    throw error;
  }
};

/**
 * 防抖函数
 * @param {Function} func 函数
 * @param {number} wait 等待时间
 * @returns {Function}
 */
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * 节流函数
 * @param {Function} func 函数
 * @param {number} limit 限制时间
 * @returns {Function}
 */
const throttle = (func, limit) => {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

module.exports = {
  generateRandomString,
  generateUniqueId,
  formatDate,
  daysBetween,
  isValidEmail,
  isValidPhone,
  deepClone,
  removeEmptyValues,
  getPagination,
  getPaginationInfo,
  generateMD5,
  generateSHA256,
  formatFileSize,
  getFileExtension,
  generateRandomColor,
  uniqueArray,
  groupBy,
  sleep,
  retry,
  debounce,
  throttle
};
