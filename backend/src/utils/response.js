/**
 * 统一响应格式工具
 */

class ResponseHelper {
  /**
   * 成功响应
   * @param {object} res Express响应对象
   * @param {any} data 响应数据
   * @param {string} message 响应消息
   * @param {number} code 状态码
   */
  static success(res, data = null, message = '操作成功', code = 200) {
    return res.status(code).json({
      success: true,
      message,
      data,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 失败响应
   * @param {object} res Express响应对象
   * @param {string} message 错误消息
   * @param {number} code 状态码
   * @param {any} error 错误详情
   */
  static error(res, message = '操作失败', code = 500, error = null) {
    const response = {
      success: false,
      message,
      timestamp: new Date().toISOString()
    };

    if (error && process.env.NODE_ENV === 'development') {
      response.error = error;
    }

    return res.status(code).json(response);
  }

  /**
   * 分页响应
   * @param {object} res Express响应对象
   * @param {Array} data 数据列表
   * @param {object} pagination 分页信息
   * @param {string} message 响应消息
   */
  static paginated(res, data, pagination, message = '获取成功') {
    return res.json({
      success: true,
      message,
      data: {
        list: data,
        pagination
      },
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 创建成功响应
   * @param {object} res Express响应对象
   * @param {any} data 创建的数据
   * @param {string} message 响应消息
   */
  static created(res, data, message = '创建成功') {
    return this.success(res, data, message, 201);
  }

  /**
   * 更新成功响应
   * @param {object} res Express响应对象
   * @param {any} data 更新的数据
   * @param {string} message 响应消息
   */
  static updated(res, data, message = '更新成功') {
    return this.success(res, data, message, 200);
  }

  /**
   * 删除成功响应
   * @param {object} res Express响应对象
   * @param {string} message 响应消息
   */
  static deleted(res, message = '删除成功') {
    return this.success(res, null, message, 200);
  }

  /**
   * 未找到响应
   * @param {object} res Express响应对象
   * @param {string} message 错误消息
   */
  static notFound(res, message = '资源不存在') {
    return this.error(res, message, 404);
  }

  /**
   * 未授权响应
   * @param {object} res Express响应对象
   * @param {string} message 错误消息
   */
  static unauthorized(res, message = '未授权访问') {
    return this.error(res, message, 401);
  }

  /**
   * 禁止访问响应
   * @param {object} res Express响应对象
   * @param {string} message 错误消息
   */
  static forbidden(res, message = '禁止访问') {
    return this.error(res, message, 403);
  }

  /**
   * 参数验证失败响应
   * @param {object} res Express响应对象
   * @param {Array} errors 验证错误列表
   * @param {string} message 错误消息
   */
  static validationError(res, errors, message = '参数验证失败') {
    return res.status(400).json({
      success: false,
      message,
      errors,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 冲突响应
   * @param {object} res Express响应对象
   * @param {string} message 错误消息
   */
  static conflict(res, message = '资源冲突') {
    return this.error(res, message, 409);
  }

  /**
   * 请求过于频繁响应
   * @param {object} res Express响应对象
   * @param {string} message 错误消息
   */
  static tooManyRequests(res, message = '请求过于频繁') {
    return this.error(res, message, 429);
  }

  /**
   * 服务不可用响应
   * @param {object} res Express响应对象
   * @param {string} message 错误消息
   */
  static serviceUnavailable(res, message = '服务暂时不可用') {
    return this.error(res, message, 503);
  }

  /**
   * 自定义响应
   * @param {object} res Express响应对象
   * @param {number} code 状态码
   * @param {boolean} success 是否成功
   * @param {string} message 响应消息
   * @param {any} data 响应数据
   */
  static custom(res, code, success, message, data = null) {
    const response = {
      success,
      message,
      timestamp: new Date().toISOString()
    };

    if (data !== null) {
      response.data = data;
    }

    return res.status(code).json(response);
  }
}

/**
 * Express中间件：为响应对象添加便捷方法
 */
const responseMiddleware = (req, res, next) => {
  // 成功响应
  res.success = (data, message, code) => {
    return ResponseHelper.success(res, data, message, code);
  };

  // 失败响应
  res.error = (message, code, error) => {
    return ResponseHelper.error(res, message, code, error);
  };

  // 分页响应
  res.paginated = (data, pagination, message) => {
    return ResponseHelper.paginated(res, data, pagination, message);
  };

  // 创建成功
  res.created = (data, message) => {
    return ResponseHelper.created(res, data, message);
  };

  // 更新成功
  res.updated = (data, message) => {
    return ResponseHelper.updated(res, data, message);
  };

  // 删除成功
  res.deleted = (message) => {
    return ResponseHelper.deleted(res, message);
  };

  // 未找到
  res.notFound = (message) => {
    return ResponseHelper.notFound(res, message);
  };

  // 未授权
  res.unauthorized = (message) => {
    return ResponseHelper.unauthorized(res, message);
  };

  // 禁止访问
  res.forbidden = (message) => {
    return ResponseHelper.forbidden(res, message);
  };

  // 验证错误
  res.validationError = (errors, message) => {
    return ResponseHelper.validationError(res, errors, message);
  };

  // 冲突
  res.conflict = (message) => {
    return ResponseHelper.conflict(res, message);
  };

  next();
};

module.exports = {
  ResponseHelper,
  responseMiddleware
};
