/**
 * 数据验证工具
 */

const { body, query, param } = require('express-validator');

/**
 * 用户相关验证规则
 */
const userValidation = {
  // 注册验证
  register: [
    body('username')
      .isLength({ min: 3, max: 20 })
      .withMessage('用户名长度必须在3-20个字符之间')
      .matches(/^[a-zA-Z0-9_]+$/)
      .withMessage('用户名只能包含字母、数字和下划线'),
    body('email')
      .isEmail()
      .withMessage('邮箱格式不正确')
      .normalizeEmail(),
    body('password')
      .isLength({ min: 6, max: 20 })
      .withMessage('密码长度必须在6-20个字符之间')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      .withMessage('密码必须包含大小写字母和数字'),
    body('name')
      .notEmpty()
      .withMessage('姓名不能为空')
      .isLength({ max: 50 })
      .withMessage('姓名长度不能超过50个字符'),
    body('phone')
      .optional()
      .matches(/^1[3-9]\d{9}$/)
      .withMessage('手机号格式不正确'),
    body('role')
      .optional()
      .isIn(['admin', 'manager', 'worker', 'viewer'])
      .withMessage('角色类型无效')
  ],

  // 登录验证
  login: [
    body('username')
      .notEmpty()
      .withMessage('用户名不能为空'),
    body('password')
      .notEmpty()
      .withMessage('密码不能为空')
  ],

  // 更新资料验证
  updateProfile: [
    body('name')
      .optional()
      .notEmpty()
      .withMessage('姓名不能为空')
      .isLength({ max: 50 })
      .withMessage('姓名长度不能超过50个字符'),
    body('email')
      .optional()
      .isEmail()
      .withMessage('邮箱格式不正确')
      .normalizeEmail(),
    body('phone')
      .optional()
      .matches(/^1[3-9]\d{9}$/)
      .withMessage('手机号格式不正确')
  ],

  // 修改密码验证
  changePassword: [
    body('oldPassword')
      .notEmpty()
      .withMessage('原密码不能为空'),
    body('newPassword')
      .isLength({ min: 6, max: 20 })
      .withMessage('新密码长度必须在6-20个字符之间')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      .withMessage('新密码必须包含大小写字母和数字')
  ]
};

/**
 * 地块相关验证规则
 */
const fieldValidation = {
  // 创建地块验证
  create: [
    body('name')
      .notEmpty()
      .withMessage('地块名称不能为空')
      .isLength({ max: 100 })
      .withMessage('地块名称长度不能超过100个字符'),
    body('code')
      .notEmpty()
      .withMessage('地块编号不能为空')
      .matches(/^[A-Z]{1,2}\d{3,6}$/)
      .withMessage('地块编号格式不正确'),
    body('area')
      .isFloat({ min: 0 })
      .withMessage('地块面积必须大于0'),
    body('unit')
      .isIn(['亩', '公顷', '平方米'])
      .withMessage('面积单位无效'),
    body('location.coordinates')
      .isArray()
      .withMessage('地块坐标必须是数组'),
    body('soilType')
      .optional()
      .isIn(['沙土', '壤土', '粘土', '沙壤土', '粘壤土'])
      .withMessage('土壤类型无效'),
    body('irrigation')
      .optional()
      .isIn(['滴灌', '喷灌', '漫灌', '无'])
      .withMessage('灌溉方式无效'),
    body('drainage')
      .optional()
      .isIn(['良好', '一般', '较差'])
      .withMessage('排水情况无效')
  ],

  // 更新地块验证
  update: [
    body('name')
      .optional()
      .notEmpty()
      .withMessage('地块名称不能为空')
      .isLength({ max: 100 })
      .withMessage('地块名称长度不能超过100个字符'),
    body('code')
      .optional()
      .matches(/^[A-Z]{1,2}\d{3,6}$/)
      .withMessage('地块编号格式不正确'),
    body('area')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('地块面积必须大于0'),
    body('status')
      .optional()
      .isIn(['active', 'inactive', 'maintenance'])
      .withMessage('地块状态无效')
  ],

  // 查询验证
  query: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码必须是正整数'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量必须在1-100之间'),
    query('status')
      .optional()
      .isIn(['active', 'inactive', 'maintenance'])
      .withMessage('状态值无效')
  ]
};

/**
 * 作物相关验证规则
 */
const cropValidation = {
  // 创建作物验证
  create: [
    body('name')
      .notEmpty()
      .withMessage('作物名称不能为空')
      .isLength({ max: 100 })
      .withMessage('作物名称长度不能超过100个字符'),
    body('variety')
      .notEmpty()
      .withMessage('品种不能为空')
      .isLength({ max: 100 })
      .withMessage('品种长度不能超过100个字符'),
    body('category')
      .isIn(['粮食作物', '经济作物', '蔬菜', '水果', '花卉', '其他'])
      .withMessage('作物类别无效'),
    body('field')
      .isMongoId()
      .withMessage('地块ID格式无效'),
    body('plantDate')
      .isISO8601()
      .withMessage('种植日期格式无效'),
    body('expectedHarvestDate')
      .isISO8601()
      .withMessage('预计收获日期格式无效'),
    body('plantingArea')
      .isFloat({ min: 0 })
      .withMessage('种植面积必须大于0'),
    body('season')
      .isIn(['春季', '夏季', '秋季', '冬季'])
      .withMessage('季节无效'),
    body('year')
      .isInt({ min: 2000, max: 3000 })
      .withMessage('年份无效')
  ],

  // 更新作物验证
  update: [
    body('name')
      .optional()
      .notEmpty()
      .withMessage('作物名称不能为空')
      .isLength({ max: 100 })
      .withMessage('作物名称长度不能超过100个字符'),
    body('plantingArea')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('种植面积必须大于0'),
    body('growthStage')
      .optional()
      .isIn(['播种', '发芽', '幼苗', '生长', '开花', '结果', '成熟', '收获', '完成'])
      .withMessage('生长阶段无效'),
    body('healthStatus')
      .optional()
      .isIn(['健康', '一般', '病虫害', '营养不良', '干旱', '涝害'])
      .withMessage('健康状态无效'),
    body('status')
      .optional()
      .isIn(['active', 'completed', 'failed'])
      .withMessage('作物状态无效')
  ]
};

/**
 * 农事操作相关验证规则
 */
const farmingValidation = {
  // 创建农事操作验证
  create: [
    body('type')
      .isIn(['浇灌', '施肥', '打药', '除草', '松土', '修剪', '其他'])
      .withMessage('操作类型无效'),
    body('crop')
      .isMongoId()
      .withMessage('作物ID格式无效'),
    body('operationDate')
      .isISO8601()
      .withMessage('操作日期格式无效'),
    body('quantity')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('用量必须大于等于0'),
    body('cost')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('成本必须大于等于0')
  ]
};

/**
 * 通用ID验证
 */
const idValidation = {
  mongoId: [
    param('id')
      .isMongoId()
      .withMessage('ID格式无效')
  ]
};

/**
 * 分页查询验证
 */
const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间')
];

/**
 * 日期范围验证
 */
const dateRangeValidation = [
  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('开始日期格式无效'),
  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('结束日期格式无效')
];

module.exports = {
  userValidation,
  fieldValidation,
  cropValidation,
  farmingValidation,
  idValidation,
  paginationValidation,
  dateRangeValidation
};
