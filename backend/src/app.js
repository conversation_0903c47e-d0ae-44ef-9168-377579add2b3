const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// 导入路由
const authRoutes = require('./routes/auth');
const fieldRoutes = require('./routes/fields');
const cropRoutes = require('./routes/crops');
const farmingRoutes = require('./routes/farming');
const harvestRoutes = require('./routes/harvest');
const materialRoutes = require('./routes/materials');
const weatherRoutes = require('./routes/weather');
const dashboardRoutes = require('./routes/dashboard');
const uploadRoutes = require('./routes/upload');
const recommendationRoutes = require('./routes/recommendations');
// const iotRoutes = require('./routes/iot');
// const simulatorRoutes = require('./routes/simulator');
// const videoRoutes = require('./routes/video');
// const reportRoutes = require('./routes/reports');
// const notificationRoutes = require('./routes/notifications');

// IoT服务 - 临时禁用，缺少依赖
// const iotService = require('./services/iotService');
// const deviceSimulator = require('./services/deviceSimulator');
// const videoService = require('./services/videoService');
// const reportService = require('./services/reportService');
// const notificationService = require('./services/notificationService');

const app = express();

// 中间件配置
app.use(helmet());
app.use(compression());
app.use(morgan('combined'));
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));

// 限流配置
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100 // 限制每个IP 15分钟内最多100个请求
});
app.use('/api/', limiter);

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use('/uploads', express.static('uploads'));

// 数据库连接
const connectDB = async () => {
  try {
    const mongoOptions = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    };

    await mongoose.connect(
      process.env.MONGODB_URI || 'mongodb://localhost:27017/farm_management',
      mongoOptions
    );

    console.log('✅ MongoDB 连接成功');

    // 创建默认管理员用户
    await createDefaultAdmin();

  } catch (error) {
    console.error('❌ MongoDB 连接失败:', error);
    process.exit(1);
  }
};

// 创建默认管理员用户
const createDefaultAdmin = async () => {
  try {
    const User = require('./models/User');
    const adminExists = await User.findOne({ username: 'admin' });

    if (!adminExists) {
      const admin = new User({
        username: 'admin',
        email: '<EMAIL>',
        password: '123456',
        name: '系统管理员',
        role: 'admin',
        permissions: [
          'field_manage', 'crop_manage', 'farming_manage',
          'harvest_manage', 'material_manage', 'weather_view',
          'user_manage', 'system_manage'
        ]
      });

      await admin.save();
      console.log('✅ 默认管理员账号创建成功 (admin/123456)');
    }
  } catch (error) {
    console.error('❌ 创建默认管理员失败:', error);
  }
};

// 数据库事件监听
mongoose.connection.on('error', (err) => {
  console.error('❌ 数据库连接错误:', err);
});

mongoose.connection.on('disconnected', () => {
  console.log('⚠️ 数据库连接断开');
});

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('\n⏹️ 正在关闭服务器...');

  // 停止IoT服务 - 临时禁用
  // try {
  //   await iotService.stop();
  //   console.log('✅ IoT服务已停止');
  // } catch (error) {
  //   console.error('❌ 停止IoT服务失败:', error);
  // }

  // 停止设备模拟器
  try {
    await deviceSimulator.stop();
    console.log('✅ 设备模拟器已停止');
  } catch (error) {
    console.error('❌ 停止设备模拟器失败:', error);
  }

  // 停止视频监控服务
  try {
    await videoService.stop();
    console.log('✅ 视频监控服务已停止');
  } catch (error) {
    console.error('❌ 停止视频监控服务失败:', error);
  }

  // 停止报表生成服务
  try {
    await reportService.stop();
    console.log('✅ 报表生成服务已停止');
  } catch (error) {
    console.error('❌ 停止报表生成服务失败:', error);
  }

  // 停止通知推送服务
  try {
    await notificationService.stop();
    console.log('✅ 通知推送服务已停止');
  } catch (error) {
    console.error('❌ 停止通知推送服务失败:', error);
  }

  await mongoose.connection.close();
  console.log('✅ 数据库连接已关闭');
  process.exit(0);
});

// 路由配置
app.use('/api/auth', authRoutes);
app.use('/api/fields', fieldRoutes);
app.use('/api/crops', cropRoutes);
app.use('/api/farming', farmingRoutes);
app.use('/api/harvest', harvestRoutes);
app.use('/api/materials', materialRoutes);
app.use('/api/weather', weatherRoutes);
app.use('/api/dashboard', dashboardRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api/recommendations', recommendationRoutes);
// app.use('/api/iot', iotRoutes); // 临时禁用，缺少依赖
// app.use('/api/simulator', simulatorRoutes); // 临时禁用，缺少依赖
// app.use('/api/video', videoRoutes); // 临时禁用，缺少依赖
// app.use('/api/reports', reportRoutes); // 临时禁用，缺少依赖
// app.use('/api/notifications', notificationRoutes); // 临时禁用，缺少依赖

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在'
  });
});

// 全局错误处理
app.use((error, req, res, next) => {
  console.error('❌ 服务器错误:', error);
  
  // Mongoose 验证错误
  if (error.name === 'ValidationError') {
    const errors = Object.values(error.errors).map(err => err.message);
    return res.status(400).json({
      success: false,
      message: '数据验证失败',
      errors
    });
  }
  
  // Mongoose 重复键错误
  if (error.code === 11000) {
    return res.status(400).json({
      success: false,
      message: '数据已存在'
    });
  }
  
  // JWT 错误
  if (error.name === 'JsonWebTokenError') {
    return res.status(401).json({
      success: false,
      message: '无效的访问令牌'
    });
  }
  
  // 默认错误
  res.status(error.status || 500).json({
    success: false,
    message: error.message || '服务器内部错误'
  });
});

const PORT = process.env.PORT || 5000;

// 启动服务器
const startServer = async () => {
  await connectDB();

  // 启动IoT服务 - 临时禁用
  // try {
  //   await iotService.start();
  //   console.log('🌐 IoT服务启动成功');
  // } catch (error) {
  //   console.error('❌ IoT服务启动失败:', error);
  // }

  // 启动设备模拟器 - 临时禁用
  // try {
  //   await deviceSimulator.start();
  //   console.log('🎭 设备模拟器启动成功');
  // } catch (error) {
  //   console.error('❌ 设备模拟器启动失败:', error);
  // }

  // 启动视频监控服务 - 临时禁用
  // try {
  //   await videoService.start();
  //   console.log('📹 视频监控服务启动成功');
  // } catch (error) {
  //   console.error('❌ 视频监控服务启动失败:', error);
  // }

  // 启动报表生成服务 - 临时禁用
  // try {
  //   await reportService.start();
  //   console.log('📊 报表生成服务启动成功');
  // } catch (error) {
  //   console.error('❌ 报表生成服务启动失败:', error);
  // }

  // 启动通知推送服务 - 临时禁用
  // try {
  //   await notificationService.start();
  //   console.log('🔔 通知推送服务启动成功');
  // } catch (error) {
  //   console.error('❌ 通知推送服务启动失败:', error);
  // }

  app.listen(PORT, () => {
    console.log(`🚀 服务器运行在端口 ${PORT}`);
    console.log(`📱 API 地址: http://localhost:${PORT}/api`);
    console.log(`🏥 健康检查: http://localhost:${PORT}/health`);
  });
};

startServer().catch(console.error);

module.exports = app;
