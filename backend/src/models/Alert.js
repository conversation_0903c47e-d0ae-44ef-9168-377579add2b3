const mongoose = require('mongoose');

const alertSchema = new mongoose.Schema({
  // 基本信息
  title: {
    type: String,
    required: [true, '预警标题不能为空'],
    trim: true,
    maxlength: [200, '预警标题不能超过200个字符']
  },
  
  message: {
    type: String,
    required: [true, '预警消息不能为空'],
    trim: true,
    maxlength: [1000, '预警消息不能超过1000个字符']
  },
  
  // 预警类型
  type: {
    type: String,
    required: [true, '预警类型不能为空'],
    enum: {
      values: ['device', 'sensor', 'weather', 'system', 'security', 'maintenance'],
      message: '预警类型必须是: device, sensor, weather, system, security, maintenance 之一'
    }
  },
  
  // 预警级别
  level: {
    type: String,
    required: [true, '预警级别不能为空'],
    enum: {
      values: ['critical', 'warning', 'info'],
      message: '预警级别必须是: critical, warning, info 之一'
    },
    default: 'info'
  },
  
  // 预警状态
  status: {
    type: String,
    enum: {
      values: ['active', 'resolved', 'dismissed'],
      message: '预警状态必须是: active, resolved, dismissed 之一'
    },
    default: 'active'
  },
  
  // 预警来源
  source: {
    type: String,
    trim: true,
    maxlength: [100, '预警来源不能超过100个字符']
  },
  
  // 关联设备
  deviceId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Device'
  },
  
  // 关联地块
  fieldId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Field'
  },
  
  // 预警数据
  data: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  
  // 阈值信息
  threshold: {
    type: {
      parameter: String,    // 参数名称
      value: Number,        // 阈值
      operator: {           // 操作符
        type: String,
        enum: ['>', '<', '>=', '<=', '==', '!='],
        default: '>'
      },
      unit: String          // 单位
    }
  },
  
  // 阅读状态
  isRead: {
    type: Boolean,
    default: false
  },
  
  readAt: {
    type: Date
  },
  
  readBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // 解决信息
  resolution: {
    type: String,
    trim: true,
    maxlength: [500, '解决方案不能超过500个字符']
  },
  
  resolvedAt: {
    type: Date
  },
  
  resolvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // 通知信息
  notificationSent: {
    type: Boolean,
    default: false
  },
  
  notificationSentAt: {
    type: Date
  },
  
  // 重复预警控制
  fingerprint: {
    type: String,
    index: true
  },
  
  // 创建和更新信息
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 索引
alertSchema.index({ type: 1, level: 1 });
alertSchema.index({ status: 1, createdAt: -1 });
alertSchema.index({ deviceId: 1, createdAt: -1 });
alertSchema.index({ fieldId: 1, createdAt: -1 });
alertSchema.index({ isRead: 1, createdAt: -1 });
alertSchema.index({ fingerprint: 1, createdAt: -1 });

// 虚拟字段
alertSchema.virtual('isActive').get(function() {
  return this.status === 'active';
});

alertSchema.virtual('isResolved').get(function() {
  return this.status === 'resolved';
});

alertSchema.virtual('isCritical').get(function() {
  return this.level === 'critical';
});

alertSchema.virtual('age').get(function() {
  return Date.now() - this.createdAt.getTime();
});

alertSchema.virtual('resolutionTime').get(function() {
  if (this.resolvedAt && this.createdAt) {
    return this.resolvedAt.getTime() - this.createdAt.getTime();
  }
  return null;
});

// 中间件
alertSchema.pre('save', function(next) {
  // 生成指纹用于去重
  if (!this.fingerprint) {
    const components = [
      this.type,
      this.deviceId?.toString() || '',
      this.fieldId?.toString() || '',
      this.threshold?.parameter || ''
    ];
    this.fingerprint = components.join('|');
  }
  
  next();
});

// 静态方法
alertSchema.statics.findActive = function() {
  return this.find({ status: 'active' });
};

alertSchema.statics.findUnread = function() {
  return this.find({ isRead: false });
};

alertSchema.statics.findByLevel = function(level) {
  return this.find({ level });
};

alertSchema.statics.findByType = function(type) {
  return this.find({ type });
};

alertSchema.statics.findByDevice = function(deviceId) {
  return this.find({ deviceId });
};

alertSchema.statics.findByField = function(fieldId) {
  return this.find({ fieldId });
};

alertSchema.statics.findRecent = function(hours = 24) {
  const since = new Date(Date.now() - hours * 60 * 60 * 1000);
  return this.find({ createdAt: { $gte: since } });
};

// 实例方法
alertSchema.methods.markAsRead = function(userId) {
  this.isRead = true;
  this.readAt = new Date();
  this.readBy = userId;
  return this.save();
};

alertSchema.methods.resolve = function(resolution, userId) {
  this.status = 'resolved';
  this.resolution = resolution;
  this.resolvedAt = new Date();
  this.resolvedBy = userId;
  
  // 同时标记为已读
  if (!this.isRead) {
    this.isRead = true;
    this.readAt = new Date();
    this.readBy = userId;
  }
  
  return this.save();
};

alertSchema.methods.dismiss = function() {
  this.status = 'dismissed';
  return this.save();
};

alertSchema.methods.reactivate = function() {
  this.status = 'active';
  this.resolvedAt = undefined;
  this.resolvedBy = undefined;
  this.resolution = undefined;
  return this.save();
};

// 检查是否为重复预警
alertSchema.methods.isDuplicate = async function() {
  const duplicateWindow = 5 * 60 * 1000; // 5分钟内的重复预警
  const since = new Date(Date.now() - duplicateWindow);
  
  const duplicate = await this.constructor.findOne({
    fingerprint: this.fingerprint,
    createdAt: { $gte: since },
    _id: { $ne: this._id }
  });
  
  return !!duplicate;
};

// 获取相关预警
alertSchema.methods.getRelatedAlerts = function() {
  const query = {
    _id: { $ne: this._id }
  };
  
  if (this.deviceId) {
    query.deviceId = this.deviceId;
  } else if (this.fieldId) {
    query.fieldId = this.fieldId;
  } else {
    query.type = this.type;
  }
  
  return this.constructor.find(query)
    .sort({ createdAt: -1 })
    .limit(10);
};

module.exports = mongoose.model('Alert', alertSchema);
