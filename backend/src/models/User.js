const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
    username: {
        type: String,
        required: [true, '用户名不能为空'],
        unique: true,
        trim: true,
        minlength: [3, '用户名至少3个字符'],
        maxlength: [20, '用户名最多20个字符']
    },
    email: {
        type: String,
        required: [true, '邮箱不能为空'],
        unique: true,
        lowercase: true,
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, '邮箱格式不正确']
    },
    password: {
        type: String,
        required: [true, '密码不能为空'],
        minlength: [6, '密码至少6个字符']
    },
    name: {
        type: String,
        required: [true, '姓名不能为空'],
        trim: true
    },
    phone: {
        type: String,
        match: [/^1[3-9]\d{9}$/, '手机号格式不正确']
    },
    role: {
        type: String,
        enum: ['admin', 'manager', 'worker', 'viewer'],
        default: 'worker'
    },
    avatar: {
        type: String,
        default: ''
    },
    department: {
        type: String,
        default: ''
    },
    status: {
        type: String,
        enum: ['active', 'inactive'],
        default: 'active'
    },
    lastLogin: {
        type: Date
    },
    permissions: [{
        type: String,
        enum: [
            'field_manage',
            'crop_manage',
            'farming_manage',
            'harvest_manage',
            'material_manage',
            'weather_view',
            'dashboard_view',
            'user_manage',
            'system_manage',
            'system_config'
        ]
    }]
}, {
    timestamps: true,
    toJSON: {
        transform: function (doc, ret) {
            delete ret.password;
            return ret;
        }
    }
});

// 密码加密中间件
userSchema.pre('save', async function (next) {
    if (!this.isModified('password')) return next();

    try {
        const salt = await bcrypt.genSalt(12);
        this.password = await bcrypt.hash(this.password, salt);
        next();
    } catch (error) {
        next(error);
    }
});

// 密码验证方法
userSchema.methods.comparePassword = async function (candidatePassword) {
    return await bcrypt.compare(candidatePassword, this.password);
};

// 获取用户权限
userSchema.methods.hasPermission = function (permission) {
    if (this.role === 'admin') return true;
    return this.permissions.includes(permission);
};

// 更新最后登录时间
userSchema.methods.updateLastLogin = function () {
    this.lastLogin = new Date();
    return this.save();
};

// 索引
userSchema.index({username: 1});
userSchema.index({email: 1});
userSchema.index({role: 1});
userSchema.index({status: 1});

module.exports = mongoose.model('User', userSchema);
