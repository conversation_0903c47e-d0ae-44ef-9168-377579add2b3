const express = require('express');
const { query } = require('express-validator');
const dashboardController = require('../controllers/dashboardController');
const { auth } = require('../middleware/auth');

const router = express.Router();

// 获取仪表盘概览数据
router.get('/overview', auth, dashboardController.getOverview);

// 获取生产趋势数据
router.get('/production-trends', auth, [
  query('period').optional().isIn(['week', 'month', 'quarter', 'year']).withMessage('时间周期无效'),
  query('year').optional().isInt({ min: 2000, max: 3000 }).withMessage('年份无效')
], dashboardController.getProductionTrends);

// 获取作物分布数据
router.get('/crop-distribution', auth, dashboardController.getCropDistribution);

// 获取收益分析数据
router.get('/revenue-analysis', auth, [
  query('year').optional().isInt({ min: 2000, max: 3000 }).withMessage('年份无效')
], dashboardController.getRevenueAnalysis);

// 获取预警信息
router.get('/alerts', auth, dashboardController.getAlerts);

// 获取地块地图数据
router.get('/map-data', auth, dashboardController.getMapData);

module.exports = router;
