const express = require('express');
const { query } = require('express-validator');
const dashboardController = require('../controllers/dashboardController');
const auth = require('../middleware/auth');

const router = express.Router();

// 获取仪表盘概览数据
router.get('/overview', auth, dashboardController.getOverview);
  try {
    const userId = req.user.userId;
    const userRole = req.user.role;
    
    // 构建查询条件（非管理员只能查看自己相关的数据）
    const userQuery = userRole === 'admin' ? {} : {
      $or: [
        { owner: userId },
        { manager: userId },
        { planter: userId },
        { operator: userId },
        { supervisor: userId }
      ]
    };

    // 并行查询各种统计数据
    const [
      fieldStats,
      cropStats,
      operationStats,
      harvestStats,
      materialStats,
      recentActivities
    ] = await Promise.all([
      // 地块统计
      Field.aggregate([
        { $match: userRole === 'admin' ? {} : { $or: [{ owner: userId }, { manager: userId }] } },
        {
          $group: {
            _id: null,
            totalFields: { $sum: 1 },
            totalArea: { $sum: '$area' },
            activeFields: { $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] } }
          }
        }
      ]),
      
      // 作物统计
      Crop.aggregate([
        { $match: userRole === 'admin' ? {} : { $or: [{ planter: userId }, { manager: userId }] } },
        {
          $group: {
            _id: null,
            totalCrops: { $sum: 1 },
            activeCrops: { $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] } },
            totalPlantingArea: { $sum: '$plantingArea' },
            expectedYield: { $sum: '$expectedYield' },
            actualYield: { $sum: '$actualYield' }
          }
        }
      ]),
      
      // 农事操作统计
      FarmingOperation.aggregate([
        { $match: userRole === 'admin' ? {} : { $or: [{ operator: userId }, { supervisor: userId }] } },
        {
          $group: {
            _id: null,
            totalOperations: { $sum: 1 },
            completedOperations: { $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] } },
            totalCost: { $sum: { $add: ['$costs.materials', '$costs.labor', '$costs.machinery', '$costs.other'] } }
          }
        }
      ]),
      
      // 收获统计
      Harvest.aggregate([
        { $match: userRole === 'admin' ? {} : { $or: [{ supervisor: userId }] } },
        {
          $group: {
            _id: null,
            totalHarvests: { $sum: 1 },
            totalYield: { $sum: '$totalYield' },
            totalRevenue: { $sum: { $sum: '$sales.totalAmount' } }
          }
        }
      ]),
      
      // 物资统计
      Material.aggregate([
        {
          $group: {
            _id: null,
            totalMaterials: { $sum: 1 },
            lowStockCount: {
              $sum: { $cond: [{ $lte: ['$inventory.currentStock', '$inventory.minStock'] }, 1, 0] }
            },
            totalValue: { $sum: { $multiply: ['$inventory.currentStock', '$pricing.averagePrice'] } }
          }
        }
      ]),
      
      // 最近活动
      FarmingOperation.find(userRole === 'admin' ? {} : { $or: [{ operator: userId }, { supervisor: userId }] })
        .populate('field', 'name code')
        .populate('crop', 'name variety')
        .populate('operator', 'name')
        .sort({ createdAt: -1 })
        .limit(10)
        .select('type operationDate field crop operator status')
    ]);

    const overview = {
      fields: fieldStats[0] || { totalFields: 0, totalArea: 0, activeFields: 0 },
      crops: cropStats[0] || { totalCrops: 0, activeCrops: 0, totalPlantingArea: 0, expectedYield: 0, actualYield: 0 },
      operations: operationStats[0] || { totalOperations: 0, completedOperations: 0, totalCost: 0 },
      harvests: harvestStats[0] || { totalHarvests: 0, totalYield: 0, totalRevenue: 0 },
      materials: materialStats[0] || { totalMaterials: 0, lowStockCount: 0, totalValue: 0 },
      recentActivities: recentActivities.map(activity => ({
        id: activity._id,
        type: activity.type,
        date: activity.operationDate,
        field: activity.field?.name,
        crop: activity.crop?.name,
        operator: activity.operator?.name,
        status: activity.status
      }))
    };

    res.json({
      success: true,
      data: overview
    });
  } catch (error) {
    console.error('获取仪表盘概览错误:', error);
    res.status(500).json({
      success: false,
      message: '获取仪表盘概览失败',
      error: error.message
    });
  }
});

// 获取生产趋势数据
router.get('/production-trends', auth, [
  query('period').optional().isIn(['week', 'month', 'quarter', 'year']).withMessage('时间周期无效'),
  query('year').optional().isInt({ min: 2000, max: 3000 }).withMessage('年份无效')
], async (req, res) => {
  try {
    const { period = 'month', year = new Date().getFullYear() } = req.query;
    const userId = req.user.userId;
    const userRole = req.user.role;
    
    let groupBy, dateRange;
    const currentDate = new Date();
    
    switch (period) {
      case 'week':
        groupBy = { week: { $week: '$operationDate' }, year: { $year: '$operationDate' } };
        dateRange = { $gte: new Date(currentDate.getFullYear(), 0, 1) };
        break;
      case 'quarter':
        groupBy = { quarter: { $ceil: { $divide: [{ $month: '$operationDate' }, 3] } }, year: { $year: '$operationDate' } };
        dateRange = { $gte: new Date(year - 2, 0, 1) };
        break;
      case 'year':
        groupBy = { year: { $year: '$operationDate' } };
        dateRange = { $gte: new Date(year - 5, 0, 1) };
        break;
      default: // month
        groupBy = { month: { $month: '$operationDate' }, year: { $year: '$operationDate' } };
        dateRange = { $gte: new Date(year, 0, 1), $lt: new Date(year + 1, 0, 1) };
    }

    const matchQuery = {
      operationDate: dateRange,
      ...(userRole === 'admin' ? {} : { $or: [{ operator: userId }, { supervisor: userId }] })
    };

    const trends = await FarmingOperation.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: groupBy,
          operationCount: { $sum: 1 },
          totalCost: { $sum: { $add: ['$costs.materials', '$costs.labor', '$costs.machinery', '$costs.other'] } },
          irrigationCount: { $sum: { $cond: [{ $eq: ['$type', '浇灌'] }, 1, 0] } },
          fertilizationCount: { $sum: { $cond: [{ $eq: ['$type', '施肥'] }, 1, 0] } },
          pesticideCount: { $sum: { $cond: [{ $eq: ['$type', '打药'] }, 1, 0] } }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.quarter': 1, '_id.week': 1 } }
    ]);

    res.json({
      success: true,
      data: trends
    });
  } catch (error) {
    console.error('获取生产趋势错误:', error);
    res.status(500).json({
      success: false,
      message: '获取生产趋势失败',
      error: error.message
    });
  }
});

// 获取作物分布数据
router.get('/crop-distribution', auth, async (req, res) => {
  try {
    const userId = req.user.userId;
    const userRole = req.user.role;
    
    const matchQuery = userRole === 'admin' ? {} : { $or: [{ planter: userId }, { manager: userId }] };

    const [categoryDistribution, stageDistribution, fieldDistribution] = await Promise.all([
      // 按类别分布
      Crop.aggregate([
        { $match: matchQuery },
        {
          $group: {
            _id: '$category',
            count: { $sum: 1 },
            totalArea: { $sum: '$plantingArea' },
            expectedYield: { $sum: '$expectedYield' }
          }
        }
      ]),
      
      // 按生长阶段分布
      Crop.aggregate([
        { $match: { ...matchQuery, status: 'active' } },
        {
          $group: {
            _id: '$growthStage',
            count: { $sum: 1 },
            totalArea: { $sum: '$plantingArea' }
          }
        }
      ]),
      
      // 按地块分布
      Crop.aggregate([
        { $match: matchQuery },
        {
          $lookup: {
            from: 'fields',
            localField: 'field',
            foreignField: '_id',
            as: 'fieldInfo'
          }
        },
        { $unwind: '$fieldInfo' },
        {
          $group: {
            _id: '$field',
            fieldName: { $first: '$fieldInfo.name' },
            fieldCode: { $first: '$fieldInfo.code' },
            cropCount: { $sum: 1 },
            totalArea: { $sum: '$plantingArea' }
          }
        }
      ])
    ]);

    res.json({
      success: true,
      data: {
        categoryDistribution,
        stageDistribution,
        fieldDistribution
      }
    });
  } catch (error) {
    console.error('获取作物分布错误:', error);
    res.status(500).json({
      success: false,
      message: '获取作物分布失败',
      error: error.message
    });
  }
});

// 获取收益分析数据
router.get('/revenue-analysis', auth, [
  query('year').optional().isInt({ min: 2000, max: 3000 }).withMessage('年份无效')
], async (req, res) => {
  try {
    const { year = new Date().getFullYear() } = req.query;
    const userId = req.user.userId;
    const userRole = req.user.role;
    
    const startDate = new Date(year, 0, 1);
    const endDate = new Date(year + 1, 0, 1);
    
    const matchQuery = {
      harvestDate: { $gte: startDate, $lt: endDate },
      ...(userRole === 'admin' ? {} : { supervisor: userId })
    };

    const [monthlyRevenue, cropRevenue, costAnalysis] = await Promise.all([
      // 月度收益
      Harvest.aggregate([
        { $match: matchQuery },
        {
          $group: {
            _id: { month: { $month: '$harvestDate' } },
            totalRevenue: { $sum: { $sum: '$sales.totalAmount' } },
            totalCost: { $sum: { $add: ['$costs.labor', '$costs.machinery', '$costs.transportation', '$costs.packaging', '$costs.storage', '$costs.other'] } },
            totalYield: { $sum: '$totalYield' },
            harvestCount: { $sum: 1 }
          }
        },
        {
          $addFields: {
            profit: { $subtract: ['$totalRevenue', '$totalCost'] },
            profitMargin: {
              $cond: [
                { $gt: ['$totalRevenue', 0] },
                { $multiply: [{ $divide: [{ $subtract: ['$totalRevenue', '$totalCost'] }, '$totalRevenue'] }, 100] },
                0
              ]
            }
          }
        },
        { $sort: { '_id.month': 1 } }
      ]),
      
      // 按作物收益
      Harvest.aggregate([
        { $match: matchQuery },
        {
          $lookup: {
            from: 'crops',
            localField: 'crop',
            foreignField: '_id',
            as: 'cropInfo'
          }
        },
        { $unwind: '$cropInfo' },
        {
          $group: {
            _id: '$cropInfo.category',
            totalRevenue: { $sum: { $sum: '$sales.totalAmount' } },
            totalCost: { $sum: { $add: ['$costs.labor', '$costs.machinery', '$costs.transportation', '$costs.packaging', '$costs.storage', '$costs.other'] } },
            totalYield: { $sum: '$totalYield' },
            avgPrice: { $avg: { $divide: [{ $sum: '$sales.totalAmount' }, '$totalYield'] } }
          }
        },
        {
          $addFields: {
            profit: { $subtract: ['$totalRevenue', '$totalCost'] }
          }
        }
      ]),
      
      // 成本分析
      Harvest.aggregate([
        { $match: matchQuery },
        {
          $group: {
            _id: null,
            laborCost: { $sum: '$costs.labor' },
            machineryCost: { $sum: '$costs.machinery' },
            transportationCost: { $sum: '$costs.transportation' },
            packagingCost: { $sum: '$costs.packaging' },
            storageCost: { $sum: '$costs.storage' },
            otherCost: { $sum: '$costs.other' }
          }
        }
      ])
    ]);

    res.json({
      success: true,
      data: {
        monthlyRevenue,
        cropRevenue,
        costAnalysis: costAnalysis[0] || {
          laborCost: 0,
          machineryCost: 0,
          transportationCost: 0,
          packagingCost: 0,
          storageCost: 0,
          otherCost: 0
        }
      }
    });
  } catch (error) {
    console.error('获取收益分析错误:', error);
    res.status(500).json({
      success: false,
      message: '获取收益分析失败',
      error: error.message
    });
  }
});

// 获取预警信息
router.get('/alerts', auth, async (req, res) => {
  try {
    const userId = req.user.userId;
    const userRole = req.user.role;
    
    const alerts = [];
    
    // 库存预警
    const lowStockMaterials = await Material.find({
      status: 'active',
      $expr: { $lte: ['$inventory.currentStock', '$inventory.minStock'] }
    }).select('name code inventory category unit');
    
    lowStockMaterials.forEach(material => {
      alerts.push({
        type: 'stock',
        level: 'warning',
        title: '库存不足',
        message: `${material.name} 库存不足，当前库存: ${material.inventory.currentStock}${material.unit}`,
        data: material
      });
    });
    
    // 作物生长预警
    const cropQuery = userRole === 'admin' ? {} : { $or: [{ planter: userId }, { manager: userId }] };
    const unhealthyCrops = await Crop.find({
      ...cropQuery,
      status: 'active',
      healthStatus: { $in: ['病虫害', '营养不良', '干旱', '涝害'] }
    }).populate('field', 'name code').select('name variety healthStatus field');
    
    unhealthyCrops.forEach(crop => {
      alerts.push({
        type: 'crop',
        level: crop.healthStatus === '病虫害' ? 'danger' : 'warning',
        title: '作物健康预警',
        message: `${crop.field.name} 的 ${crop.name}(${crop.variety}) 状态异常: ${crop.healthStatus}`,
        data: crop
      });
    });
    
    // 收获提醒
    const harvestDue = await Crop.find({
      ...cropQuery,
      status: 'active',
      expectedHarvestDate: {
        $gte: new Date(),
        $lte: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7天内
      }
    }).populate('field', 'name code').select('name variety expectedHarvestDate field');
    
    harvestDue.forEach(crop => {
      const daysLeft = Math.ceil((crop.expectedHarvestDate - new Date()) / (1000 * 60 * 60 * 24));
      alerts.push({
        type: 'harvest',
        level: 'info',
        title: '收获提醒',
        message: `${crop.field.name} 的 ${crop.name}(${crop.variety}) 预计 ${daysLeft} 天后收获`,
        data: crop
      });
    });

    res.json({
      success: true,
      data: alerts
    });
  } catch (error) {
    console.error('获取预警信息错误:', error);
    res.status(500).json({
      success: false,
      message: '获取预警信息失败',
      error: error.message
    });
  }
});

// 获取地块地图数据
router.get('/map-data', auth, async (req, res) => {
  try {
    const userId = req.user.userId;
    const userRole = req.user.role;
    
    const fieldQuery = userRole === 'admin' ? {} : { $or: [{ owner: userId }, { manager: userId }] };
    
    const fields = await Field.find(fieldQuery)
      .populate('owner', 'name')
      .populate('manager', 'name')
      .select('name code area location center soilType status');
    
    const mapData = fields.map(field => ({
      id: field._id,
      name: field.name,
      code: field.code,
      area: field.area,
      coordinates: field.location.coordinates,
      center: field.center,
      soilType: field.soilType,
      status: field.status,
      owner: field.owner?.name,
      manager: field.manager?.name
    }));

    res.json({
      success: true,
      data: mapData
    });
  } catch (error) {
    console.error('获取地块地图数据错误:', error);
    res.status(500).json({
      success: false,
      message: '获取地块地图数据失败',
      error: error.message
    });
  }
});

module.exports = router;
