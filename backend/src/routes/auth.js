const express = require('express');
const authController = require('../controllers/authController');
const auth = require('../middleware/auth');
const { userValidation } = require('../utils/validation');

const router = express.Router();

// 用户注册
router.post('/register', userValidation.register, authController.register);

// 用户登录
router.post('/login', userValidation.login, authController.login);

// 获取当前用户信息
router.get('/me', auth, authController.getCurrentUser);

// 更新用户信息
router.put('/profile', auth, userValidation.updateProfile, authController.updateProfile);

// 修改密码
router.put('/password', auth, userValidation.changePassword, authController.changePassword);

// 登出
router.post('/logout', auth, (req, res) => {
  res.json({
    success: true,
    message: '登出成功'
  });
});

module.exports = router;
