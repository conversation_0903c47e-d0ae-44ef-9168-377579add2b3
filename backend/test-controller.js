const authController = require('./src/controllers/authController');

console.log('authController:', authController);
console.log('authController.getCurrentUser:', authController.getCurrentUser);
console.log('typeof authController.getCurrentUser:', typeof authController.getCurrentUser);

// 检查所有方法
console.log('All methods:');
Object.getOwnPropertyNames(Object.getPrototypeOf(authController)).forEach(name => {
  if (name !== 'constructor') {
    console.log(`  ${name}: ${typeof authController[name]}`);
  }
});
