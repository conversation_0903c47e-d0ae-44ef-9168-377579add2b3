/**
 * 应用配置中心
 */

require('dotenv').config();

const config = {
  // 应用基础配置
  app: {
    name: process.env.APP_NAME || '农场智慧管理系统',
    version: process.env.APP_VERSION || '1.0.0',
    env: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.PORT) || 5000,
    debug: process.env.DEBUG === 'true'
  },

  // 数据库配置
  database: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/farm_management',
    testUri: process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/farm_management_test',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      bufferMaxEntries: 0,
      bufferCommands: false
    }
  },

  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'fallback_secret_key',
    expiresIn: process.env.JWT_EXPIRE || '7d',
    refreshSecret: process.env.JWT_REFRESH_SECRET || 'fallback_refresh_secret',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRE || '30d'
  },

  // 密码加密配置
  bcrypt: {
    rounds: parseInt(process.env.BCRYPT_ROUNDS) || 12
  },

  // 文件上传配置
  upload: {
    path: process.env.UPLOAD_PATH || './uploads',
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB
    allowedTypes: process.env.ALLOWED_FILE_TYPES?.split(',') || [
      'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf'
    ]
  },

  // 邮件配置
  mail: {
    host: process.env.MAIL_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.MAIL_PORT) || 587,
    secure: process.env.MAIL_SECURE === 'true',
    auth: {
      user: process.env.MAIL_USER,
      pass: process.env.MAIL_PASS
    },
    from: process.env.MAIL_FROM || '农场管理系统 <<EMAIL>>'
  },

  // Redis配置
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD || '',
    db: parseInt(process.env.REDIS_DB) || 0,
    enabled: process.env.REDIS_ENABLED === 'true'
  },

  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || './logs/app.log',
    maxFiles: parseInt(process.env.LOG_MAX_FILES) || 5,
    maxSize: process.env.LOG_MAX_SIZE || '10m'
  },

  // CORS配置
  cors: {
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000', 'http://localhost:3001'],
    credentials: process.env.CORS_CREDENTIALS === 'true'
  },

  // 安全配置
  security: {
    rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW) || 15 * 60 * 1000, // 15分钟
    rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX) || 100,
    helmet: {
      contentSecurityPolicy: process.env.NODE_ENV === 'production',
      crossOriginEmbedderPolicy: false
    }
  },

  // 天气API配置
  weather: {
    apiKey: process.env.WEATHER_API_KEY,
    apiUrl: process.env.WEATHER_API_URL || 'https://api.openweathermap.org/data/2.5',
    enabled: !!process.env.WEATHER_API_KEY
  },

  // 地图服务配置
  map: {
    apiKey: process.env.MAP_API_KEY,
    serviceUrl: process.env.MAP_SERVICE_URL || 'https://api.mapbox.com',
    enabled: !!process.env.MAP_API_KEY
  },

  // IoT设备配置
  iot: {
    mqttBroker: process.env.IOT_MQTT_BROKER || 'mqtt://localhost:1883',
    mqttUsername: process.env.IOT_MQTT_USERNAME || '',
    mqttPassword: process.env.IOT_MQTT_PASSWORD || '',
    deviceTimeout: parseInt(process.env.IOT_DEVICE_TIMEOUT) || 30000,
    enabled: process.env.IOT_ENABLED === 'true'
  },

  // 视频监控配置
  video: {
    storagePath: process.env.VIDEO_STORAGE_PATH || './uploads/videos',
    streamPort: parseInt(process.env.VIDEO_STREAM_PORT) || 8554,
    recordingEnabled: process.env.VIDEO_RECORDING_ENABLED === 'true',
    maxRecordingDuration: parseInt(process.env.VIDEO_MAX_DURATION) || 3600, // 1小时
    enabled: process.env.VIDEO_ENABLED === 'true'
  },

  // 报表配置
  report: {
    templatePath: process.env.REPORT_TEMPLATE_PATH || './templates/reports',
    outputPath: process.env.REPORT_OUTPUT_PATH || './uploads/reports',
    enabled: process.env.REPORT_ENABLED === 'true'
  },

  // 通知配置
  notification: {
    enabled: process.env.NOTIFICATION_ENABLED === 'true',
    pushServiceKey: process.env.PUSH_SERVICE_KEY,
    channels: {
      email: process.env.NOTIFICATION_EMAIL === 'true',
      sms: process.env.NOTIFICATION_SMS === 'true',
      push: process.env.NOTIFICATION_PUSH === 'true'
    }
  },

  // 备份配置
  backup: {
    enabled: process.env.BACKUP_ENABLED === 'true',
    schedule: process.env.BACKUP_SCHEDULE || '0 2 * * *', // 每天凌晨2点
    retentionDays: parseInt(process.env.BACKUP_RETENTION_DAYS) || 30,
    path: process.env.BACKUP_PATH || './backups'
  },

  // 监控配置
  monitoring: {
    enabled: process.env.MONITORING_ENABLED === 'true',
    healthCheckInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL) || 60000,
    metricsEnabled: process.env.METRICS_ENABLED === 'true'
  },

  // 第三方服务配置
  thirdParty: {
    apiKey: process.env.THIRD_PARTY_API_KEY,
    secret: process.env.THIRD_PARTY_SECRET,
    enabled: !!process.env.THIRD_PARTY_API_KEY
  },

  // 开发配置
  development: {
    enableSwagger: process.env.ENABLE_SWAGGER === 'true',
    mockData: process.env.MOCK_DATA === 'true',
    debugRoutes: process.env.DEBUG_ROUTES === 'true'
  }
};

/**
 * 验证必需的配置项
 */
function validateConfig() {
  const requiredConfigs = [
    'app.port',
    'database.uri',
    'jwt.secret'
  ];

  const missingConfigs = [];

  requiredConfigs.forEach(configPath => {
    const value = configPath.split('.').reduce((obj, key) => obj?.[key], config);
    if (!value) {
      missingConfigs.push(configPath);
    }
  });

  if (missingConfigs.length > 0) {
    throw new Error(`缺少必需的配置项: ${missingConfigs.join(', ')}`);
  }
}

/**
 * 获取配置信息（隐藏敏感信息）
 */
function getPublicConfig() {
  const publicConfig = JSON.parse(JSON.stringify(config));
  
  // 隐藏敏感信息
  if (publicConfig.jwt) {
    publicConfig.jwt.secret = '***';
    publicConfig.jwt.refreshSecret = '***';
  }
  
  if (publicConfig.mail?.auth) {
    publicConfig.mail.auth.pass = '***';
  }
  
  if (publicConfig.redis) {
    publicConfig.redis.password = '***';
  }
  
  if (publicConfig.weather) {
    publicConfig.weather.apiKey = '***';
  }
  
  if (publicConfig.map) {
    publicConfig.map.apiKey = '***';
  }

  return publicConfig;
}

// 验证配置
try {
  validateConfig();
} catch (error) {
  console.error('配置验证失败:', error.message);
  process.exit(1);
}

module.exports = {
  ...config,
  validateConfig,
  getPublicConfig
};
