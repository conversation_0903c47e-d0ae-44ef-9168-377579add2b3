/**
 * 数据库配置
 */

const mongoose = require('mongoose');
const logger = require('../src/utils/logger');

class DatabaseConfig {
  constructor() {
    this.connection = null;
    this.isConnected = false;
  }

  /**
   * 连接数据库
   */
  async connect() {
    try {
      const mongoOptions = {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        bufferMaxEntries: 0,
        bufferCommands: false,
      };

      // 根据环境选择数据库URI
      const mongoUri = process.env.NODE_ENV === 'test' 
        ? process.env.MONGODB_TEST_URI 
        : process.env.MONGODB_URI || 'mongodb://localhost:27017/farm_management';

      this.connection = await mongoose.connect(mongoUri, mongoOptions);
      this.isConnected = true;

      logger.info('✅ MongoDB 连接成功', {
        host: this.connection.connection.host,
        port: this.connection.connection.port,
        database: this.connection.connection.name
      });

      // 监听连接事件
      this.setupEventListeners();

      return this.connection;
    } catch (error) {
      logger.error('❌ MongoDB 连接失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 连接错误
    mongoose.connection.on('error', (error) => {
      logger.error('❌ 数据库连接错误', { error: error.message });
      this.isConnected = false;
    });

    // 连接断开
    mongoose.connection.on('disconnected', () => {
      logger.warn('⚠️ 数据库连接断开');
      this.isConnected = false;
    });

    // 重新连接
    mongoose.connection.on('reconnected', () => {
      logger.info('🔄 数据库重新连接成功');
      this.isConnected = true;
    });

    // 应用终止时关闭连接
    process.on('SIGINT', async () => {
      await this.disconnect();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      await this.disconnect();
      process.exit(0);
    });
  }

  /**
   * 断开数据库连接
   */
  async disconnect() {
    try {
      if (this.isConnected) {
        await mongoose.connection.close();
        this.isConnected = false;
        logger.info('✅ 数据库连接已关闭');
      }
    } catch (error) {
      logger.error('❌ 关闭数据库连接失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      readyState: mongoose.connection.readyState,
      host: mongoose.connection.host,
      port: mongoose.connection.port,
      name: mongoose.connection.name
    };
  }

  /**
   * 健康检查
   */
  async healthCheck() {
    try {
      if (!this.isConnected) {
        return { status: 'disconnected', message: '数据库未连接' };
      }

      // 执行简单查询测试连接
      await mongoose.connection.db.admin().ping();
      
      return {
        status: 'connected',
        message: '数据库连接正常',
        details: this.getConnectionStatus()
      };
    } catch (error) {
      return {
        status: 'error',
        message: '数据库连接异常',
        error: error.message
      };
    }
  }

  /**
   * 清理数据库（仅测试环境）
   */
  async clearDatabase() {
    if (process.env.NODE_ENV !== 'test') {
      throw new Error('只能在测试环境中清理数据库');
    }

    try {
      const collections = await mongoose.connection.db.collections();
      
      for (const collection of collections) {
        await collection.deleteMany({});
      }

      logger.info('🧹 测试数据库已清理');
    } catch (error) {
      logger.error('❌ 清理测试数据库失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 创建索引
   */
  async createIndexes() {
    try {
      // 用户集合索引
      await mongoose.connection.collection('users').createIndex({ username: 1 }, { unique: true });
      await mongoose.connection.collection('users').createIndex({ email: 1 }, { unique: true });
      
      // 地块集合索引
      await mongoose.connection.collection('fields').createIndex({ code: 1 }, { unique: true });
      await mongoose.connection.collection('fields').createIndex({ 'location.coordinates': '2dsphere' });
      
      // 作物集合索引
      await mongoose.connection.collection('crops').createIndex({ field: 1, plantDate: -1 });
      await mongoose.connection.collection('crops').createIndex({ status: 1, growthStage: 1 });
      
      // 农事操作集合索引
      await mongoose.connection.collection('farmingoperations').createIndex({ crop: 1, operationDate: -1 });
      await mongoose.connection.collection('farmingoperations').createIndex({ type: 1, operationDate: -1 });
      
      // 收获记录集合索引
      await mongoose.connection.collection('harvests').createIndex({ crop: 1, harvestDate: -1 });
      
      // 物资集合索引
      await mongoose.connection.collection('materials').createIndex({ category: 1, status: 1 });
      await mongoose.connection.collection('materials').createIndex({ name: 'text', description: 'text' });

      logger.info('📊 数据库索引创建完成');
    } catch (error) {
      logger.error('❌ 创建数据库索引失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 数据库统计信息
   */
  async getStats() {
    try {
      const stats = await mongoose.connection.db.stats();
      const collections = await mongoose.connection.db.listCollections().toArray();
      
      const collectionStats = {};
      for (const collection of collections) {
        const collStats = await mongoose.connection.db.collection(collection.name).stats();
        collectionStats[collection.name] = {
          count: collStats.count,
          size: collStats.size,
          avgObjSize: collStats.avgObjSize
        };
      }

      return {
        database: {
          name: stats.db,
          collections: stats.collections,
          objects: stats.objects,
          dataSize: stats.dataSize,
          storageSize: stats.storageSize,
          indexes: stats.indexes,
          indexSize: stats.indexSize
        },
        collections: collectionStats
      };
    } catch (error) {
      logger.error('❌ 获取数据库统计信息失败', { error: error.message });
      throw error;
    }
  }
}

// 创建单例实例
const databaseConfig = new DatabaseConfig();

module.exports = databaseConfig;
