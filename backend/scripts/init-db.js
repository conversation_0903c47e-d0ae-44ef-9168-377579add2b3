const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// 导入模型
const User = require('../src/models/User');
const Field = require('../src/models/Field');
const Crop = require('../src/models/Crop');
const Material = require('../src/models/Material');

// 连接数据库
const connectDB = async () => {
    try {
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/farm_management', {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });
        console.log('✅ 数据库连接成功');
    } catch (error) {
        console.error('❌ 数据库连接失败:', error);
        process.exit(1);
    }
};

// 创建默认用户
const createDefaultUsers = async () => {
    try {
        console.log('🔄 创建默认用户...');

        // 检查是否已存在管理员
        const adminExists = await User.findOne({username: 'admin'});
        if (adminExists) {
            console.log('⚠️  管理员用户已存在，跳过创建');
            return;
        }

        // 创建管理员用户
        const admin = new User({
            username: 'admin',
            email: '<EMAIL>',
            password: '123456',
            name: '系统管理员',
            role: 'admin',
            permissions: [
                'field_manage', 'crop_manage', 'farming_manage',
                'harvest_manage', 'material_manage', 'weather_view', 'dashboard_view',
                'user_manage', 'system_manage', 'system_config'
            ],
            status: 'active'
        });

        await admin.save();
        console.log('✅ 管理员用户创建成功 (admin/123456)');

        // 创建普通用户
        const users = [
            {
                username: 'manager',
                email: '<EMAIL>',
                password: '123456',
                name: '农场经理',
                role: 'manager',
                permissions: ['field_manage', 'crop_manage', 'farming_manage', 'harvest_manage', 'material_manage'],
                phone: '13800138001'
            },
            {
                username: 'worker',
                email: '<EMAIL>',
                password: '123456',
                name: '农场工人',
                role: 'worker',
                permissions: ['farming_manage', 'harvest_manage'],
                phone: '13800138002'
            }
        ];

        for (const userData of users) {
            const user = new User(userData);
            await user.save();
            console.log(`✅ 用户 ${userData.name} 创建成功`);
        }

    } catch (error) {
        console.error('❌ 创建默认用户失败:', error);
    }
};

// 创建示例地块
const createSampleFields = async () => {
    try {
        console.log('🔄 创建示例地块...');

        const fieldCount = await Field.countDocuments();
        if (fieldCount > 0) {
            console.log('⚠️  地块数据已存在，跳过创建');
            return;
        }

        const admin = await User.findOne({username: 'admin'});
        const manager = await User.findOne({username: 'manager'});

        const fields = [
            {
                name: '东区地块A',
                code: 'A001',
                area: 50.5,
                unit: '亩',
                location: {
                    type: 'Polygon',
                    coordinates: [[
                        [116.3974, 39.9093],
                        [116.3984, 39.9093],
                        [116.3984, 39.9103],
                        [116.3974, 39.9103],
                        [116.3974, 39.9093]
                    ]]
                },
                center: {latitude: 39.9098, longitude: 116.3979},
                soilType: '壤土',
                soilPH: 6.8,
                fertility: '中',
                irrigation: '滴灌',
                drainage: '良好',
                owner: admin._id,
                manager: manager._id,
                status: 'active'
            },
            {
                name: '西区地块B',
                code: 'B001',
                area: 75.2,
                unit: '亩',
                location: {
                    type: 'Polygon',
                    coordinates: [[
                        [116.3964, 39.9083],
                        [116.3974, 39.9083],
                        [116.3974, 39.9093],
                        [116.3964, 39.9093],
                        [116.3964, 39.9083]
                    ]]
                },
                center: {latitude: 39.9088, longitude: 116.3969},
                soilType: '沙壤土',
                soilPH: 7.2,
                fertility: '高',
                irrigation: '喷灌',
                drainage: '良好',
                owner: admin._id,
                manager: manager._id,
                status: 'active'
            },
            {
                name: '南区地块C',
                code: 'C001',
                area: 42.8,
                unit: '亩',
                location: {
                    type: 'Polygon',
                    coordinates: [[
                        [116.3974, 39.9073],
                        [116.3984, 39.9073],
                        [116.3984, 39.9083],
                        [116.3974, 39.9083],
                        [116.3974, 39.9073]
                    ]]
                },
                center: {latitude: 39.9078, longitude: 116.3979},
                soilType: '粘壤土',
                soilPH: 6.5,
                fertility: '中',
                irrigation: '漫灌',
                drainage: '一般',
                owner: admin._id,
                manager: manager._id,
                status: 'active'
            }
        ];

        for (const fieldData of fields) {
            const field = new Field(fieldData);
            await field.save();
            console.log(`✅ 地块 ${fieldData.name} 创建成功`);
        }

    } catch (error) {
        console.error('❌ 创建示例地块失败:', error);
    }
};

// 创建示例作物
const createSampleCrops = async () => {
    try {
        console.log('🔄 创建示例作物...');

        const cropCount = await Crop.countDocuments();
        if (cropCount > 0) {
            console.log('⚠️  作物数据已存在，跳过创建');
            return;
        }

        const fields = await Field.find().limit(3);

        console.log(fields)


        const planter = await User.findOne({username: 'manager'});

        const crops = [
            {
                name: '玉米',
                variety: '先玉335',
                category: '粮食作物',
                field: fields[0]._id,
                plantDate: new Date('2024-04-15'),
                expectedHarvestDate: new Date('2024-09-15'),
                plantingArea: 45.0,
                plantingDensity: 4500,
                expectedYield: 2250,
                yieldUnit: '公斤',
                seedSource: '种子公司A',
                seedCost: 1800,
                growthStage: '生长',
                healthStatus: '健康',
                season: '春季',
                year: 2024,
                planter: planter._id,
                status: 'active'
            },
            {
                name: '小麦',
                variety: '济麦22',
                category: '粮食作物',
                field: fields[1]._id,
                plantDate: new Date('2023-10-20'),
                expectedHarvestDate: new Date('2024-06-20'),
                actualHarvestDate: new Date('2024-06-18'),
                plantingArea: 70.0,
                plantingDensity: 3500,
                expectedYield: 3500,
                actualYield: 3680,
                yieldUnit: '公斤',
                seedSource: '种子公司B',
                seedCost: 2100,
                growthStage: '收获',
                healthStatus: '健康',
                season: '秋季',
                year: 2023,
                planter: planter._id,
                status: 'completed'
            },
            {
                name: '大豆',
                variety: '中黄13',
                category: '经济作物',
                field: fields[2]._id,
                plantDate: new Date('2024-05-10'),
                expectedHarvestDate: new Date('2024-10-10'),
                plantingArea: 40.0,
                plantingDensity: 2800,
                expectedYield: 1200,
                yieldUnit: '公斤',
                seedSource: '种子公司C',
                seedCost: 1200,
                growthStage: '开花',
                healthStatus: '健康',
                season: '春季',
                year: 2024,
                planter: planter._id,
                status: 'active'
            }
        ];

        for (const cropData of crops) {
            const crop = new Crop(cropData);
            await crop.save();
            console.log(`✅ 作物 ${cropData.name} 创建成功`);
        }

    } catch (error) {
        console.error('❌ 创建示例作物失败:', error);
    }
};

// 创建示例物资
const createSampleMaterials = async () => {
    try {
        console.log('🔄 创建示例物资...');

        const materialCount = await Material.countDocuments();
        if (materialCount > 0) {
            console.log('⚠️  物资数据已存在，跳过创建');
            return;
        }

        const admin = await User.findOne({username: 'admin'});

        const materials = [
            {
                name: '复合肥',
                code: 'FHF001',
                category: '肥料',
                subcategory: '复合肥料',
                specification: 'NPK 15-15-15',
                unit: '袋',
                inventory: {
                    currentStock: 50,
                    minStock: 10,
                    safetyStock: 20
                },
                pricing: {
                    purchasePrice: 120,
                    averagePrice: 125,
                    lastPrice: 130
                },
                suppliers: [
                    {
                        name: '肥料供应商A',
                        contact: '张经理',
                        phone: '13800138001',
                        price: 120
                    }
                ],
                createdBy: admin._id,
                status: 'active'
            },
            {
                name: '玉米种子',
                code: 'YMZ001',
                category: '种子',
                subcategory: '玉米种子',
                specification: '先玉335',
                unit: '袋',
                inventory: {
                    currentStock: 8,
                    minStock: 5,
                    safetyStock: 10
                },
                pricing: {
                    purchasePrice: 180,
                    averagePrice: 185,
                    lastPrice: 190
                },
                suppliers: [
                    {
                        name: '种子公司A',
                        contact: '李经理',
                        phone: '13800138002',
                        price: 180
                    }
                ],
                createdBy: admin._id,
                status: 'active'
            },
            {
                name: '除草剂',
                code: 'CCJ001',
                category: '农药',
                subcategory: '除草剂',
                specification: '草甘膦 41%',
                unit: '瓶',
                inventory: {
                    currentStock: 25,
                    minStock: 10,
                    safetyStock: 15
                },
                pricing: {
                    purchasePrice: 45,
                    averagePrice: 48,
                    lastPrice: 50
                },
                suppliers: [
                    {
                        name: '农药供应商A',
                        contact: '王经理',
                        phone: '13800138003',
                        price: 45
                    }
                ],
                createdBy: admin._id,
                status: 'active'
            }
        ];

        for (const materialData of materials) {
            const material = new Material(materialData);
            await material.save();
            console.log(`✅ 物资 ${materialData.name} 创建成功`);
        }

    } catch (error) {
        console.error('❌ 创建示例物资失败:', error);
    }
};

// 主函数
const initDatabase = async () => {
    try {
        console.log('🚀 开始初始化数据库...');

        await connectDB();

        await createDefaultUsers();
        await createSampleFields();
        await createSampleCrops();
        await createSampleMaterials();

        console.log('✅ 数据库初始化完成！');
        console.log('');
        console.log('📋 默认账号信息:');
        console.log('管理员: admin / 123456');
        console.log('经理: manager / 123456');
        console.log('工人: worker / 123456');

    } catch (error) {
        console.error('❌ 数据库初始化失败:', error);
    } finally {
        await mongoose.connection.close();
        console.log('🔌 数据库连接已关闭');
        process.exit(0);
    }
};

// 运行初始化
if (require.main === module) {
    initDatabase();
}

module.exports = {initDatabase};
