/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ConfirmButton: typeof import('./src/components/global/ConfirmButton.vue')['default']
    DataTable: typeof import('./src/components/global/DataTable.vue')['default']
    EmptyState: typeof import('./src/components/global/EmptyState.vue')['default']
    FormDialog: typeof import('./src/components/global/FormDialog.vue')['default']
    PageContainer: typeof import('./src/components/global/PageContainer.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchForm: typeof import('./src/components/global/SearchForm.vue')['default']
    StatusTag: typeof import('./src/components/global/StatusTag.vue')['default']
  }
}
