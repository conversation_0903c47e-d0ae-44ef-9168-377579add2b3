{"name": "farm-dashboard-screen", "version": "1.0.0", "description": "农场智慧管理系统数据大屏", "private": true, "scripts": {"dev": "vite --port 3001", "build": "vite build", "preview": "vite preview --port 3001", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "axios": "^1.5.0", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "@jiaminghi/data-view": "^2.10.0", "dayjs": "^1.11.9", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "animate.css": "^4.1.1", "countup.js": "^2.8.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "vite": "^4.4.9", "eslint": "^8.48.0", "eslint-plugin-vue": "^9.17.0", "sass": "^1.66.1", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}