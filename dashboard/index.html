<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>农场智慧管理系统 - 数据大屏</title>
    <meta name="description" content="农场智慧管理系统数据可视化大屏" />
    
    <style>
      body {
        margin: 0;
        padding: 0;
        background: #0a0e27;
        font-family: 'Microsoft YaHei', sans-serif;
        overflow: hidden;
      }
      
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        color: #00d4ff;
      }
      
      .loading-content {
        text-align: center;
      }
      
      .loading-spinner {
        width: 60px;
        height: 60px;
        border: 3px solid rgba(0, 212, 255, 0.3);
        border-top: 3px solid #00d4ff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        font-size: 18px;
        font-weight: 500;
        letter-spacing: 2px;
      }
      
      .loading-dots {
        display: inline-block;
        animation: dots 1.5s infinite;
      }
      
      @keyframes dots {
        0%, 20% { content: ''; }
        40% { content: '.'; }
        60% { content: '..'; }
        80%, 100% { content: '...'; }
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div id="loading">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <div class="loading-text">
            农场数据大屏加载中<span class="loading-dots"></span>
          </div>
        </div>
      </div>
    </div>
    
    <script type="module" src="/src/main.js"></script>
    
    <script>
      // 隐藏加载动画
      window.addEventListener('load', () => {
        const loading = document.getElementById('loading');
        if (loading) {
          setTimeout(() => {
            loading.style.opacity = '0';
            setTimeout(() => {
              loading.style.display = 'none';
            }, 500);
          }, 1000);
        }
      });
    </script>
  </body>
</html>
