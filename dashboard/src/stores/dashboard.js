import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import * as dashboardApi from '@/api/dashboard'

export const useDashboardStore = defineStore('dashboard', () => {
  // 状态
  const loading = ref(false)
  const error = ref(null)
  const lastUpdateTime = ref(null)
  
  // 概览数据
  const overview = ref({
    totalFields: 0,
    totalDevices: 0,
    totalCrops: 0,
    totalHarvest: 0,
    onlineDevices: 0,
    activeAlerts: 0,
    todayTasks: 0,
    monthlyRevenue: 0
  })
  
  // 统计数据
  const stats = ref({
    fields: {},
    devices: {},
    crops: {},
    harvest: {},
    alerts: {},
    tasks: {}
  })
  
  // 趋势数据
  const trends = ref({
    production: [],
    revenue: [],
    environment: []
  })
  
  // 分布数据
  const distributions = ref({
    deviceStatus: [],
    crops: [],
    fieldUtilization: []
  })
  
  // 实时数据
  const realTimeData = ref({
    sensors: {},
    cameras: {},
    weather: {}
  })
  
  // 系统状态
  const systemStatus = ref({
    health: 'good',
    resources: {
      cpu: 0,
      memory: 0,
      disk: 0,
      network: 0
    }
  })
  
  // 配置
  const config = ref({
    refreshInterval: 30000, // 30秒
    autoRefresh: true,
    theme: 'dark',
    layout: 'default'
  })
  
  // 计算属性
  const deviceOnlineRate = computed(() => {
    if (overview.value.totalDevices === 0) return 0
    return Math.round((overview.value.onlineDevices / overview.value.totalDevices) * 100)
  })
  
  const fieldUtilizationRate = computed(() => {
    const utilization = distributions.value.fieldUtilization
    if (!utilization.length) return 0
    
    const totalArea = utilization.reduce((sum, item) => sum + item.totalArea, 0)
    const usedArea = utilization.reduce((sum, item) => sum + item.usedArea, 0)
    
    return totalArea > 0 ? Math.round((usedArea / totalArea) * 100) : 0
  })
  
  const alertSeverityDistribution = computed(() => {
    const alerts = stats.value.alerts.levelDistribution || []
    const total = alerts.reduce((sum, item) => sum + item.count, 0)
    
    return alerts.map(item => ({
      ...item,
      percentage: total > 0 ? Math.round((item.count / total) * 100) : 0
    }))
  })
  
  // Actions
  const setLoading = (value) => {
    loading.value = value
  }
  
  const setError = (value) => {
    error.value = value
  }
  
  const updateLastUpdateTime = () => {
    lastUpdateTime.value = new Date()
  }
  
  // 获取概览数据
  const fetchOverview = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await dashboardApi.getDashboardOverview()
      overview.value = response.data
      updateLastUpdateTime()
    } catch (err) {
      setError(err.message)
      console.error('获取概览数据失败:', err)
    } finally {
      setLoading(false)
    }
  }
  
  // 获取统计数据
  const fetchStats = async () => {
    try {
      const [
        fieldStats,
        deviceStats,
        cropStats,
        harvestStats,
        alertStats,
        taskStats
      ] = await Promise.allSettled([
        dashboardApi.getFieldStats(),
        dashboardApi.getDeviceStats(),
        dashboardApi.getCropStats(),
        dashboardApi.getHarvestStats(),
        dashboardApi.getAlertStats(),
        dashboardApi.getTaskStats()
      ])
      
      stats.value = {
        fields: fieldStats.status === 'fulfilled' ? fieldStats.value.data : {},
        devices: deviceStats.status === 'fulfilled' ? deviceStats.value.data : {},
        crops: cropStats.status === 'fulfilled' ? cropStats.value.data : {},
        harvest: harvestStats.status === 'fulfilled' ? harvestStats.value.data : {},
        alerts: alertStats.status === 'fulfilled' ? alertStats.value.data : {},
        tasks: taskStats.status === 'fulfilled' ? taskStats.value.data : {}
      }
    } catch (err) {
      console.error('获取统计数据失败:', err)
    }
  }
  
  // 获取趋势数据
  const fetchTrends = async (timeRange = '30d') => {
    try {
      const [
        productionTrend,
        revenueTrend,
        environmentTrend
      ] = await Promise.allSettled([
        dashboardApi.getProductionTrend({ timeRange }),
        dashboardApi.getRevenueTrend({ timeRange }),
        dashboardApi.getEnvironmentTrend({ timeRange })
      ])
      
      trends.value = {
        production: productionTrend.status === 'fulfilled' ? productionTrend.value.data : [],
        revenue: revenueTrend.status === 'fulfilled' ? revenueTrend.value.data : [],
        environment: environmentTrend.status === 'fulfilled' ? environmentTrend.value.data : []
      }
    } catch (err) {
      console.error('获取趋势数据失败:', err)
    }
  }
  
  // 获取分布数据
  const fetchDistributions = async () => {
    try {
      const [
        deviceStatus,
        cropDistribution,
        fieldUtilization
      ] = await Promise.allSettled([
        dashboardApi.getDeviceStatusDistribution(),
        dashboardApi.getCropDistribution(),
        dashboardApi.getFieldUtilization()
      ])
      
      distributions.value = {
        deviceStatus: deviceStatus.status === 'fulfilled' ? deviceStatus.value.data : [],
        crops: cropDistribution.status === 'fulfilled' ? cropDistribution.value.data : [],
        fieldUtilization: fieldUtilization.status === 'fulfilled' ? fieldUtilization.value.data : []
      }
    } catch (err) {
      console.error('获取分布数据失败:', err)
    }
  }
  
  // 获取实时数据
  const fetchRealTimeData = async () => {
    try {
      const [
        sensorData,
        cameraStatus,
        weatherInfo
      ] = await Promise.allSettled([
        dashboardApi.getRealTimeStats(),
        dashboardApi.getCameraStatus(),
        dashboardApi.getWeatherInfo()
      ])
      
      realTimeData.value = {
        sensors: sensorData.status === 'fulfilled' ? sensorData.value.data : {},
        cameras: cameraStatus.status === 'fulfilled' ? cameraStatus.value.data : {},
        weather: weatherInfo.status === 'fulfilled' ? weatherInfo.value.data : {}
      }
    } catch (err) {
      console.error('获取实时数据失败:', err)
    }
  }
  
  // 获取系统状态
  const fetchSystemStatus = async () => {
    try {
      const [health, resources] = await Promise.allSettled([
        dashboardApi.getSystemHealth(),
        dashboardApi.getSystemResources()
      ])
      
      systemStatus.value = {
        health: health.status === 'fulfilled' ? health.value.data.status : 'unknown',
        resources: resources.status === 'fulfilled' ? resources.value.data : {
          cpu: 0,
          memory: 0,
          disk: 0,
          network: 0
        }
      }
    } catch (err) {
      console.error('获取系统状态失败:', err)
    }
  }
  
  // 获取所有数据
  const fetchAllData = async () => {
    await Promise.all([
      fetchOverview(),
      fetchStats(),
      fetchTrends(),
      fetchDistributions(),
      fetchRealTimeData(),
      fetchSystemStatus()
    ])
  }
  
  // 刷新数据
  const refreshData = async () => {
    await fetchAllData()
  }
  
  // 获取配置
  const fetchConfig = async () => {
    try {
      const response = await dashboardApi.getDashboardConfig()
      config.value = { ...config.value, ...response.data }
    } catch (err) {
      console.error('获取配置失败:', err)
    }
  }
  
  // 保存配置
  const saveConfig = async (newConfig) => {
    try {
      await dashboardApi.saveDashboardConfig(newConfig)
      config.value = { ...config.value, ...newConfig }
    } catch (err) {
      console.error('保存配置失败:', err)
      throw err
    }
  }
  
  // 重置配置
  const resetConfig = async () => {
    try {
      await dashboardApi.resetDashboardConfig()
      config.value = {
        refreshInterval: 30000,
        autoRefresh: true,
        theme: 'dark',
        layout: 'default'
      }
    } catch (err) {
      console.error('重置配置失败:', err)
      throw err
    }
  }
  
  // 清除数据
  const clearData = () => {
    overview.value = {
      totalFields: 0,
      totalDevices: 0,
      totalCrops: 0,
      totalHarvest: 0,
      onlineDevices: 0,
      activeAlerts: 0,
      todayTasks: 0,
      monthlyRevenue: 0
    }
    stats.value = {
      fields: {},
      devices: {},
      crops: {},
      harvest: {},
      alerts: {},
      tasks: {}
    }
    trends.value = {
      production: [],
      revenue: [],
      environment: []
    }
    distributions.value = {
      deviceStatus: [],
      crops: [],
      fieldUtilization: []
    }
    realTimeData.value = {
      sensors: {},
      cameras: {},
      weather: {}
    }
    systemStatus.value = {
      health: 'good',
      resources: {
        cpu: 0,
        memory: 0,
        disk: 0,
        network: 0
      }
    }
    error.value = null
    lastUpdateTime.value = null
  }
  
  return {
    // 状态
    loading,
    error,
    lastUpdateTime,
    overview,
    stats,
    trends,
    distributions,
    realTimeData,
    systemStatus,
    config,
    
    // 计算属性
    deviceOnlineRate,
    fieldUtilizationRate,
    alertSeverityDistribution,
    
    // Actions
    setLoading,
    setError,
    updateLastUpdateTime,
    fetchOverview,
    fetchStats,
    fetchTrends,
    fetchDistributions,
    fetchRealTimeData,
    fetchSystemStatus,
    fetchAllData,
    refreshData,
    fetchConfig,
    saveConfig,
    resetConfig,
    clearData
  }
})
