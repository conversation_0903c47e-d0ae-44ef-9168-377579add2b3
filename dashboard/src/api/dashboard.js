import { api, cachedRequest } from './index'

// 获取仪表盘概览数据
export const getDashboardOverview = () => {
  return cachedRequest('dashboard-overview', () => 
    api.get('/dashboard/overview'), 
    2 * 60 * 1000 // 2分钟缓存
  )
}

// 获取实时统计数据
export const getRealTimeStats = () => {
  return api.get('/dashboard/stats/realtime')
}

// 获取地块统计
export const getFieldStats = () => {
  return cachedRequest('field-stats', () => 
    api.get('/dashboard/stats/fields'),
    5 * 60 * 1000 // 5分钟缓存
  )
}

// 获取设备统计
export const getDeviceStats = () => {
  return api.get('/dashboard/stats/devices')
}

// 获取作物统计
export const getCropStats = () => {
  return cachedRequest('crop-stats', () => 
    api.get('/dashboard/stats/crops'),
    10 * 60 * 1000 // 10分钟缓存
  )
}

// 获取收获统计
export const getHarvestStats = (params = {}) => {
  return api.get('/dashboard/stats/harvest', params)
}

// 获取产量趋势数据
export const getProductionTrend = (params = {}) => {
  const { timeRange = '30d', fieldId, cropType } = params
  return api.get('/dashboard/trends/production', {
    timeRange,
    fieldId,
    cropType
  })
}

// 获取收入趋势数据
export const getRevenueTrend = (params = {}) => {
  const { timeRange = '30d', fieldId } = params
  return api.get('/dashboard/trends/revenue', {
    timeRange,
    fieldId
  })
}

// 获取环境数据趋势
export const getEnvironmentTrend = (params = {}) => {
  const { timeRange = '24h', deviceId, dataType } = params
  return api.get('/dashboard/trends/environment', {
    timeRange,
    deviceId,
    dataType
  })
}

// 获取设备状态分布
export const getDeviceStatusDistribution = () => {
  return api.get('/dashboard/distribution/device-status')
}

// 获取作物分布
export const getCropDistribution = () => {
  return cachedRequest('crop-distribution', () => 
    api.get('/dashboard/distribution/crops'),
    15 * 60 * 1000 // 15分钟缓存
  )
}

// 获取地块利用率
export const getFieldUtilization = () => {
  return cachedRequest('field-utilization', () => 
    api.get('/dashboard/utilization/fields'),
    30 * 60 * 1000 // 30分钟缓存
  )
}

// 获取预警统计
export const getAlertStats = () => {
  return api.get('/dashboard/stats/alerts')
}

// 获取最近预警
export const getRecentAlerts = (limit = 10) => {
  return api.get('/dashboard/alerts/recent', { limit })
}

// 获取任务统计
export const getTaskStats = () => {
  return api.get('/dashboard/stats/tasks')
}

// 获取今日任务
export const getTodayTasks = () => {
  return api.get('/dashboard/tasks/today')
}

// 获取天气信息
export const getWeatherInfo = (location) => {
  return cachedRequest(`weather-${location}`, () => 
    api.get('/dashboard/weather', { location }),
    30 * 60 * 1000 // 30分钟缓存
  )
}

// 获取天气预报
export const getWeatherForecast = (location, days = 7) => {
  return cachedRequest(`weather-forecast-${location}-${days}`, () => 
    api.get('/dashboard/weather/forecast', { location, days }),
    60 * 60 * 1000 // 1小时缓存
  )
}

// 获取传感器实时数据
export const getSensorRealTimeData = (deviceId) => {
  return api.get(`/dashboard/sensors/${deviceId}/realtime`)
}

// 获取传感器历史数据
export const getSensorHistoryData = (deviceId, params = {}) => {
  const { timeRange = '24h', dataType } = params
  return api.get(`/dashboard/sensors/${deviceId}/history`, {
    timeRange,
    dataType
  })
}

// 获取摄像头状态
export const getCameraStatus = () => {
  return api.get('/dashboard/cameras/status')
}

// 获取系统健康状态
export const getSystemHealth = () => {
  return api.get('/dashboard/system/health')
}

// 获取系统资源使用情况
export const getSystemResources = () => {
  return api.get('/dashboard/system/resources')
}

// 获取用户活动统计
export const getUserActivityStats = () => {
  return api.get('/dashboard/stats/user-activity')
}

// 获取操作日志
export const getOperationLogs = (params = {}) => {
  const { page = 1, limit = 20, action, userId } = params
  return api.get('/dashboard/logs/operations', {
    page,
    limit,
    action,
    userId
  })
}

// 获取数据导出
export const exportDashboardData = (params = {}) => {
  const { type, timeRange, format = 'excel' } = params
  return api.download('/dashboard/export', {
    type,
    timeRange,
    format
  })
}

// 获取自定义图表数据
export const getCustomChartData = (chartConfig) => {
  return api.post('/dashboard/charts/custom', chartConfig)
}

// 保存仪表盘配置
export const saveDashboardConfig = (config) => {
  return api.post('/dashboard/config', config)
}

// 获取仪表盘配置
export const getDashboardConfig = () => {
  return api.get('/dashboard/config')
}

// 重置仪表盘配置
export const resetDashboardConfig = () => {
  return api.delete('/dashboard/config')
}

// 获取数据刷新时间
export const getDataRefreshTime = () => {
  return api.get('/dashboard/refresh-time')
}

// 手动刷新数据
export const refreshDashboardData = () => {
  return api.post('/dashboard/refresh')
}
