import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 添加认证token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加请求时间戳
    config.metadata = { startTime: new Date() }
    
    return config
  },
  error => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    // 计算请求耗时
    const endTime = new Date()
    const duration = endTime - response.config.metadata.startTime
    console.log(`API请求耗时: ${duration}ms - ${response.config.url}`)
    
    const { data } = response
    
    // 统一处理响应数据
    if (data.success) {
      return data
    } else {
      // 业务错误
      ElMessage.error(data.message || '请求失败')
      return Promise.reject(new Error(data.message || '请求失败'))
    }
  },
  error => {
    console.error('响应拦截器错误:', error)
    
    // 网络错误处理
    if (!error.response) {
      ElMessage.error('网络连接失败，请检查网络设置')
      return Promise.reject(error)
    }
    
    const { status, data } = error.response
    
    switch (status) {
      case 401:
        ElMessage.error('登录已过期，请重新登录')
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
        window.location.href = '/login'
        break
      case 403:
        ElMessage.error('没有权限访问该资源')
        break
      case 404:
        ElMessage.error('请求的资源不存在')
        break
      case 422:
        ElMessage.error(data.message || '数据验证失败')
        break
      case 429:
        ElMessage.error('请求过于频繁，请稍后再试')
        break
      case 500:
        ElMessage.error('服务器内部错误')
        break
      case 502:
        ElMessage.error('网关错误')
        break
      case 503:
        ElMessage.error('服务暂时不可用')
        break
      default:
        ElMessage.error(data?.message || `请求失败 (${status})`)
    }
    
    return Promise.reject(error)
  }
)

// 请求方法封装
export const api = {
  // GET请求
  get(url, params = {}, config = {}) {
    return request({
      method: 'GET',
      url,
      params,
      ...config
    })
  },
  
  // POST请求
  post(url, data = {}, config = {}) {
    return request({
      method: 'POST',
      url,
      data,
      ...config
    })
  },
  
  // PUT请求
  put(url, data = {}, config = {}) {
    return request({
      method: 'PUT',
      url,
      data,
      ...config
    })
  },
  
  // DELETE请求
  delete(url, config = {}) {
    return request({
      method: 'DELETE',
      url,
      ...config
    })
  },
  
  // PATCH请求
  patch(url, data = {}, config = {}) {
    return request({
      method: 'PATCH',
      url,
      data,
      ...config
    })
  },
  
  // 文件上传
  upload(url, formData, config = {}) {
    return request({
      method: 'POST',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      ...config
    })
  },
  
  // 文件下载
  download(url, params = {}, config = {}) {
    return request({
      method: 'GET',
      url,
      params,
      responseType: 'blob',
      ...config
    })
  }
}

// 批量请求
export const batchRequest = (requests) => {
  return Promise.allSettled(requests.map(req => {
    const { method = 'GET', url, data, params, ...config } = req
    return api[method.toLowerCase()](url, method === 'GET' ? params : data, config)
  }))
}

// 请求重试
export const retryRequest = async (requestFn, maxRetries = 3, delay = 1000) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await requestFn()
    } catch (error) {
      if (i === maxRetries - 1) {
        throw error
      }
      
      // 指数退避
      const waitTime = delay * Math.pow(2, i)
      await new Promise(resolve => setTimeout(resolve, waitTime))
    }
  }
}

// 请求缓存
const cache = new Map()

export const cachedRequest = async (key, requestFn, ttl = 5 * 60 * 1000) => {
  const cached = cache.get(key)
  
  if (cached && Date.now() - cached.timestamp < ttl) {
    return cached.data
  }
  
  try {
    const data = await requestFn()
    cache.set(key, {
      data,
      timestamp: Date.now()
    })
    return data
  } catch (error) {
    // 如果有缓存数据，在请求失败时返回缓存
    if (cached) {
      console.warn('请求失败，返回缓存数据:', error)
      return cached.data
    }
    throw error
  }
}

// 清除缓存
export const clearCache = (key) => {
  if (key) {
    cache.delete(key)
  } else {
    cache.clear()
  }
}

// 请求取消
export const createCancelToken = () => {
  return axios.CancelToken.source()
}

// 检查请求是否被取消
export const isCancel = axios.isCancel

// 导出默认实例
export default request
