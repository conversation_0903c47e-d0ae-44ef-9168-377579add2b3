// 颜色变量
$primary-color: #409eff;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$info-color: #909399;

// 文本颜色
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$text-placeholder: #c0c4cc;

// 边框颜色
$border-base: #dcdfe6;
$border-light: #e4e7ed;
$border-lighter: #ebeef5;
$border-extra-light: #f2f6fc;

// 背景颜色
$bg-color: #ffffff;
$bg-color-page: #f2f3f5;
$bg-color-overlay: rgba(255, 255, 255, 0.9);

// 阴影
$box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
$box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

// 字体
$font-size-extra-small: 12px;
$font-size-small: 13px;
$font-size-base: 14px;
$font-size-medium: 16px;
$font-size-large: 18px;
$font-size-extra-large: 20px;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;
$spacing-xl: 20px;
$spacing-xxl: 24px;

// 圆角
$border-radius-base: 4px;
$border-radius-small: 2px;
$border-radius-large: 6px;
$border-radius-round: 20px;
$border-radius-circle: 100%;

// 过渡
$transition-base: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
$transition-fade: opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
$transition-md-fade: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);

// 布局
$header-height: 50px;
$sidebar-width: 210px;
$sidebar-width-collapsed: 54px;
$tags-view-height: 34px;

// 响应式断点
$mobile: 768px;
$tablet: 992px;
$desktop: 1200px;
$widescreen: 1920px;

// Z-index
$z-index-normal: 1;
$z-index-top: 1000;
$z-index-popper: 2000;
$z-index-modal: 3000;

// 农业相关颜色
$farm-green: #52c41a;
$farm-blue: #1890ff;
$farm-yellow: #faad14;
$farm-orange: #fa8c16;
$farm-red: #f5222d;
$farm-purple: #722ed1;
$farm-cyan: #13c2c2;
$farm-lime: #a0d911;

// 状态颜色
$status-success: #52c41a;
$status-processing: #1890ff;
$status-error: #ff4d4f;
$status-warning: #faad14;
$status-default: #d9d9d9;

// 渐变色
$gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
$gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
$gradient-danger: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);

// 农场主题渐变
$gradient-farm-1: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
$gradient-farm-2: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
$gradient-farm-3: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);
$gradient-farm-4: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);

// 暗色主题
$dark-bg-color: #141414;
$dark-bg-color-light: #1f1f1f;
$dark-text-color: rgba(255, 255, 255, 0.85);
$dark-text-color-secondary: rgba(255, 255, 255, 0.65);
$dark-border-color: #303030;

// 混合函数
@function tint($color, $percentage) {
  @return mix(white, $color, $percentage);
}

@function shade($color, $percentage) {
  @return mix(black, $color, $percentage);
}
