import { createApp } from 'vue'
import { createPinia } from 'pinia'
import dataV from '@jiaminghi/data-view'
import 'animate.css'

import App from './App.vue'
import router from './router'
import './styles/index.scss'

// 导入全局组件
import GlobalComponents from './components/global'

const app = createApp(App)

// 使用插件
app.use(createPinia())
app.use(router)
app.use(dataV)

// 注册全局组件
app.use(GlobalComponents)

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err, info)
}

app.mount('#app')

// 移除加载动画
const loading = document.getElementById('loading')
if (loading) {
  loading.style.display = 'none'
}
