<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue'

// 自适应屏幕尺寸
const handleResize = () => {
  const scale = Math.min(
    window.innerWidth / 1920,
    window.innerHeight / 1080
  )
  
  document.documentElement.style.setProperty('--scale', scale)
}

onMounted(() => {
  handleResize()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss">
:root {
  --scale: 1;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #0a0e27;
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

#app {
  width: 100vw;
  height: 100vh;
  transform: scale(var(--scale));
  transform-origin: 0 0;
  overflow: hidden;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 212, 255, 0.6);
  border-radius: 3px;
  
  &:hover {
    background: rgba(0, 212, 255, 0.8);
  }
}

// 文字选择样式
::selection {
  background: rgba(0, 212, 255, 0.3);
  color: #fff;
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(20px);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}

// 响应式断点
@media screen and (max-width: 1366px) {
  #app {
    transform: scale(0.8);
  }
}

@media screen and (max-width: 1024px) {
  #app {
    transform: scale(0.7);
  }
}

@media screen and (max-width: 768px) {
  #app {
    transform: scale(0.6);
  }
}
</style>
