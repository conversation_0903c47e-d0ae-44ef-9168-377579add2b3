<template>
  <div class="dashboard-container">
    <!-- 头部标题栏 -->
    <header class="dashboard-header">
      <div class="header-left">
        <div class="logo">
          <img src="/logo.svg" alt="Logo" />
        </div>
        <div class="title">
          <h1>智慧农场管理系统</h1>
          <p>Farm Intelligent Management System</p>
        </div>
      </div>
      <div class="header-center">
        <div class="weather-info">
          <div class="weather-icon">
            <img :src="weatherData.icon" :alt="weatherData.condition" />
          </div>
          <div class="weather-details">
            <div class="temperature">{{ weatherData.temperature }}°C</div>
            <div class="condition">{{ weatherData.condition }}</div>
          </div>
        </div>
      </div>
      <div class="header-right">
        <div class="datetime">
          <div class="time">{{ currentTime }}</div>
          <div class="date">{{ currentDate }}</div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="dashboard-main">
      <!-- 第一行：核心指标卡片 -->
      <section class="metrics-section">
        <div
          v-for="(metric, index) in coreMetrics"
          :key="metric.key"
          class="metric-card"
          :style="{ animationDelay: `${index * 0.1}s` }"
        >
          <div class="metric-icon" :style="{ background: metric.gradient }">
            <i :class="metric.icon"></i>
          </div>
          <div class="metric-content">
            <div class="metric-value">
              <count-up :end-val="metric.value" :duration="2" />
              <span class="unit">{{ metric.unit }}</span>
            </div>
            <div class="metric-label">{{ metric.label }}</div>
            <div class="metric-change" :class="metric.changeType">
              <i :class="metric.changeIcon"></i>
              <span>{{ metric.change }}</span>
            </div>
          </div>
          <div class="metric-chart">
            <div :ref="el => metricChartRefs[metric.key] = el" class="mini-chart"></div>
          </div>
        </div>
      </section>

      <!-- 第二行：主要图表区域 -->
      <section class="charts-section">
        <!-- 产量趋势图 -->
        <div class="chart-panel large">
          <div class="panel-header">
            <h3>
              <i class="fas fa-chart-line"></i>
              产量趋势分析
            </h3>
            <div class="panel-controls">
              <el-select v-model="yieldPeriod" size="small" @change="updateYieldChart">
                <el-option label="近7天" value="7d" />
                <el-option label="近30天" value="30d" />
                <el-option label="近3个月" value="3m" />
                <el-option label="近1年" value="1y" />
              </el-select>
            </div>
          </div>
          <div class="panel-content">
            <div ref="yieldChartRef" class="chart-container"></div>
          </div>
        </div>

        <!-- 收入分析图 -->
        <div class="chart-panel medium">
          <div class="panel-header">
            <h3>
              <i class="fas fa-coins"></i>
              收入分析
            </h3>
          </div>
          <div class="panel-content">
            <div ref="revenueChartRef" class="chart-container"></div>
          </div>
        </div>

        <!-- 地块状态分布 -->
        <div class="chart-panel medium">
          <div class="panel-header">
            <h3>
              <i class="fas fa-map-marked-alt"></i>
              地块状态
            </h3>
          </div>
          <div class="panel-content">
            <div ref="fieldStatusChartRef" class="chart-container"></div>
          </div>
        </div>
      </section>

      <!-- 第三行：详细信息面板 -->
      <section class="details-section">
        <!-- 实时监控 -->
        <div class="detail-panel">
          <div class="panel-header">
            <h3>
              <i class="fas fa-eye"></i>
              实时监控
            </h3>
          </div>
          <div class="panel-content">
            <div class="monitor-grid">
              <div class="monitor-item" v-for="item in monitorData" :key="item.name">
                <div class="monitor-icon" :style="{ color: item.color }">
                  <i :class="item.icon"></i>
                </div>
                <div class="monitor-info">
                  <div class="monitor-value">{{ item.value }}</div>
                  <div class="monitor-label">{{ item.name }}</div>
                </div>
                <div class="monitor-status" :class="item.status">
                  <i :class="getStatusIcon(item.status)"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 库存预警 -->
        <div class="detail-panel">
          <div class="panel-header">
            <h3>
              <i class="fas fa-exclamation-triangle"></i>
              库存预警
            </h3>
          </div>
          <div class="panel-content">
            <div class="alert-list">
              <div
                v-for="alert in stockAlerts"
                :key="alert.id"
                class="alert-item"
                :class="alert.level"
              >
                <div class="alert-icon">
                  <i :class="alert.icon"></i>
                </div>
                <div class="alert-content">
                  <div class="alert-title">{{ alert.title }}</div>
                  <div class="alert-desc">{{ alert.description }}</div>
                </div>
                <div class="alert-action">
                  <el-button size="small" type="primary" text>处理</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 今日任务 -->
        <div class="detail-panel">
          <div class="panel-header">
            <h3>
              <i class="fas fa-tasks"></i>
              今日任务
            </h3>
            <div class="task-progress">
              <el-progress
                :percentage="taskProgress"
                :show-text="false"
                :stroke-width="4"
                color="#00d4ff"
              />
              <span class="progress-text">{{ completedTasks }}/{{ totalTasks }}</span>
            </div>
          </div>
          <div class="panel-content">
            <div class="task-list">
              <div
                v-for="task in todayTasks"
                :key="task.id"
                class="task-item"
                :class="task.status"
              >
                <div class="task-status">
                  <i :class="getTaskIcon(task.status)"></i>
                </div>
                <div class="task-content">
                  <div class="task-title">{{ task.title }}</div>
                  <div class="task-meta">
                    <span class="task-time">{{ task.time }}</span>
                    <span class="task-field">{{ task.field }}</span>
                  </div>
                </div>
                <div class="task-priority" :class="task.priority">
                  <i class="fas fa-flag"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 地块地图 -->
        <div class="detail-panel">
          <div class="panel-header">
            <h3>
              <i class="fas fa-map"></i>
              地块分布
            </h3>
          </div>
          <div class="panel-content">
            <div ref="mapRef" class="map-container">
              <!-- 这里可以集成地图组件 -->
              <div class="map-placeholder">
                <i class="fas fa-map-marked-alt"></i>
                <p>地块分布图</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 底部状态栏 -->
    <footer class="dashboard-footer">
      <div class="footer-left">
        <div class="system-status">
          <span class="status-dot" :class="systemStatus.class"></span>
          <span>{{ systemStatus.text }}</span>
        </div>
      </div>
      <div class="footer-center">
        <div class="data-stats">
          <span>数据更新: {{ lastUpdateTime }}</span>
          <span class="separator">|</span>
          <span>在线设备: {{ onlineDevices }}</span>
          <span class="separator">|</span>
          <span>数据完整性: {{ dataIntegrity }}%</span>
        </div>
      </div>
      <div class="footer-right">
        <div class="version-info">
          <span>Version 3.0.0</span>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick, computed } from 'vue'
import * as echarts from 'echarts'

// 响应式数据
const currentTime = ref('')
const currentDate = ref('')
const lastUpdateTime = ref('')
const yieldPeriod = ref('30d')
const onlineDevices = ref(12)
const dataIntegrity = ref(98.5)

// 图表引用
const yieldChartRef = ref()
const revenueChartRef = ref()
const fieldStatusChartRef = ref()
const mapRef = ref()
const metricChartRefs = reactive({})

// 图表实例
let yieldChart = null
let revenueChart = null
let fieldStatusChart = null

// 核心指标数据
const coreMetrics = ref([
  {
    key: 'fields',
    label: '地块总数',
    value: 12,
    unit: '个',
    icon: 'fas fa-map-marked-alt',
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    change: '+2 本月',
    changeType: 'positive',
    changeIcon: 'fas fa-arrow-up'
  },
  {
    key: 'crops',
    label: '作物种类',
    value: 8,
    unit: '种',
    icon: 'fas fa-seedling',
    gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    change: '+1 本月',
    changeType: 'positive',
    changeIcon: 'fas fa-arrow-up'
  },
  {
    key: 'yield',
    label: '本月产量',
    value: 2.8,
    unit: '吨',
    icon: 'fas fa-weight-hanging',
    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    change: '+15.2%',
    changeType: 'positive',
    changeIcon: 'fas fa-arrow-up'
  },
  {
    key: 'revenue',
    label: '本月收入',
    value: 45600,
    unit: '元',
    icon: 'fas fa-coins',
    gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    change: '+12.5%',
    changeType: 'positive',
    changeIcon: 'fas fa-arrow-up'
  }
])

// 天气数据
const weatherData = ref({
  temperature: 25,
  condition: '晴天',
  icon: '/images/weather/sunny.png'
})

// 监控数据
const monitorData = ref([
  { name: '土壤湿度', value: '65%', icon: 'fas fa-tint', color: '#409eff', status: 'normal' },
  { name: '空气温度', value: '25°C', icon: 'fas fa-thermometer-half', color: '#67c23a', status: 'normal' },
  { name: '光照强度', value: '850lx', icon: 'fas fa-sun', color: '#e6a23c', status: 'normal' },
  { name: '风速', value: '3.2m/s', icon: 'fas fa-wind', color: '#909399', status: 'normal' }
])

// 库存预警
const stockAlerts = ref([
  {
    id: 1,
    title: '复合肥库存不足',
    description: '当前库存: 5袋，建议及时补充',
    level: 'warning',
    icon: 'fas fa-exclamation-triangle'
  },
  {
    id: 2,
    title: '农药即将过期',
    description: '除草剂将于3天后过期，请及时使用',
    level: 'danger',
    icon: 'fas fa-exclamation-circle'
  },
  {
    id: 3,
    title: '种子库存充足',
    description: '玉米种子库存正常，无需补充',
    level: 'success',
    icon: 'fas fa-check-circle'
  }
])

// 今日任务
const todayTasks = ref([
  {
    id: 1,
    title: '地块A浇灌作业',
    time: '08:00',
    field: '地块A',
    status: 'completed',
    priority: 'high'
  },
  {
    id: 2,
    title: '地块B施肥作业',
    time: '10:00',
    field: '地块B',
    status: 'in-progress',
    priority: 'medium'
  },
  {
    id: 3,
    title: '地块C除草作业',
    time: '14:00',
    field: '地块C',
    status: 'pending',
    priority: 'low'
  },
  {
    id: 4,
    title: '设备维护检查',
    time: '16:00',
    field: '全部',
    status: 'pending',
    priority: 'high'
  }
])

// 系统状态
const systemStatus = ref({
  class: 'online',
  text: '系统运行正常'
})

// 计算属性
const completedTasks = computed(() => {
  return todayTasks.value.filter(task => task.status === 'completed').length
})

const totalTasks = computed(() => {
  return todayTasks.value.length
})

const taskProgress = computed(() => {
  return Math.round((completedTasks.value / totalTasks.value) * 100)
})

// 方法
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', { hour12: false })
  currentDate.value = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
  lastUpdateTime.value = now.toLocaleString('zh-CN')
}

const initCharts = () => {
  initYieldChart()
  initRevenueChart()
  initFieldStatusChart()
  initMetricCharts()
}

const initYieldChart = () => {
  if (!yieldChartRef.value) return

  yieldChart = echarts.init(yieldChartRef.value)
  updateYieldChart()
}

const updateYieldChart = () => {
  if (!yieldChart) return

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00d4ff',
      textStyle: { color: '#fff' }
    },
    legend: {
      data: ['玉米', '小麦', '大豆'],
      textStyle: { color: '#fff' },
      top: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月'],
      axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
      axisLabel: { color: '#999' },
      splitLine: { show: false }
    },
    yAxis: {
      type: 'value',
      name: '产量(吨)',
      nameTextStyle: { color: '#999' },
      axisLine: { show: false },
      axisLabel: { color: '#999' },
      splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } }
    },
    series: [
      {
        name: '玉米',
        type: 'line',
        data: [12, 15, 18, 22, 25, 28],
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: { color: '#00d4ff', width: 3 },
        itemStyle: { color: '#00d4ff' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(0, 212, 255, 0.3)' },
              { offset: 1, color: 'rgba(0, 212, 255, 0.1)' }
            ]
          }
        }
      },
      {
        name: '小麦',
        type: 'line',
        data: [8, 10, 12, 15, 18, 20],
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: { color: '#00ff88', width: 3 },
        itemStyle: { color: '#00ff88' }
      },
      {
        name: '大豆',
        type: 'line',
        data: [5, 6, 8, 10, 12, 15],
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: { color: '#ffaa00', width: 3 },
        itemStyle: { color: '#ffaa00' }
      }
    ]
  }

  yieldChart.setOption(option)
}

const getStatusIcon = (status) => {
  const iconMap = {
    'normal': 'fas fa-check-circle',
    'warning': 'fas fa-exclamation-triangle',
    'danger': 'fas fa-exclamation-circle'
  }
  return iconMap[status] || 'fas fa-question-circle'
}

const getTaskIcon = (status) => {
  const iconMap = {
    'completed': 'fas fa-check-circle',
    'in-progress': 'fas fa-clock',
    'pending': 'fas fa-circle'
  }
  return iconMap[status] || 'fas fa-circle'
}

// 定时器
let timeTimer = null

// 生命周期
onMounted(() => {
  updateTime()
  timeTimer = setInterval(updateTime, 1000)

  nextTick(() => {
    initCharts()
  })
})

onUnmounted(() => {
  if (timeTimer) clearInterval(timeTimer)

  if (yieldChart) yieldChart.dispose()
  if (revenueChart) revenueChart.dispose()
  if (fieldStatusChart) fieldStatusChart.dispose()
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0a0e27 0%, #1a1a2e 50%, #16213e 100%);
  color: #fff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dashboard-header {
  height: 80px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 212, 255, 0.3);
  display: flex;
  align-items: center;
  padding: 0 30px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 20px;

    .logo img {
      width: 50px;
      height: 50px;
    }

    .title {
      h1 {
        font-size: 24px;
        font-weight: 600;
        margin: 0;
        background: linear-gradient(45deg, #00d4ff, #00ff88);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      p {
        font-size: 12px;
        color: #999;
        margin: 2px 0 0;
      }
    }
  }

  .header-center {
    flex: 1;
    display: flex;
    justify-content: center;

    .weather-info {
      display: flex;
      align-items: center;
      gap: 12px;
      background: rgba(255, 255, 255, 0.1);
      padding: 8px 16px;
      border-radius: 20px;

      .weather-icon img {
        width: 32px;
        height: 32px;
      }

      .weather-details {
        .temperature {
          font-size: 18px;
          font-weight: 600;
          color: #00d4ff;
        }

        .condition {
          font-size: 12px;
          color: #999;
        }
      }
    }
  }

  .header-right {
    .datetime {
      text-align: right;

      .time {
        font-size: 20px;
        font-weight: 600;
        color: #00d4ff;
        font-family: 'Courier New', monospace;
      }

      .date {
        font-size: 12px;
        color: #999;
        margin-top: 2px;
      }
    }
  }
}

.dashboard-main {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

.metrics-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;

  .metric-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 12px;
    padding: 20px;
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 16px;
    align-items: center;
    transition: all 0.3s ease;
    animation: slideInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(20px);

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(0, 212, 255, 0.5);
      transform: translateY(-2px);
    }

    .metric-icon {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: #fff;
    }

    .metric-content {
      .metric-value {
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 4px;

        .unit {
          font-size: 14px;
          color: #999;
          margin-left: 4px;
        }
      }

      .metric-label {
        font-size: 14px;
        color: #999;
        margin-bottom: 8px;
      }

      .metric-change {
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 4px;

        &.positive {
          color: #00ff88;
        }

        &.negative {
          color: #ff4757;
        }
      }
    }

    .metric-chart {
      width: 60px;
      height: 40px;

      .mini-chart {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 20px;
  height: 400px;
}

.details-section {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  height: 300px;
}

.chart-panel,
.detail-panel {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .panel-header {
    padding: 16px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: #00d4ff;
      }
    }
  }

  .panel-content {
    flex: 1;
    padding: 20px;
    overflow: hidden;

    .chart-container {
      width: 100%;
      height: 100%;
    }
  }
}

.dashboard-footer {
  height: 50px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 212, 255, 0.3);
  display: flex;
  align-items: center;
  padding: 0 30px;
  font-size: 12px;

  .footer-left {
    .system-status {
      display: flex;
      align-items: center;
      gap: 8px;

      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;

        &.online {
          background: #00ff88;
          box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }

        &.offline {
          background: #ff4757;
          box-shadow: 0 0 10px rgba(255, 71, 87, 0.5);
        }
      }
    }
  }

  .footer-center {
    flex: 1;
    text-align: center;

    .data-stats {
      color: #999;

      .separator {
        margin: 0 12px;
        color: #666;
      }
    }
  }

  .footer-right {
    .version-info {
      color: #666;
    }
  }
}

// 动画
@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 1600px) {
  .metrics-section {
    grid-template-columns: repeat(2, 1fr);
  }

  .charts-section {
    grid-template-columns: 1fr;
    height: auto;

    .chart-panel {
      height: 300px;
    }
  }

  .details-section {
    grid-template-columns: repeat(2, 1fr);
    height: auto;

    .detail-panel {
      height: 250px;
    }
  }
}
</style>
