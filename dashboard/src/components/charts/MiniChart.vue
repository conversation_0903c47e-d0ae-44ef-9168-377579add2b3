<template>
  <div ref="chartRef" class="mini-chart" :style="{ height: height + 'px' }"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  type: {
    type: String,
    default: 'line',
    validator: (value) => ['line', 'bar', 'area'].includes(value)
  },
  color: {
    type: String,
    default: '#3b82f6'
  },
  height: {
    type: Number,
    default: 60
  },
  smooth: {
    type: Boolean,
    default: true
  },
  showTooltip: {
    type: Boolean,
    default: true
  }
})

const chartRef = ref(null)
let chartInstance = null

// 基础配置
const getBaseOptions = () => ({
  grid: {
    left: 0,
    right: 0,
    top: 0,
    bottom: 0
  },
  xAxis: {
    type: 'category',
    show: false,
    data: props.data.map((_, index) => index)
  },
  yAxis: {
    type: 'value',
    show: false
  },
  tooltip: props.showTooltip ? {
    trigger: 'axis',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderColor: props.color,
    borderWidth: 1,
    textStyle: {
      color: '#fff',
      fontSize: 12
    },
    formatter: function(params) {
      const point = params[0]
      return `值: ${point.value}`
    }
  } : false,
  series: []
})

// 获取系列配置
const getSeriesConfig = () => {
  const baseConfig = {
    data: props.data,
    symbol: 'none',
    lineStyle: {
      width: 2,
      color: props.color
    },
    itemStyle: {
      color: props.color
    }
  }

  switch (props.type) {
    case 'line':
      return {
        ...baseConfig,
        type: 'line',
        smooth: props.smooth
      }
    
    case 'area':
      return {
        ...baseConfig,
        type: 'line',
        smooth: props.smooth,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: props.color + '80' },
              { offset: 1, color: props.color + '20' }
            ]
          }
        }
      }
    
    case 'bar':
      return {
        ...baseConfig,
        type: 'bar',
        barWidth: '60%'
      }
    
    default:
      return baseConfig
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value || !props.data.length) return
  
  chartInstance = echarts.init(chartRef.value)
  updateChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 更新图表
const updateChart = () => {
  if (!chartInstance || !props.data.length) return
  
  const options = getBaseOptions()
  options.series = [getSeriesConfig()]
  
  chartInstance.setOption(options, true)
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 销毁图表
const destroyChart = () => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
}

// 监听数据变化
watch(() => props.data, () => {
  nextTick(() => {
    if (chartInstance) {
      updateChart()
    } else {
      initChart()
    }
  })
}, { deep: true })

// 监听其他属性变化
watch([() => props.type, () => props.color, () => props.smooth], () => {
  nextTick(() => {
    updateChart()
  })
})

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

onUnmounted(() => {
  destroyChart()
})

// 暴露方法
defineExpose({
  chartInstance,
  updateChart
})
</script>

<style scoped>
.mini-chart {
  width: 100%;
}
</style>
