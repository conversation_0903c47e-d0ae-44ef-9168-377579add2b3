<template>
  <div class="real-time-chart">
    <div class="chart-header">
      <h3>{{ title }}</h3>
      <div class="chart-controls">
        <el-select v-model="timeRange" @change="handleTimeRangeChange" size="small">
          <el-option label="最近1小时" value="1h" />
          <el-option label="最近6小时" value="6h" />
          <el-option label="最近24小时" value="24h" />
        </el-select>
        <el-button 
          :type="isAutoRefresh ? 'primary' : 'default'" 
          size="small" 
          @click="toggleAutoRefresh"
        >
          <el-icon><Refresh /></el-icon>
          {{ isAutoRefresh ? '停止' : '自动' }}
        </el-button>
      </div>
    </div>
    
    <div ref="chartRef" class="chart-container" :style="{ height: height }"></div>
    
    <div class="chart-footer">
      <div class="data-points">
        <span v-for="(point, index) in latestData" :key="index" class="data-point">
          <span class="point-label">{{ point.label }}:</span>
          <span class="point-value" :style="{ color: point.color }">
            {{ formatValue(point.value, point.unit) }}
          </span>
        </span>
      </div>
      <div class="last-update">
        最后更新: {{ lastUpdateTime }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { formatDateTime } from '@/utils'

const props = defineProps({
  title: {
    type: String,
    default: '实时数据'
  },
  height: {
    type: String,
    default: '300px'
  },
  dataSource: {
    type: Function,
    required: true
  },
  series: {
    type: Array,
    default: () => []
  },
  refreshInterval: {
    type: Number,
    default: 30000 // 30秒
  }
})

const chartRef = ref(null)
const timeRange = ref('1h')
const isAutoRefresh = ref(true)
const lastUpdateTime = ref('')
const latestData = ref([])

let chartInstance = null
let refreshTimer = null
let chartData = []

// 图表配置
const chartOptions = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985'
      }
    },
    formatter: function(params) {
      let result = `${formatDateTime(new Date(params[0].axisValue))}<br/>`
      params.forEach(param => {
        result += `${param.marker}${param.seriesName}: ${param.value[1]}${param.data.unit || ''}<br/>`
      })
      return result
    }
  },
  legend: {
    data: [],
    textStyle: {
      color: '#fff'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'time',
    boundaryGap: false,
    axisLine: {
      lineStyle: {
        color: '#4a5568'
      }
    },
    axisLabel: {
      color: '#a0aec0',
      formatter: function(value) {
        return echarts.format.formatTime('hh:mm', value)
      }
    }
  },
  yAxis: {
    type: 'value',
    axisLine: {
      lineStyle: {
        color: '#4a5568'
      }
    },
    axisLabel: {
      color: '#a0aec0'
    },
    splitLine: {
      lineStyle: {
        color: '#2d3748'
      }
    }
  },
  series: []
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value, 'dark')
  chartInstance.setOption(chartOptions)
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 更新图表数据
const updateChart = async () => {
  try {
    const data = await props.dataSource(timeRange.value)
    chartData = data
    
    // 更新系列数据
    const series = props.series.map(seriesConfig => ({
      name: seriesConfig.name,
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 4,
      lineStyle: {
        width: 2,
        color: seriesConfig.color
      },
      itemStyle: {
        color: seriesConfig.color
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: seriesConfig.color + '40' },
            { offset: 1, color: seriesConfig.color + '10' }
          ]
        }
      },
      data: data.map(item => [
        item.timestamp,
        item[seriesConfig.field],
        { unit: seriesConfig.unit }
      ])
    }))
    
    // 更新图例
    chartOptions.legend.data = props.series.map(s => s.name)
    chartOptions.series = series
    
    if (chartInstance) {
      chartInstance.setOption(chartOptions, true)
    }
    
    // 更新最新数据显示
    if (data.length > 0) {
      const latest = data[data.length - 1]
      latestData.value = props.series.map(seriesConfig => ({
        label: seriesConfig.name,
        value: latest[seriesConfig.field],
        unit: seriesConfig.unit,
        color: seriesConfig.color
      }))
    }
    
    lastUpdateTime.value = formatDateTime(new Date())
  } catch (error) {
    console.error('更新图表数据失败:', error)
  }
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 处理时间范围变化
const handleTimeRangeChange = () => {
  updateChart()
}

// 切换自动刷新
const toggleAutoRefresh = () => {
  isAutoRefresh.value = !isAutoRefresh.value
  
  if (isAutoRefresh.value) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

// 开始自动刷新
const startAutoRefresh = () => {
  stopAutoRefresh()
  refreshTimer = setInterval(() => {
    updateChart()
  }, props.refreshInterval)
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 格式化数值
const formatValue = (value, unit) => {
  if (typeof value !== 'number') return value
  
  let formattedValue = value.toFixed(1)
  
  // 处理大数值
  if (value >= 1000000) {
    formattedValue = (value / 1000000).toFixed(1) + 'M'
  } else if (value >= 1000) {
    formattedValue = (value / 1000).toFixed(1) + 'K'
  }
  
  return formattedValue + (unit || '')
}

// 销毁图表
const destroyChart = () => {
  stopAutoRefresh()
  
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  
  window.removeEventListener('resize', handleResize)
}

// 监听系列配置变化
watch(() => props.series, () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

onMounted(() => {
  nextTick(() => {
    initChart()
    updateChart()
    
    if (isAutoRefresh.value) {
      startAutoRefresh()
    }
  })
})

onUnmounted(() => {
  destroyChart()
})

// 暴露方法
defineExpose({
  updateChart,
  chartInstance
})
</script>

<style scoped>
.real-time-chart {
  background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  border-radius: 12px;
  padding: 20px;
  color: #fff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #f7fafc;
}

.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.chart-container {
  width: 100%;
  min-height: 200px;
}

.chart-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #4a5568;
}

.data-points {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.data-point {
  display: flex;
  align-items: center;
  gap: 4px;
}

.point-label {
  color: #a0aec0;
  font-size: 14px;
}

.point-value {
  font-weight: 600;
  font-size: 16px;
}

.last-update {
  color: #718096;
  font-size: 12px;
}

/* 深色主题下的Element Plus组件样式调整 */
:deep(.el-select) {
  --el-select-input-color: #f7fafc;
  --el-select-input-focus-border-color: #3182ce;
}

:deep(.el-button--default) {
  --el-button-text-color: #f7fafc;
  --el-button-bg-color: #4a5568;
  --el-button-border-color: #4a5568;
}

:deep(.el-button--primary) {
  --el-button-bg-color: #3182ce;
  --el-button-border-color: #3182ce;
}
</style>
