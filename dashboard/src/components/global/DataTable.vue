<template>
  <div class="data-table">
    <el-table
      ref="tableRef"
      :data="data"
      :loading="loading"
      :height="height"
      :max-height="maxHeight"
      :stripe="stripe"
      :border="border"
      :size="size"
      :empty-text="emptyText"
      v-bind="$attrs"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
    >
      <slot />
    </el-table>

    <!-- 分页 -->
    <div v-if="showPagination && pagination" class="table-pagination">
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="pageSizes"
        :layout="paginationLayout"
        :background="true"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  height: {
    type: [String, Number],
    default: undefined
  },
  maxHeight: {
    type: [String, Number],
    default: undefined
  },
  stripe: {
    type: Boolean,
    default: true
  },
  border: {
    type: Boolean,
    default: true
  },
  size: {
    type: String,
    default: 'default'
  },
  emptyText: {
    type: String,
    default: '暂无数据'
  },
  showPagination: {
    type: Boolean,
    default: true
  },
  pagination: {
    type: Object,
    default: null
  },
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100]
  },
  paginationLayout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper'
  }
})

const emit = defineEmits([
  'selection-change',
  'sort-change',
  'page-change',
  'size-change'
])

const tableRef = ref()

// 选择变化
const handleSelectionChange = (selection) => {
  emit('selection-change', selection)
}

// 排序变化
const handleSortChange = (sortInfo) => {
  emit('sort-change', sortInfo)
}

// 页码变化
const handleCurrentChange = (page) => {
  emit('page-change', page)
}

// 页大小变化
const handleSizeChange = (size) => {
  emit('size-change', size)
}

// 暴露表格方法
const clearSelection = () => {
  tableRef.value?.clearSelection()
}

const toggleRowSelection = (row, selected) => {
  tableRef.value?.toggleRowSelection(row, selected)
}

const toggleAllSelection = () => {
  tableRef.value?.toggleAllSelection()
}

const setCurrentRow = (row) => {
  tableRef.value?.setCurrentRow(row)
}

const clearSort = () => {
  tableRef.value?.clearSort()
}

const doLayout = () => {
  tableRef.value?.doLayout()
}

defineExpose({
  clearSelection,
  toggleRowSelection,
  toggleAllSelection,
  setCurrentRow,
  clearSort,
  doLayout
})
</script>

<style lang="scss" scoped>
.data-table {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  :deep(.el-table) {
    flex: 1;
    
    .el-table__header-wrapper {
      th {
        background-color: #fafafa;
        color: #606266;
        font-weight: 500;
      }
    }
    
    .el-table__body-wrapper {
      .el-table__row {
        &:hover {
          background-color: #f5f7fa;
        }
      }
    }
    
    .el-table__empty-block {
      min-height: 200px;
    }
  }
}

.table-pagination {
  display: flex;
  justify-content: flex-end;
  padding: 16px 0;
  border-top: 1px solid #ebeef5;
  
  :deep(.el-pagination) {
    .el-pagination__total {
      margin-right: auto;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .table-pagination {
    justify-content: center;
    
    :deep(.el-pagination) {
      .el-pagination__sizes,
      .el-pagination__jump {
        display: none;
      }
    }
  }
}
</style>
