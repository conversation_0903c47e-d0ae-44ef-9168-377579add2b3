<template>
  <div class="search-form">
    <el-form
      ref="formRef"
      :model="formData"
      :inline="inline"
      :label-width="labelWidth"
      :size="size"
      @submit.prevent="handleSearch"
    >
      <slot :form-data="formData" :handle-search="handleSearch" :handle-reset="handleReset" />
      
      <el-form-item v-if="showActions" class="search-actions">
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          {{ searchText }}
        </el-button>
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          {{ resetText }}
        </el-button>
        <el-button v-if="showExpand && hasExpandItems" type="text" @click="toggleExpand">
          {{ expanded ? '收起' : '展开' }}
          <el-icon>
            <ArrowUp v-if="expanded" />
            <ArrowDown v-else />
          </el-icon>
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  inline: {
    type: Boolean,
    default: true
  },
  labelWidth: {
    type: String,
    default: 'auto'
  },
  size: {
    type: String,
    default: 'default'
  },
  showActions: {
    type: Boolean,
    default: true
  },
  searchText: {
    type: String,
    default: '搜索'
  },
  resetText: {
    type: String,
    default: '重置'
  },
  showExpand: {
    type: Boolean,
    default: false
  },
  defaultExpanded: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'search', 'reset'])

const formRef = ref()
const expanded = ref(props.defaultExpanded)

// 表单数据
const formData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 是否有可展开项
const hasExpandItems = computed(() => {
  // 这里可以根据实际需求判断是否有可展开的表单项
  return props.showExpand
})

// 处理搜索
const handleSearch = () => {
  emit('search', formData.value)
}

// 处理重置
const handleReset = () => {
  // 重置表单数据
  const resetData = {}
  Object.keys(formData.value).forEach(key => {
    resetData[key] = ''
  })
  
  emit('update:modelValue', resetData)
  emit('reset', resetData)
  
  // 重置表单验证
  formRef.value?.resetFields()
}

// 切换展开状态
const toggleExpand = () => {
  expanded.value = !expanded.value
}

// 暴露方法
defineExpose({
  search: handleSearch,
  reset: handleReset,
  toggleExpand,
  validate: () => formRef.value?.validate(),
  resetFields: () => formRef.value?.resetFields(),
  clearValidate: () => formRef.value?.clearValidate()
})
</script>

<style lang="scss" scoped>
.search-form {
  .search-actions {
    margin-left: auto;
    
    .el-button {
      margin-left: 8px;
    }
  }
  
  // 展开/收起动画
  .expand-transition {
    transition: all 0.3s ease;
    overflow: hidden;
  }
  
  .expand-enter-active,
  .expand-leave-active {
    transition: all 0.3s ease;
  }
  
  .expand-enter-from,
  .expand-leave-to {
    opacity: 0;
    max-height: 0;
  }
  
  .expand-enter-to,
  .expand-leave-from {
    opacity: 1;
    max-height: 200px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .search-form {
    :deep(.el-form) {
      .el-form-item {
        width: 100%;
        margin-bottom: 12px;
        
        .el-form-item__content {
          width: 100%;
          
          .el-input,
          .el-select,
          .el-date-picker {
            width: 100% !important;
          }
        }
      }
      
      .search-actions {
        .el-button {
          width: 100%;
          margin: 4px 0;
        }
      }
    }
  }
}
</style>
