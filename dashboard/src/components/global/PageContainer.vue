<template>
  <div class="page-container">
    <!-- 页面头部 -->
    <div v-if="showHeader" class="page-header">
      <div class="page-title">
        <el-icon v-if="icon" class="title-icon">
          <component :is="icon" />
        </el-icon>
        <h1>{{ title }}</h1>
        <el-tag v-if="subtitle" type="info" size="small">{{ subtitle }}</el-tag>
      </div>
      
      <div v-if="$slots.actions" class="page-actions">
        <slot name="actions" />
      </div>
    </div>

    <!-- 搜索区域 -->
    <div v-if="$slots.search" class="search-section">
      <el-card shadow="never">
        <slot name="search" />
      </el-card>
    </div>

    <!-- 主要内容 -->
    <div class="page-content">
      <el-card v-if="showCard" shadow="never" :body-style="cardBodyStyle">
        <slot />
      </el-card>
      <div v-else>
        <slot />
      </div>
    </div>

    <!-- 底部区域 -->
    <div v-if="$slots.footer" class="page-footer">
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default: ''
  },
  subtitle: {
    type: String,
    default: ''
  },
  icon: {
    type: String,
    default: ''
  },
  showHeader: {
    type: Boolean,
    default: true
  },
  showCard: {
    type: Boolean,
    default: true
  },
  cardBodyStyle: {
    type: Object,
    default: () => ({ padding: '20px' })
  }
})
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .page-title {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .title-icon {
      font-size: 24px;
      color: #409eff;
    }
    
    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .page-actions {
    display: flex;
    gap: 8px;
  }
}

.search-section {
  :deep(.el-card__body) {
    padding: 16px;
  }
}

.page-content {
  flex: 1;
  min-height: 0;
  
  :deep(.el-card) {
    height: 100%;
    
    .el-card__body {
      height: calc(100% - 40px);
      overflow: auto;
    }
  }
}

.page-footer {
  padding: 16px 0;
  border-top: 1px solid #ebeef5;
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    
    .page-title h1 {
      font-size: 20px;
    }
    
    .page-actions {
      width: 100%;
      justify-content: flex-end;
    }
  }
}
</style>
