<template>
  <span class="count-up" :class="{ 'count-up--animated': isAnimating }">
    {{ displayValue }}
  </span>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue'

const props = defineProps({
  // 结束值
  endVal: {
    type: Number,
    required: true
  },
  // 开始值
  startVal: {
    type: Number,
    default: 0
  },
  // 动画持续时间（秒）
  duration: {
    type: Number,
    default: 2
  },
  // 小数位数
  decimalPlaces: {
    type: Number,
    default: 0
  },
  // 前缀
  prefix: {
    type: String,
    default: ''
  },
  // 后缀
  suffix: {
    type: String,
    default: ''
  },
  // 千分位分隔符
  separator: {
    type: String,
    default: ','
  },
  // 是否自动开始
  autoStart: {
    type: Boolean,
    default: true
  },
  // 缓动函数
  easingFn: {
    type: Function,
    default: null
  },
  // 格式化函数
  formattingFn: {
    type: Function,
    default: null
  }
})

const emit = defineEmits(['started', 'finished'])

const currentValue = ref(props.startVal)
const isAnimating = ref(false)
let animationId = null
let startTime = null

// 默认缓动函数（easeOutExpo）
const defaultEasingFn = (t, b, c, d) => {
  return c * (-Math.pow(2, -10 * t / d) + 1) * 1024 / 1023 + b
}

// 格式化数字
const formatNumber = (num) => {
  if (props.formattingFn) {
    return props.formattingFn(num)
  }
  
  // 保留小数位
  const fixed = num.toFixed(props.decimalPlaces)
  
  // 添加千分位分隔符
  if (props.separator) {
    const parts = fixed.split('.')
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, props.separator)
    return parts.join('.')
  }
  
  return fixed
}

// 显示值
const displayValue = computed(() => {
  const formatted = formatNumber(currentValue.value)
  return props.prefix + formatted + props.suffix
})

// 动画函数
const animate = (timestamp) => {
  if (!startTime) startTime = timestamp
  
  const progress = timestamp - startTime
  const duration = props.duration * 1000
  
  if (progress < duration) {
    const easingFn = props.easingFn || defaultEasingFn
    const value = easingFn(
      progress,
      props.startVal,
      props.endVal - props.startVal,
      duration
    )
    
    currentValue.value = value
    animationId = requestAnimationFrame(animate)
  } else {
    // 动画结束
    currentValue.value = props.endVal
    isAnimating.value = false
    emit('finished')
  }
}

// 开始动画
const start = () => {
  if (isAnimating.value) return
  
  isAnimating.value = true
  startTime = null
  currentValue.value = props.startVal
  
  emit('started')
  animationId = requestAnimationFrame(animate)
}

// 停止动画
const stop = () => {
  if (animationId) {
    cancelAnimationFrame(animationId)
    animationId = null
  }
  isAnimating.value = false
  startTime = null
}

// 重置
const reset = () => {
  stop()
  currentValue.value = props.startVal
}

// 立即完成
const finish = () => {
  stop()
  currentValue.value = props.endVal
  emit('finished')
}

// 监听结束值变化
watch(() => props.endVal, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    if (props.autoStart) {
      start()
    }
  }
})

// 监听开始值变化
watch(() => props.startVal, (newVal) => {
  if (!isAnimating.value) {
    currentValue.value = newVal
  }
})

onMounted(() => {
  if (props.autoStart) {
    start()
  } else {
    currentValue.value = props.startVal
  }
})

// 暴露方法
defineExpose({
  start,
  stop,
  reset,
  finish,
  isAnimating: () => isAnimating.value
})
</script>

<style scoped>
.count-up {
  display: inline-block;
  transition: all 0.3s ease;
}

.count-up--animated {
  animation: countUpPulse 0.3s ease-out;
}

@keyframes countUpPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
</style>
