<template>
  <div class="stat-card" :class="[`stat-card--${type}`, { 'stat-card--animated': animated }]">
    <div class="stat-card__background">
      <div class="stat-card__pattern"></div>
    </div>
    
    <div class="stat-card__content">
      <div class="stat-card__header">
        <div class="stat-card__icon" :style="{ backgroundColor: iconBgColor }">
          <el-icon :size="iconSize" :color="iconColor">
            <component :is="icon" />
          </el-icon>
        </div>
        <div class="stat-card__trend" v-if="trend">
          <el-icon :color="trendColor" size="16">
            <ArrowUp v-if="trend > 0" />
            <ArrowDown v-if="trend < 0" />
            <Minus v-if="trend === 0" />
          </el-icon>
          <span :style="{ color: trendColor }">{{ Math.abs(trend) }}%</span>
        </div>
      </div>
      
      <div class="stat-card__body">
        <div class="stat-card__value">
          <CountUp
            :end-val="value"
            :duration="duration"
            :decimal-places="decimalPlaces"
            :prefix="prefix"
            :suffix="suffix"
            :separator="separator"
          />
        </div>
        <div class="stat-card__title">{{ title }}</div>
        <div class="stat-card__subtitle" v-if="subtitle">{{ subtitle }}</div>
      </div>
      
      <div class="stat-card__footer" v-if="$slots.footer">
        <slot name="footer"></slot>
      </div>
    </div>
    
    <!-- 进度条 -->
    <div class="stat-card__progress" v-if="showProgress">
      <div 
        class="stat-card__progress-bar" 
        :style="{ 
          width: `${progressPercent}%`, 
          backgroundColor: progressColor 
        }"
      ></div>
    </div>
    
    <!-- 迷你图表 -->
    <div class="stat-card__chart" v-if="chartData && chartData.length > 0">
      <MiniChart 
        :data="chartData" 
        :type="chartType" 
        :color="chartColor"
        :height="40"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'
import CountUp from '@/components/common/CountUp.vue'
import MiniChart from '@/components/charts/MiniChart.vue'

const props = defineProps({
  // 基础属性
  title: {
    type: String,
    required: true
  },
  subtitle: {
    type: String,
    default: ''
  },
  value: {
    type: Number,
    required: true
  },
  
  // 图标
  icon: {
    type: [String, Object],
    required: true
  },
  iconSize: {
    type: Number,
    default: 24
  },
  iconColor: {
    type: String,
    default: '#fff'
  },
  iconBgColor: {
    type: String,
    default: ''
  },
  
  // 样式类型
  type: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'success', 'warning', 'danger', 'info'].includes(value)
  },
  
  // 数值格式化
  prefix: {
    type: String,
    default: ''
  },
  suffix: {
    type: String,
    default: ''
  },
  separator: {
    type: String,
    default: ','
  },
  decimalPlaces: {
    type: Number,
    default: 0
  },
  duration: {
    type: Number,
    default: 2
  },
  
  // 趋势
  trend: {
    type: Number,
    default: null
  },
  
  // 进度条
  showProgress: {
    type: Boolean,
    default: false
  },
  progressPercent: {
    type: Number,
    default: 0
  },
  progressColor: {
    type: String,
    default: ''
  },
  
  // 迷你图表
  chartData: {
    type: Array,
    default: () => []
  },
  chartType: {
    type: String,
    default: 'line',
    validator: (value) => ['line', 'bar', 'area'].includes(value)
  },
  chartColor: {
    type: String,
    default: ''
  },
  
  // 动画
  animated: {
    type: Boolean,
    default: true
  }
})

// 计算属性
const trendColor = computed(() => {
  if (props.trend > 0) return '#10b981'
  if (props.trend < 0) return '#ef4444'
  return '#6b7280'
})

const iconBgColor = computed(() => {
  if (props.iconBgColor) return props.iconBgColor
  
  const colorMap = {
    primary: '#3b82f6',
    success: '#10b981',
    warning: '#f59e0b',
    danger: '#ef4444',
    info: '#6b7280'
  }
  
  return colorMap[props.type] || colorMap.primary
})

const progressColor = computed(() => {
  if (props.progressColor) return props.progressColor
  return iconBgColor.value
})

const chartColor = computed(() => {
  if (props.chartColor) return props.chartColor
  return iconBgColor.value
})
</script>

<style scoped>
.stat-card {
  position: relative;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  border-radius: 16px;
  padding: 24px;
  color: #fff;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
}

.stat-card--animated {
  animation: slideInUp 0.6s ease-out;
}

.stat-card__background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
}

.stat-card__pattern {
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  animation: patternMove 20s linear infinite;
}

.stat-card__content {
  position: relative;
  z-index: 1;
}

.stat-card__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-card__icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.stat-card__trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 600;
}

.stat-card__body {
  margin-bottom: 16px;
}

.stat-card__value {
  font-size: 32px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #fff 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-card__title {
  font-size: 16px;
  font-weight: 500;
  color: #e2e8f0;
  margin-bottom: 4px;
}

.stat-card__subtitle {
  font-size: 14px;
  color: #94a3b8;
}

.stat-card__footer {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 16px;
}

.stat-card__progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.stat-card__progress-bar {
  height: 100%;
  transition: width 0.6s ease;
  background: linear-gradient(90deg, transparent 0%, currentColor 50%, transparent 100%);
  animation: progressShine 2s ease-in-out infinite;
}

.stat-card__chart {
  margin-top: 16px;
  height: 40px;
}

/* 类型样式 */
.stat-card--primary {
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
}

.stat-card--success {
  background: linear-gradient(135deg, #047857 0%, #10b981 100%);
}

.stat-card--warning {
  background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
}

.stat-card--danger {
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
}

.stat-card--info {
  background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%);
}

/* 动画 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes patternMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(20px, 20px);
  }
}

@keyframes progressShine {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 响应式 */
@media (max-width: 768px) {
  .stat-card {
    padding: 16px;
  }
  
  .stat-card__value {
    font-size: 24px;
  }
  
  .stat-card__icon {
    width: 40px;
    height: 40px;
  }
}
</style>
