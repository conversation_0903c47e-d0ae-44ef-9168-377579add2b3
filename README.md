# 农场智慧管理系统

## 项目简介
农场智慧管理系统是一个集成了GIS地块管理、农事操作记录、物资管理、天气监控等功能的现代化农业管理平台。系统采用前后端分离架构，提供Web管理界面和数据可视化大屏，帮助农场实现数字化、智能化管理。

## 系统特色
- 🗺️ **GIS地块管理**: 基于Leaflet的地块可视化管理，支持地块绘制、编辑和查询
- 🌱 **智能农事记录**: 全程记录种植、浇灌、施肥、打药、收获等农事操作
- 📊 **数据分析统计**: 多维度数据分析，为农业生产提供科学决策支持
- 🌤️ **天气监控预警**: 实时天气信息和农业气象指导
- 📦 **物资库存管理**: 农资采购、库存、使用全流程管理
- 📱 **响应式设计**: 支持PC、平板、手机等多终端访问
- 🖥️ **数据大屏**: 农场运营数据实时可视化展示

## 系统架构
```
farm-management-system/
├── backend/                 # 后端服务 (Node.js + Express + MongoDB)
│   ├── src/
│   │   ├── controllers/     # 控制器层
│   │   ├── models/         # 数据模型层
│   │   ├── routes/         # 路由层
│   │   ├── middleware/     # 中间件
│   │   ├── services/       # 业务服务层
│   │   └── utils/          # 工具函数
│   ├── config/             # 配置文件
│   └── package.json
├── frontend/               # 前端管理界面 (Vue.js 3 + Element Plus)
│   ├── src/
│   │   ├── components/     # 公共组件
│   │   ├── views/          # 页面组件
│   │   ├── stores/         # 状态管理 (Pinia)
│   │   ├── router/         # 路由配置
│   │   ├── api/            # API接口
│   │   ├── utils/          # 工具函数
│   │   └── styles/         # 样式文件
│   └── package.json
├── dashboard/              # 数据大屏 (Vue.js + ECharts + DataV)
│   ├── src/
│   │   ├── components/     # 大屏组件
│   │   ├── views/          # 大屏页面
│   │   └── utils/          # 工具函数
│   └── package.json
├── docs/                   # 项目文档
├── start.bat              # Windows启动脚本
├── start.sh               # Linux/Mac启动脚本
└── README.md              # 项目说明
```

## 主要功能模块

### 1. 用户权限管理
- 多角色用户管理 (管理员、管理者、工作人员、查看者)
- 细粒度权限控制
- 用户登录认证和会话管理

### 2. 地块管理系统
- 🗺️ GIS地图可视化展示
- 📍 地块信息管理 (位置、面积、土壤类型等)
- ✏️ 地块边界绘制和编辑
- 🧪 土壤检测记录管理
- 📈 地块统计分析

### 3. 作物种植管理
- 🌱 作物种植计划制定
- 📝 种植记录管理
- 📊 生长周期跟踪
- 🔍 病虫害记录
- 📈 产量预测分析

### 4. 农事操作管理
- 💧 浇灌作业记录 (用水量、方式、时间等)
- 🌿 施肥作业记录 (肥料类型、用量、成本等)
- 💊 打药作业记录 (农药类型、浓度、安全间隔等)
- 🌾 除草作业记录
- 💰 成本核算统计

### 5. 收获管理
- 📦 收获记录管理
- ⚖️ 产量统计分析
- 🏆 质量等级评估
- 💵 收益分析计算
- 📊 收获数据报表

### 6. 物资管理
- 📦 农资库存管理 (种子、肥料、农药、工具等)
- 🛒 采购计划制定
- 💰 成本核算分析
- 🏪 供应商管理
- ⚠️ 库存预警提醒

### 7. 天气监控
- 🌤️ 实时天气信息
- 📅 天气预报查询
- 🌾 农业气象指导
- ⚠️ 灾害预警提醒
- 📊 气象数据统计

### 8. 数据大屏
- 📊 农场运营概况
- 📈 实时数据监控
- 📉 趋势分析图表
- 🗺️ 地块分布展示
- 💹 收益统计分析

## 技术栈

### 后端技术
- **运行环境**: Node.js 16+
- **Web框架**: Express.js
- **数据库**: MongoDB
- **ODM**: Mongoose
- **认证**: JWT
- **文件上传**: Multer
- **数据验证**: Express-validator
- **日志**: Morgan
- **安全**: Helmet, CORS

### 前端技术
- **框架**: Vue.js 3
- **构建工具**: Vite
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **地图**: Leaflet + Vue-Leaflet
- **图表**: ECharts + Vue-ECharts
- **样式**: SCSS
- **工具库**: Lodash, Day.js

### 大屏技术
- **框架**: Vue.js 3
- **可视化**: ECharts, DataV
- **动画**: Animate.css
- **数字动画**: CountUp.js

## 快速开始

### 环境要求
- Node.js >= 16.0.0
- MongoDB >= 4.4
- npm >= 8.0.0

### 一键启动 (推荐)

**Windows用户:**
```bash
# 双击运行或在命令行执行
start.bat
```

**Linux/Mac用户:**
```bash
# 添加执行权限
chmod +x start.sh
# 运行启动脚本
./start.sh
```

### 手动安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd farm-management-system
```

2. **安装后端依赖**
```bash
cd backend
npm install
```

3. **配置后端环境**
```bash
# 复制环境配置文件
cp .env.example .env
# 编辑配置文件，设置数据库连接等
```

4. **安装前端依赖**
```bash
cd ../frontend
npm install
```

5. **安装大屏依赖**
```bash
cd ../dashboard
npm install
```

6. **启动服务**
```bash
# 启动后端服务 (端口: 5000)
cd backend
npm run dev

# 启动前端服务 (端口: 3000)
cd frontend
npm run dev

# 启动大屏服务 (端口: 3001)
cd dashboard
npm run dev
```

### 访问地址
- 📱 **前端管理界面**: http://localhost:3000
- 🖥️ **数据大屏**: http://localhost:3001
- 🔧 **后端API**: http://localhost:5000

### 默认账号
- **用户名**: admin
- **密码**: 123456

## 项目结构详解

### 后端目录结构
```
backend/
├── src/
│   ├── app.js              # 应用入口文件
│   ├── controllers/        # 控制器
│   │   ├── authController.js
│   │   ├── fieldController.js
│   │   └── ...
│   ├── models/            # 数据模型
│   │   ├── User.js
│   │   ├── Field.js
│   │   └── ...
│   ├── routes/            # 路由定义
│   │   ├── auth.js
│   │   ├── fields.js
│   │   └── ...
│   ├── middleware/        # 中间件
│   │   ├── auth.js
│   │   └── ...
│   ├── services/          # 业务服务
│   └── utils/             # 工具函数
├── config/                # 配置文件
├── uploads/               # 文件上传目录
├── .env.example          # 环境配置示例
└── package.json
```

### 前端目录结构
```
frontend/
├── src/
│   ├── main.js            # 应用入口
│   ├── App.vue            # 根组件
│   ├── components/        # 公共组件
│   ├── views/             # 页面组件
│   │   ├── auth/          # 认证相关页面
│   │   ├── dashboard/     # 仪表盘
│   │   ├── fields/        # 地块管理
│   │   ├── crops/         # 作物管理
│   │   └── ...
│   ├── layout/            # 布局组件
│   ├── stores/            # 状态管理
│   ├── router/            # 路由配置
│   ├── api/               # API接口
│   ├── utils/             # 工具函数
│   └── styles/            # 样式文件
├── public/                # 静态资源
└── package.json
```

## 开发指南

### 后端开发
1. 在 `src/models/` 中定义数据模型
2. 在 `src/routes/` 中定义路由
3. 在 `src/controllers/` 中实现业务逻辑
4. 在 `src/middleware/` 中添加中间件

### 前端开发
1. 在 `src/api/` 中定义API接口
2. 在 `src/views/` 中创建页面组件
3. 在 `src/components/` 中创建公共组件
4. 在 `src/stores/` 中管理应用状态

### 代码规范
- 使用ESLint进行代码检查
- 遵循Vue.js官方风格指南
- 使用语义化的Git提交信息

## 部署指南

### 生产环境部署
1. **构建前端项目**
```bash
cd frontend
npm run build

cd ../dashboard
npm run build
```

2. **配置生产环境变量**
```bash
# 设置NODE_ENV为production
export NODE_ENV=production
```

3. **启动后端服务**
```bash
cd backend
npm start
```

4. **配置Nginx反向代理** (可选)

### Docker部署 (开发中)
```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

## API文档

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `GET /api/auth/me` - 获取当前用户信息
- `PUT /api/auth/profile` - 更新用户信息

### 地块管理接口
- `GET /api/fields` - 获取地块列表
- `POST /api/fields` - 创建地块
- `GET /api/fields/:id` - 获取地块详情
- `PUT /api/fields/:id` - 更新地块
- `DELETE /api/fields/:id` - 删除地块

更多API文档请参考 `docs/api.md`

## 常见问题

### Q: 如何修改数据库连接？
A: 编辑 `backend/.env` 文件中的 `MONGODB_URI` 配置项。

### Q: 如何添加新的功能模块？
A: 参考现有模块的结构，在后端添加模型、路由、控制器，在前端添加页面、API接口。

### Q: 如何自定义地图服务？
A: 修改前端地图组件中的瓦片服务地址，支持OpenStreetMap、高德地图等。

### Q: 如何配置天气API？
A: 在 `backend/.env` 文件中配置 `WEATHER_API_KEY` 和 `WEATHER_API_URL`。

## 贡献指南
1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证
本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系我们
- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]
- 邮箱: [<EMAIL>]

## 更新日志
### v1.0.0 (2024-01-01)
- 🎉 初始版本发布
- ✨ 完成基础功能模块
- 🗺️ 集成GIS地块管理
- 📊 添加数据可视化大屏
- 📱 实现响应式设计

---

**感谢使用农场智慧管理系统！** 🌾
