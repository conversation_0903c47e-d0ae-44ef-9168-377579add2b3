#!/bin/bash

echo "========================================"
echo "安装扩展物联网协议和模拟器依赖包"
echo "========================================"
echo

echo "正在安装后端扩展IoT依赖..."
cd backend

echo "安装基础IoT协议依赖..."
npm install modbus-serial mqtt axios

echo "安装OPC UA协议依赖..."
npm install node-opcua

echo "安装BACnet协议依赖..."
npm install bacstack

echo "安装串口通信依赖..."
npm install serialport @serialport/parser-readline

echo "安装LoRaWAN协议依赖..."
npm install lora-packet

echo "安装模拟器相关依赖..."
npm install express

echo "后端扩展IoT依赖安装完成"
cd ..

echo
echo "========================================"
echo "所有扩展IoT依赖安装完成！"
echo "========================================"
echo
echo "支持的协议:"
echo "- Modbus TCP/RTU ✓"
echo "- MQTT ✓"
echo "- HTTP API ✓"
echo "- TCP Socket ✓"
echo "- PLC (西门子/三菱) ✓"
echo "- OPC UA ✓"
echo "- BACnet ✓"
echo "- LoRaWAN ✓"
echo "- 串口通信 ✓"
echo
echo "模拟器功能:"
echo "- Modbus TCP模拟器 ✓"
echo "- HTTP API模拟器 ✓"
echo "- MQTT设备模拟器 ✓"
echo "- 实时数据生成 ✓"
echo "- 多设备类型支持 ✓"
echo "========================================"
echo
echo "模拟器端口分配:"
echo "- Modbus TCP 温湿度传感器: 127.0.0.1:5020"
echo "- Modbus TCP 土壤传感器: 127.0.0.1:5021"
echo "- Modbus TCP 电能表: 127.0.0.1:5022"
echo "- HTTP API 气象站: http://localhost:3080"
echo "- MQTT 设备: mqtt://127.0.0.1:1883"
echo "========================================"
echo
