# 🎭 设备模拟器使用指南

## 📖 概述

设备模拟器是农场智慧管理系统的重要组成部分，它允许您在没有真实物理设备的情况下测试和演示完整的物联网功能。模拟器提供了多种协议支持和真实的数据生成，让您能够完整体验系统的所有功能。

## 🚀 快速开始

### 1. 安装依赖
```bash
# Windows
install-extended-iot-dependencies.bat

# Linux/Mac
./install-extended-iot-dependencies.sh
```

### 2. 启动系统
```bash
# 启动后端和前端
start-all.bat  # Windows
./start-all.sh # Linux/Mac
```

### 3. 访问模拟器管理
- 登录系统：http://localhost:3000
- 进入：物联网管理 → 设备模拟器

## 🎯 模拟器功能

### 📡 **自动启动的模拟器**

系统启动时会自动创建以下模拟器：

#### 🌡️ **Modbus TCP 温湿度传感器**
- **地址**: 127.0.0.1:5020
- **从站ID**: 1
- **寄存器**:
  - 地址0: 温度 (×10)
  - 地址1: 湿度 (×10)

#### 🌱 **Modbus TCP 土壤传感器**
- **地址**: 127.0.0.1:5021
- **从站ID**: 1
- **寄存器**:
  - 地址0: 土壤湿度 (×10)
  - 地址1: 土壤温度 (×10)
  - 地址2: 土壤pH (×100)
  - 地址3: 土壤EC (×10)

#### ⚡ **Modbus TCP 电能表**
- **地址**: 127.0.0.1:5022
- **从站ID**: 1
- **寄存器**:
  - 地址0: A相电压 (×10)
  - 地址1: A相电流 (×100)
  - 地址2-3: 有功功率 (×1000, 32位)
  - 地址4-5: 总电能 (×100, 32位)
  - 地址6: 频率 (×100)

#### 🌤️ **HTTP API 气象站**
- **地址**: http://localhost:3080
- **端点**:
  - `/api/weather/current` - 当前天气数据
  - `/api/sensors/soil` - 土壤传感器数据

#### 📡 **MQTT 设备模拟器**
- **Broker**: mqtt://127.0.0.1:1883
- **主题**:
  - `farm/sensors/temperature` - 温度数据
  - `farm/sensors/humidity` - 湿度数据
  - `farm/meters/water` - 水表数据

## 🔧 使用模拟器

### 1. 创建模拟设备

#### 方法一：快速创建
1. 进入"设备模拟器"页面
2. 在"快速创建模拟设备"区域选择设备类型
3. 点击对应的设备卡片
4. 系统自动创建模拟设备

#### 方法二：手动创建
1. 进入"物联网管理" → "设备管理"
2. 点击"添加设备"
3. 选择协议类型为"Device Simulator"
4. 配置设备参数
5. 保存并启动设备

### 2. 连接模拟设备

#### Modbus TCP设备配置示例：
```javascript
{
  name: "温湿度传感器001",
  code: "TEMP_001",
  type: "temperature_humidity",
  protocol: "modbus_tcp",
  connection: {
    host: "127.0.0.1",
    port: 5020,
    slaveId: 1,
    timeout: 5000
  },
  registers: [
    {
      name: "temperature",
      type: "input_register",
      address: 0,
      dataType: "int16",
      scale: 0.1,
      unit: "°C"
    },
    {
      name: "humidity", 
      type: "input_register",
      address: 1,
      dataType: "int16",
      scale: 0.1,
      unit: "%RH"
    }
  ]
}
```

#### HTTP API设备配置示例：
```javascript
{
  name: "气象站001",
  code: "WEATHER_001", 
  type: "weather_station",
  protocol: "http_api",
  connection: {
    host: "127.0.0.1",
    port: 3080
  },
  endpoints: [
    {
      name: "temperature",
      path: "/api/weather/current",
      dataPath: "temperature",
      dataType: "number"
    }
  ]
}
```

### 3. 查看模拟数据

#### 实时数据监控：
1. 进入"物联网管理" → "设备管理"
2. 找到模拟设备，点击"查看"
3. 查看实时数据和历史趋势

#### 数据预览：
1. 进入"设备模拟器"页面
2. 在"实时数据预览"区域查看各类数据
3. 点击"刷新数据"获取最新值

## 📊 数据生成特性

### 🔄 **真实数据模拟**

模拟器生成的数据具有以下特性：

#### 🌡️ **温度数据**
- 基础温度：20°C
- 日周期变化：±10°C (12小时周期)
- 随机噪声：±2°C
- 数据范围：-10°C ~ 50°C

#### 💧 **湿度数据**
- 基础湿度：60%RH
- 日周期变化：±20%RH (24小时周期)
- 随机噪声：±5%RH
- 数据范围：0% ~ 100%

#### 🌱 **土壤湿度**
- 基础湿度：45%
- 长周期变化：±15% (48小时周期)
- 随机噪声：±3%
- 数据范围：0% ~ 100%

#### ⚡ **电力数据**
- 电压：220V ± 10V (1分钟周期波动)
- 电流：5A ± 2A (30分钟负载周期)
- 功率：1.1kW ± 0.5kW
- 频率：50Hz ± 0.2Hz

#### 🌞 **光照数据**
- 白天光照：0 ~ 80000 lux (太阳角度计算)
- 夜间光照：0 lux
- 云层影响：随机30%衰减
- UV指数：0 ~ 10 (与光照同步)

### 📈 **数据周期性**

| 参数 | 周期 | 变化幅度 | 噪声 |
|------|------|----------|------|
| 温度 | 12小时 | ±10°C | ±2°C |
| 湿度 | 24小时 | ±20%RH | ±5%RH |
| 土壤湿度 | 48小时 | ±15% | ±3% |
| 土壤pH | 7天 | ±0.3 | ±0.1 |
| 光照强度 | 24小时 | 0~80000 lux | ±5000 lux |
| 风速 | 5分钟 | ±2 m/s | ±1 m/s |
| 电压 | 1分钟 | ±5V | ±2V |

## 🛠️ 高级功能

### 1. 自定义模拟器

您可以创建自定义的模拟器：

```javascript
// 添加自定义Modbus TCP模拟器
{
  name: "自定义传感器",
  type: "modbus_tcp",
  host: "127.0.0.1",
  port: 5023,
  slaveId: 1,
  registers: {
    0: () => Math.random() * 100,  // 自定义数据生成函数
    1: () => Math.sin(Date.now() / 10000) * 50 + 50
  }
}
```

### 2. 批量创建设备

使用快速创建功能批量生成模拟设备：

```javascript
// 快速创建API调用
{
  type: "temperature_humidity",
  count: 5,
  namePrefix: "温湿度传感器"
}
```

### 3. 数据导出

模拟器支持数据导出功能：
- CSV格式导出
- JSON格式导出
- 实时数据流导出
- 历史数据批量导出

## 🔍 故障排除

### 常见问题

#### 1. 模拟器无法启动
**解决方案**：
- 检查端口是否被占用
- 确认依赖包已正确安装
- 查看控制台错误信息

#### 2. 设备连接失败
**解决方案**：
- 确认模拟器已启动
- 检查IP地址和端口配置
- 验证协议类型匹配

#### 3. 数据不更新
**解决方案**：
- 检查设备连接状态
- 确认采集间隔设置
- 重启模拟器服务

### 调试技巧

#### 1. 查看模拟器日志
```bash
# 在后端控制台查看日志
npm run dev  # 开发模式启动
```

#### 2. 测试Modbus连接
```bash
# 使用Modbus测试工具
# 连接到 127.0.0.1:5020
# 读取寄存器地址0和1
```

#### 3. 测试HTTP API
```bash
# 使用curl测试
curl http://localhost:3080/api/weather/current
curl http://localhost:3080/api/sensors/soil
```

## 📚 扩展开发

### 添加新的设备类型

1. 在`deviceSimulator.js`中添加数据生成器
2. 在`simulatorHandler.js`中添加设备类型支持
3. 更新前端设备模板
4. 添加相应的图标和显示逻辑

### 自定义数据生成算法

```javascript
// 示例：添加新的数据生成器
createCustomSensorGenerator() {
  return () => {
    // 自定义算法
    const baseValue = 50;
    const timeVariation = Math.sin(Date.now() / 60000) * 20;
    const randomNoise = (Math.random() - 0.5) * 5;
    return baseValue + timeVariation + randomNoise;
  };
}
```

## 🎯 最佳实践

### 1. 设备命名规范
- 使用有意义的设备名称
- 包含设备类型和编号
- 例如：`温湿度传感器_001`、`土壤传感器_A区_001`

### 2. 数据采集间隔
- 传感器数据：30-60秒
- 表计数据：1-5分钟
- 控制器状态：10-30秒

### 3. 模拟器管理
- 定期检查模拟器状态
- 及时清理不需要的模拟器
- 监控系统资源使用情况

---

**设备模拟器让您无需真实硬件即可完整体验物联网功能！** 🎭✨

通过模拟器，您可以：
- 🔧 测试系统功能
- 📊 演示数据可视化
- 🎯 验证业务逻辑
- 🚀 快速原型开发
- 📚 学习物联网技术

享受您的智慧农场模拟之旅！ 🌱🎭🚀
