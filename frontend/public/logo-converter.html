<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logo转换器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .logo-preview {
            text-align: center;
            margin: 20px 0;
        }
        .logo-preview img {
            max-width: 200px;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 10px;
            background: white;
        }
        .download-section {
            text-align: center;
            margin: 30px 0;
        }
        .btn {
            background: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #45a049;
        }
        .instructions {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        canvas {
            border: 1px solid #ddd;
            margin: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌾 农场智慧管理系统 Logo</h1>

        <div class="logo-preview">
            <h3>SVG Logo预览</h3>
            <img src="logo.svg" alt="农场管理系统Logo" id="svgLogo">
        </div>

        <div class="instructions">
            <h3>📋 使用说明</h3>
            <p>1. 上面显示的是SVG格式的logo</p>
            <p>2. 点击下面的按钮可以生成PNG格式的logo</p>
            <p>3. 生成的PNG文件可以直接下载使用</p>
        </div>

        <div class="download-section">
            <button class="btn" onclick="convertToPNG(120, 120)">生成 120x120 PNG</button>
            <button class="btn" onclick="convertToPNG(64, 64)">生成 64x64 PNG</button>
            <button class="btn" onclick="convertToPNG(32, 32)">生成 32x32 PNG</button>
        </div>

        <div id="canvasContainer" style="text-align: center;"></div>
    </div>

    <script>
        function convertToPNG(width, height) {
            const svgElement = document.getElementById('svgLogo');
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            canvas.width = width;
            canvas.height = height;

            const img = new Image();
            img.onload = function() {
                // 清除画布
                ctx.clearRect(0, 0, width, height);

                // 绘制白色背景
                ctx.fillStyle = 'white';
                ctx.fillRect(0, 0, width, height);

                // 绘制SVG图像
                ctx.drawImage(img, 0, 0, width, height);

                // 创建下载链接
                canvas.toBlob(function(blob) {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `logo-${width}x${height}.png`;
                    a.textContent = `下载 ${width}x${height} PNG`;
                    a.className = 'btn';
                    a.style.display = 'block';
                    a.style.margin = '10px auto';
                    a.style.width = '200px';

                    // 添加到页面
                    const container = document.getElementById('canvasContainer');
                    container.appendChild(canvas);
                    container.appendChild(a);


                    console.log(123)
                    // 自动下载
                    a.click();
                });
            };

            // 将SVG转换为data URL
            const svgData = new XMLSerializer().serializeToString(svgElement);
            const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
            const svgUrl = URL.createObjectURL(svgBlob);

            img.src = svgUrl;
        }

        // 页面加载完成后自动生成主要尺寸的PNG
        window.addEventListener('load', function() {
            setTimeout(() => {
                convertToPNG(120, 120);
            }, 1000);
        });
    </script>
</body>
</html>
