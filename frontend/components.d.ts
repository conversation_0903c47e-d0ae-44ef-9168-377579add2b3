/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ConfirmButton: typeof import('./src/components/global/ConfirmButton.vue')['default']
    DataTable: typeof import('./src/components/global/DataTable.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopconfirm: typeof import('element-plus/es')['ElPopconfirm']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTag: typeof import('element-plus/es')['ElTag']
    EmptyState: typeof import('./src/components/global/EmptyState.vue')['default']
    FieldMap: typeof import('./src/components/map/FieldMap.vue')['default']
    FileUpload: typeof import('./src/components/upload/FileUpload.vue')['default']
    FormDialog: typeof import('./src/components/global/FormDialog.vue')['default']
    PageContainer: typeof import('./src/components/global/PageContainer.vue')['default']
    RecommendationCard: typeof import('./src/components/recommendations/RecommendationCard.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchForm: typeof import('./src/components/global/SearchForm.vue')['default']
    StatusTag: typeof import('./src/components/global/StatusTag.vue')['default']
    WeatherCard: typeof import('./src/components/weather/WeatherCard.vue')['default']
    WeatherForecast: typeof import('./src/components/weather/WeatherForecast.vue')['default']
  }
}
