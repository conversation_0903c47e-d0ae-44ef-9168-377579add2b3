{"name": "farm-management-frontend", "version": "1.0.0", "description": "农场智慧管理系统前端", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "element-plus": "^2.3.9", "@element-plus/icons-vue": "^2.1.0", "axios": "^1.5.0", "leaflet": "^1.9.4", "vue-leaflet": "^0.1.0", "@vue-leaflet/vue-leaflet": "^0.10.1", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "dayjs": "^1.11.9", "nprogress": "^0.2.0", "js-cookie": "^3.0.5", "@vueuse/core": "^10.4.1", "lodash-es": "^4.17.21", "mitt": "^3.0.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "vite": "^4.4.9", "eslint": "^8.48.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.0.3", "@types/leaflet": "^1.9.4", "@types/js-cookie": "^3.0.3", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.2", "sass": "^1.66.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}