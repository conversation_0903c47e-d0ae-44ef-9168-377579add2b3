import { defineStore } from 'pinia'

export const useAppStore = defineStore('app', {
  state: () => ({
    // 侧边栏
    sidebar: {
      opened: true,
      withoutAnimation: false
    },

    // 设备类型
    device: 'desktop',

    // 主题
    theme: 'light',

    // 语言
    language: 'zh-cn',

    // 页面大小
    size: 'default',

    // 标签页
    tagsView: {
      visitedViews: [],
      cachedViews: []
    },

    // 全屏状态
    isFullscreen: false,

    // 加载状态
    loading: false,

    // 系统配置
    settings: {
      title: '农场智慧管理系统',
      logo: '/logo.svg',
      fixedHeader: true,
      showTagsView: true,
      showSidebar: true,
      showBreadcrumb: true,
      showSettings: true,
      showFooter: true,
      multipleTab: true,
      animation: 'fade-transform'
    },

    // 缓存的数据
    cachedData: {
      fields: [],
      crops: [],
      materials: [],
      users: []
    }
  }),

  getters: {
    // 侧边栏状态
    sidebarOpened: (state) => state.sidebar.opened,

    // 是否移动设备
    isMobile: (state) => state.device === 'mobile',

    // 当前主题
    currentTheme: (state) => state.theme,

    // 访问过的视图
    visitedViews: (state) => state.tagsView.visitedViews,

    // 缓存的视图
    cachedViews: (state) => state.tagsView.cachedViews
  },

  actions: {
    // 初始化应用
    async initApp() {
      // 检测设备类型
      this.detectDevice()

      // 加载主题
      this.loadTheme()

      // 监听窗口大小变化
      this.addResizeListener()
    },

    // 切换侧边栏
    toggleSidebar() {
      this.sidebar.opened = !this.sidebar.opened
      this.sidebar.withoutAnimation = false
    },

    // 关闭侧边栏
    closeSidebar(withoutAnimation = false) {
      this.sidebar.opened = false
      this.sidebar.withoutAnimation = withoutAnimation
    },

    // 检测设备类型
    detectDevice() {
      const rect = document.body.getBoundingClientRect()
      const isMobile = rect.width - 1 < 992
      this.device = isMobile ? 'mobile' : 'desktop'

      if (isMobile) {
        this.closeSidebar(true)
      }
    },

    // 切换主题
    toggleTheme() {
      this.theme = this.theme === 'light' ? 'dark' : 'light'
      this.applyTheme()
    },

    // 设置主题
    setTheme(theme) {
      this.theme = theme
      this.applyTheme()
    },

    // 应用主题
    applyTheme() {
      const html = document.documentElement
      if (this.theme === 'dark') {
        html.classList.add('dark')
      } else {
        html.classList.remove('dark')
      }
      localStorage.setItem('theme', this.theme)
    },

    // 加载主题
    loadTheme() {
      const savedTheme = localStorage.getItem('theme')
      if (savedTheme) {
        this.theme = savedTheme
      }
      this.applyTheme()
    },

    // 设置语言
    setLanguage(language) {
      this.language = language
      localStorage.setItem('language', language)
    },

    // 设置页面大小
    setSize(size) {
      this.size = size
      localStorage.setItem('size', size)
    },

    // 添加访问的视图
    addVisitedView(view) {
      if (this.tagsView.visitedViews.some(v => v.path === view.path)) return

      this.tagsView.visitedViews.push({
        name: view.name,
        path: view.path,
        title: view.meta?.title || 'Unknown',
        affix: view.meta?.affix || false
      })
    },

    // 添加缓存的视图
    addCachedView(view) {
      if (this.tagsView.cachedViews.includes(view.name)) return
      if (view.meta?.keepAlive !== false) {
        this.tagsView.cachedViews.push(view.name)
      }
    },

    // 删除访问的视图
    delVisitedView(view) {
      const index = this.tagsView.visitedViews.findIndex(v => v.path === view.path)
      if (index > -1) {
        this.tagsView.visitedViews.splice(index, 1)
      }
    },

    // 删除缓存的视图
    delCachedView(view) {
      const index = this.tagsView.cachedViews.indexOf(view.name)
      if (index > -1) {
        this.tagsView.cachedViews.splice(index, 1)
      }
    },

    // 删除其他视图
    delOthersViews(view) {
      this.tagsView.visitedViews = this.tagsView.visitedViews.filter(v =>
        v.affix || v.path === view.path
      )
      this.tagsView.cachedViews = this.tagsView.cachedViews.filter(name =>
        name === view.name
      )
    },

    // 删除所有视图
    delAllViews() {
      this.tagsView.visitedViews = this.tagsView.visitedViews.filter(v => v.affix)
      this.tagsView.cachedViews = []
    },

    // 设置全屏状态
    setFullscreen(isFullscreen) {
      this.isFullscreen = isFullscreen
    },

    // 设置加载状态
    setLoading(loading) {
      this.loading = loading
    },

    // 更新设置
    updateSettings(settings) {
      this.settings = { ...this.settings, ...settings }
      localStorage.setItem('app-settings', JSON.stringify(this.settings))
    },

    // 加载设置
    loadSettings() {
      const savedSettings = localStorage.getItem('app-settings')
      if (savedSettings) {
        this.settings = { ...this.settings, ...JSON.parse(savedSettings) }
      }
    },

    // 缓存数据
    setCachedData(key, data) {
      this.cachedData[key] = data
    },

    // 获取缓存数据
    getCachedData(key) {
      return this.cachedData[key]
    },

    // 清除缓存数据
    clearCachedData(key) {
      if (key) {
        this.cachedData[key] = []
      } else {
        this.cachedData = {
          fields: [],
          crops: [],
          materials: [],
          users: []
        }
      }
    },

    // 添加窗口大小监听器
    addResizeListener() {
      window.addEventListener('resize', () => {
        this.detectDevice()
      })
    }
  },

  persist: {
    key: 'app-store',
    storage: localStorage,
    paths: ['theme', 'language', 'size', 'settings', 'sidebar']
  }
})
