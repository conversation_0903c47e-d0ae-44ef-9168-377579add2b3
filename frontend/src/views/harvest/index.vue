<template>
  <page-container title="收获管理" icon="Box">
    <template #actions>
      <el-button type="primary" @click="handleAdd" v-permission="'harvest_manage'">
        <el-icon><Plus /></el-icon>
        新增收获记录
      </el-button>
    </template>

    <template #search>
      <el-form :model="searchForm" inline>
        <el-form-item label="作物名称">
          <el-input
            v-model="searchForm.cropName"
            placeholder="请输入作物名称"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="地块">
          <el-select
            v-model="searchForm.field"
            placeholder="请选择地块"
            clearable
            filterable
            remote
            :remote-method="searchFields"
            :loading="fieldLoading"
          >
            <el-option
              v-for="field in fieldOptions"
              :key="field._id"
              :label="`${field.name} (${field.code})`"
              :value="field._id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="质量等级">
          <el-select v-model="searchForm.quality" placeholder="请选择质量等级" clearable>
            <el-option label="优质" value="excellent" />
            <el-option label="良好" value="good" />
            <el-option label="一般" value="average" />
            <el-option label="较差" value="poor" />
          </el-select>
        </el-form-item>
        <el-form-item label="收获日期">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </template>

    <data-table
      :data="tableData"
      :loading="loading"
      :pagination="pagination"
      @page-change="handlePageChange"
      @size-change="handleSizeChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="crop" label="作物" min-width="150">
        <template #default="{ row }">
          <div v-if="row.crop">
            <div>{{ row.crop.name }} ({{ row.crop.variety }})</div>
            <div class="text-muted">{{ row.field?.name }}</div>
          </div>
          <span v-else class="text-muted">-</span>
        </template>
      </el-table-column>
      <el-table-column prop="harvestDate" label="收获日期" width="120">
        <template #default="{ row }">
          {{ formatDate(row.harvestDate) }}
        </template>
      </el-table-column>
      <el-table-column prop="totalYield" label="总产量" width="120" align="right">
        <template #default="{ row }">
          {{ formatNumber(row.totalYield, 2) }} {{ row.yieldUnit }}
        </template>
      </el-table-column>
      <el-table-column prop="yieldPerArea" label="亩产量" width="120" align="right">
        <template #default="{ row }">
          {{ formatNumber(row.yieldPerArea, 2) }} {{ row.yieldUnit }}/亩
        </template>
      </el-table-column>
      <el-table-column prop="quality" label="质量等级" width="100">
        <template #default="{ row }">
          <status-tag :status="row.quality" />
        </template>
      </el-table-column>
      <el-table-column prop="moistureContent" label="含水率" width="100" align="center">
        <template #default="{ row }">
          {{ row.moistureContent ? `${row.moistureContent}%` : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="totalRevenue" label="总收入(元)" width="120" align="right">
        <template #default="{ row }">
          {{ calculateTotalRevenue(row.sales) }}
        </template>
      </el-table-column>
      <el-table-column prop="totalCost" label="总成本(元)" width="120" align="right">
        <template #default="{ row }">
          {{ calculateTotalCost(row.costs) }}
        </template>
      </el-table-column>
      <el-table-column prop="profit" label="利润(元)" width="120" align="right">
        <template #default="{ row }">
          <span :class="getProfitClass(row)">
            {{ calculateProfit(row) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="supervisor" label="负责人" width="100">
        <template #default="{ row }">
          {{ row.supervisor?.name || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleView(row)">
            查看
          </el-button>
          <el-button 
            type="primary" 
            link 
            @click="handleEdit(row)"
            v-permission="'harvest_manage'"
          >
            编辑
          </el-button>
          <el-button 
            type="danger" 
            link 
            @click="handleDelete(row)"
            v-permission="'harvest_manage'"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </data-table>

    <!-- 新增/编辑对话框 -->
    <form-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      :confirm-loading="submitLoading"
      width="1000px"
      @confirm="handleSubmit"
      @cancel="handleCancel"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="作物" prop="crop">
              <el-select
                v-model="formData.crop"
                placeholder="请选择作物"
                style="width: 100%"
                filterable
                remote
                :remote-method="searchCrops"
                :loading="cropLoading"
                @change="handleCropChange"
              >
                <el-option
                  v-for="crop in cropOptions"
                  :key="crop._id"
                  :label="`${crop.name} (${crop.variety}) - ${crop.field?.name}`"
                  :value="crop._id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收获日期" prop="harvestDate">
              <el-date-picker
                v-model="formData.harvestDate"
                type="date"
                placeholder="请选择收获日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="总产量" prop="totalYield">
              <el-input-number
                v-model="formData.totalYield"
                :min="0"
                :precision="2"
                placeholder="请输入总产量"
                style="width: 100%"
                @change="calculateYieldPerArea"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产量单位" prop="yieldUnit">
              <el-select v-model="formData.yieldUnit" placeholder="请选择产量单位" style="width: 100%">
                <el-option label="公斤" value="公斤" />
                <el-option label="吨" value="吨" />
                <el-option label="斤" value="斤" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="收获面积(亩)" prop="harvestArea">
              <el-input-number
                v-model="formData.harvestArea"
                :min="0"
                :precision="2"
                placeholder="请输入收获面积"
                style="width: 100%"
                @change="calculateYieldPerArea"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="亩产量" prop="yieldPerArea">
              <el-input-number
                v-model="formData.yieldPerArea"
                :min="0"
                :precision="2"
                placeholder="自动计算"
                style="width: 100%"
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="质量等级" prop="quality">
              <el-select v-model="formData.quality" placeholder="请选择质量等级" style="width: 100%">
                <el-option label="优质" value="excellent" />
                <el-option label="良好" value="good" />
                <el-option label="一般" value="average" />
                <el-option label="较差" value="poor" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="含水率(%)" prop="moistureContent">
              <el-input-number
                v-model="formData.moistureContent"
                :min="0"
                :max="100"
                :precision="1"
                placeholder="请输入含水率"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 销售信息 -->
        <el-divider content-position="left">销售信息</el-divider>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="销售数量" prop="sales.quantity">
              <el-input-number
                v-model="formData.sales.quantity"
                :min="0"
                :precision="2"
                placeholder="请输入销售数量"
                style="width: 100%"
                @change="calculateSalesAmount"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="销售单价(元)" prop="sales.unitPrice">
              <el-input-number
                v-model="formData.sales.unitPrice"
                :min="0"
                :precision="2"
                placeholder="请输入销售单价"
                style="width: 100%"
                @change="calculateSalesAmount"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="销售总额(元)" prop="sales.totalAmount">
              <el-input-number
                v-model="formData.sales.totalAmount"
                :min="0"
                :precision="2"
                placeholder="自动计算"
                style="width: 100%"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="销售日期" prop="sales.saleDate">
              <el-date-picker
                v-model="formData.sales.saleDate"
                type="date"
                placeholder="请选择销售日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="买方信息" prop="sales.buyer">
          <el-input v-model="formData.sales.buyer" placeholder="请输入买方信息" />
        </el-form-item>
        
        <!-- 成本信息 -->
        <el-divider content-position="left">成本信息</el-divider>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="人工成本(元)">
              <el-input-number
                v-model="formData.costs.labor"
                :min="0"
                :precision="2"
                placeholder="请输入人工成本"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="机械成本(元)">
              <el-input-number
                v-model="formData.costs.machinery"
                :min="0"
                :precision="2"
                placeholder="请输入机械成本"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="运输成本(元)">
              <el-input-number
                v-model="formData.costs.transportation"
                :min="0"
                :precision="2"
                placeholder="请输入运输成本"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="包装成本(元)">
              <el-input-number
                v-model="formData.costs.packaging"
                :min="0"
                :precision="2"
                placeholder="请输入包装成本"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="储存成本(元)">
              <el-input-number
                v-model="formData.costs.storage"
                :min="0"
                :precision="2"
                placeholder="请输入储存成本"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="其他成本(元)">
              <el-input-number
                v-model="formData.costs.other"
                :min="0"
                :precision="2"
                placeholder="请输入其他成本"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="formData.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
    </form-dialog>
  </page-container>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getHarvests, createHarvest, updateHarvest, deleteHarvest } from '@/api/harvest'
import { getFieldOptions } from '@/api/fields'
import { getCropOptions } from '@/api/crops'
import { formatNumber, formatDate } from '@/utils'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const fieldLoading = ref(false)
const cropLoading = ref(false)
const tableData = ref([])
const fieldOptions = ref([])
const cropOptions = ref([])
const dialogVisible = ref(false)
const dialogTitle = ref('')
const currentRow = ref(null)
const formRef = ref()
const dateRange = ref([])

// 搜索表单
const searchForm = reactive({
  cropName: '',
  field: '',
  quality: '',
  startDate: '',
  endDate: ''
})

// 分页数据
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 表单数据
const formData = reactive({
  crop: '',
  harvestDate: '',
  totalYield: null,
  yieldUnit: '公斤',
  harvestArea: null,
  yieldPerArea: null,
  quality: '',
  moistureContent: null,
  sales: {
    quantity: null,
    unitPrice: null,
    totalAmount: null,
    saleDate: '',
    buyer: ''
  },
  costs: {
    labor: 0,
    machinery: 0,
    transportation: 0,
    packaging: 0,
    storage: 0,
    other: 0
  },
  notes: ''
})

// 表单验证规则
const formRules = {
  crop: [
    { required: true, message: '请选择作物', trigger: 'change' }
  ],
  harvestDate: [
    { required: true, message: '请选择收获日期', trigger: 'change' }
  ],
  totalYield: [
    { required: true, message: '请输入总产量', trigger: 'blur' },
    { type: 'number', min: 0, message: '总产量必须大于0', trigger: 'blur' }
  ],
  yieldUnit: [
    { required: true, message: '请选择产量单位', trigger: 'change' }
  ],
  harvestArea: [
    { required: true, message: '请输入收获面积', trigger: 'blur' },
    { type: 'number', min: 0, message: '收获面积必须大于0', trigger: 'blur' }
  ]
}

// 方法
const loadData = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.current,
      limit: pagination.pageSize,
      ...searchForm
    }
    
    const response = await getHarvests(params)
    tableData.value = response.data.harvests
    pagination.total = response.data.pagination.total
  } catch (error) {
    console.error('加载收获数据失败:', error)
  } finally {
    loading.value = false
  }
}

const searchFields = async (query) => {
  try {
    fieldLoading.value = true
    const response = await getFieldOptions({ search: query })
    fieldOptions.value = response.data.fields || []
  } catch (error) {
    console.error('搜索地块失败:', error)
  } finally {
    fieldLoading.value = false
  }
}

const searchCrops = async (query) => {
  try {
    cropLoading.value = true
    const response = await getCropOptions({ search: query, status: 'active' })
    cropOptions.value = response.data.crops || []
  } catch (error) {
    console.error('搜索作物失败:', error)
  } finally {
    cropLoading.value = false
  }
}

const handleDateRangeChange = (dates) => {
  if (dates && dates.length === 2) {
    searchForm.startDate = dates[0]
    searchForm.endDate = dates[1]
  } else {
    searchForm.startDate = ''
    searchForm.endDate = ''
  }
}

const handleCropChange = (cropId) => {
  const selectedCrop = cropOptions.value.find(crop => crop._id === cropId)
  if (selectedCrop) {
    formData.harvestArea = selectedCrop.plantingArea
    calculateYieldPerArea()
  }
}

const calculateYieldPerArea = () => {
  if (formData.totalYield && formData.harvestArea) {
    formData.yieldPerArea = formData.totalYield / formData.harvestArea
  }
}

const calculateSalesAmount = () => {
  if (formData.sales.quantity && formData.sales.unitPrice) {
    formData.sales.totalAmount = formData.sales.quantity * formData.sales.unitPrice
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  dateRange.value = []
  handleSearch()
}

const handlePageChange = (page) => {
  pagination.current = page
  loadData()
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.current = 1
  loadData()
}

const handleAdd = () => {
  dialogTitle.value = '新增收获记录'
  currentRow.value = null
  resetForm()
  dialogVisible.value = true
  searchCrops('')
}

const handleEdit = (row) => {
  dialogTitle.value = '编辑收获记录'
  currentRow.value = row
  Object.keys(formData).forEach(key => {
    if (key === 'crop') {
      formData[key] = row.crop?._id || ''
    } else if (key === 'harvestDate') {
      formData[key] = row[key] ? new Date(row[key]) : ''
    } else if (key === 'sales') {
      formData[key] = { 
        ...formData[key], 
        ...row[key],
        saleDate: row[key]?.saleDate ? new Date(row[key].saleDate) : ''
      }
    } else if (key === 'costs') {
      formData[key] = { ...formData[key], ...row[key] }
    } else {
      formData[key] = row[key] || ''
    }
  })
  dialogVisible.value = true
  searchCrops('')
}

const handleView = (row) => {
  router.push(`/harvest/${row._id}`)
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这条收获记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteHarvest(row._id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除收获记录失败:', error)
    }
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    const submitData = { ...formData }
    // 处理日期格式
    if (submitData.harvestDate) {
      submitData.harvestDate = new Date(submitData.harvestDate).toISOString()
    }
    if (submitData.sales.saleDate) {
      submitData.sales.saleDate = new Date(submitData.sales.saleDate).toISOString()
    }
    
    if (currentRow.value) {
      await updateHarvest(currentRow.value._id, submitData)
      ElMessage.success('更新成功')
    } else {
      await createHarvest(submitData)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    loadData()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}

const handleCancel = () => {
  dialogVisible.value = false
}

const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'yieldUnit') {
      formData[key] = '公斤'
    } else if (key === 'sales') {
      formData[key] = {
        quantity: null,
        unitPrice: null,
        totalAmount: null,
        saleDate: '',
        buyer: ''
      }
    } else if (key === 'costs') {
      formData[key] = {
        labor: 0,
        machinery: 0,
        transportation: 0,
        packaging: 0,
        storage: 0,
        other: 0
      }
    } else if (typeof formData[key] === 'number') {
      formData[key] = null
    } else {
      formData[key] = ''
    }
  })
  formRef.value?.clearValidate()
}

// 工具方法
const calculateTotalRevenue = (sales) => {
  if (!sales || !sales.totalAmount) return '0.00'
  return formatNumber(sales.totalAmount, 2)
}

const calculateTotalCost = (costs) => {
  if (!costs) return '0.00'
  const total = (costs.labor || 0) + (costs.machinery || 0) + (costs.transportation || 0) + 
                (costs.packaging || 0) + (costs.storage || 0) + (costs.other || 0)
  return formatNumber(total, 2)
}

const calculateProfit = (row) => {
  const revenue = row.sales?.totalAmount || 0
  const costs = (row.costs?.labor || 0) + (row.costs?.machinery || 0) + (row.costs?.transportation || 0) + 
                (row.costs?.packaging || 0) + (row.costs?.storage || 0) + (row.costs?.other || 0)
  return formatNumber(revenue - costs, 2)
}

const getProfitClass = (row) => {
  const profit = parseFloat(calculateProfit(row))
  return profit >= 0 ? 'text-success' : 'text-danger'
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.text-muted {
  color: #909399;
}

.text-success {
  color: #67c23a;
}

.text-danger {
  color: #f56c6c;
}
</style>
