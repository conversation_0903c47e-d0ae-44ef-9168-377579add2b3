<template>
  <div class="system-container">
    <el-card class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2>系统管理</h2>
          <p>系统配置、监控和维护</p>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="refreshSystemInfo">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </el-card>

    <el-row :gutter="20">
      <!-- 系统信息 -->
      <el-col :span="12">
        <el-card title="系统信息" class="system-info-card">
          <template #header>
            <div class="card-header">
              <span>系统信息</span>
              <el-tag :type="systemHealth.type">{{ systemHealth.text }}</el-tag>
            </div>
          </template>
          
          <div class="info-list">
            <div class="info-item">
              <span class="label">系统版本:</span>
              <span class="value">{{ systemInfo.version }}</span>
            </div>
            <div class="info-item">
              <span class="label">运行时间:</span>
              <span class="value">{{ formatUptime(systemInfo.uptime) }}</span>
            </div>
            <div class="info-item">
              <span class="label">服务器时间:</span>
              <span class="value">{{ formatDateTime(systemInfo.serverTime) }}</span>
            </div>
            <div class="info-item">
              <span class="label">数据库状态:</span>
              <el-tag :type="systemInfo.database.status === 'connected' ? 'success' : 'danger'">
                {{ systemInfo.database.status === 'connected' ? '已连接' : '断开' }}
              </el-tag>
            </div>
            <div class="info-item">
              <span class="label">Redis状态:</span>
              <el-tag :type="systemInfo.redis.status === 'connected' ? 'success' : 'danger'">
                {{ systemInfo.redis.status === 'connected' ? '已连接' : '断开' }}
              </el-tag>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 资源使用情况 -->
      <el-col :span="12">
        <el-card title="资源使用情况">
          <div class="resource-list">
            <div class="resource-item">
              <div class="resource-header">
                <span>CPU使用率</span>
                <span class="percentage">{{ systemResources.cpu }}%</span>
              </div>
              <el-progress 
                :percentage="systemResources.cpu" 
                :color="getProgressColor(systemResources.cpu)"
                :show-text="false"
              />
            </div>
            
            <div class="resource-item">
              <div class="resource-header">
                <span>内存使用率</span>
                <span class="percentage">{{ systemResources.memory }}%</span>
              </div>
              <el-progress 
                :percentage="systemResources.memory" 
                :color="getProgressColor(systemResources.memory)"
                :show-text="false"
              />
            </div>
            
            <div class="resource-item">
              <div class="resource-header">
                <span>磁盘使用率</span>
                <span class="percentage">{{ systemResources.disk }}%</span>
              </div>
              <el-progress 
                :percentage="systemResources.disk" 
                :color="getProgressColor(systemResources.disk)"
                :show-text="false"
              />
            </div>
            
            <div class="resource-item">
              <div class="resource-header">
                <span>网络使用率</span>
                <span class="percentage">{{ systemResources.network }}%</span>
              </div>
              <el-progress 
                :percentage="systemResources.network" 
                :color="getProgressColor(systemResources.network)"
                :show-text="false"
              />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 系统配置 -->
      <el-col :span="12">
        <el-card title="系统配置">
          <el-form :model="systemConfig" label-width="120px">
            <el-form-item label="系统名称:">
              <el-input v-model="systemConfig.systemName" />
            </el-form-item>
            
            <el-form-item label="管理员邮箱:">
              <el-input v-model="systemConfig.adminEmail" />
            </el-form-item>
            
            <el-form-item label="数据保留天数:">
              <el-input-number 
                v-model="systemConfig.dataRetentionDays" 
                :min="1" 
                :max="365"
              />
            </el-form-item>
            
            <el-form-item label="自动备份:">
              <el-switch v-model="systemConfig.autoBackup" />
            </el-form-item>
            
            <el-form-item label="邮件通知:">
              <el-switch v-model="systemConfig.emailNotification" />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="saveSystemConfig" :loading="saving">
                保存配置
              </el-button>
              <el-button @click="resetSystemConfig">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 系统操作 -->
      <el-col :span="12">
        <el-card title="系统操作">
          <div class="operation-list">
            <div class="operation-item">
              <div class="operation-info">
                <h4>清理系统缓存</h4>
                <p>清理临时文件和缓存数据</p>
              </div>
              <el-button type="warning" @click="clearCache" :loading="operations.clearCache">
                清理缓存
              </el-button>
            </div>
            
            <div class="operation-item">
              <div class="operation-info">
                <h4>数据库备份</h4>
                <p>创建数据库完整备份</p>
              </div>
              <el-button type="primary" @click="backupDatabase" :loading="operations.backup">
                立即备份
              </el-button>
            </div>
            
            <div class="operation-item">
              <div class="operation-info">
                <h4>系统日志</h4>
                <p>查看和下载系统日志</p>
              </div>
              <el-button @click="viewLogs">查看日志</el-button>
              <el-button @click="downloadLogs" :loading="operations.downloadLogs">
                下载日志
              </el-button>
            </div>
            
            <div class="operation-item">
              <div class="operation-info">
                <h4>重启服务</h4>
                <p>重启系统服务（谨慎操作）</p>
              </div>
              <el-button type="danger" @click="confirmRestartService">
                重启服务
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统日志对话框 -->
    <el-dialog v-model="logDialogVisible" title="系统日志" width="80%" top="5vh">
      <div class="log-container">
        <div class="log-filters">
          <el-select v-model="logFilter.level" placeholder="日志级别" clearable>
            <el-option label="错误" value="error" />
            <el-option label="警告" value="warn" />
            <el-option label="信息" value="info" />
            <el-option label="调试" value="debug" />
          </el-select>
          
          <el-date-picker
            v-model="logFilter.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
          
          <el-button type="primary" @click="fetchLogs">查询</el-button>
          <el-button @click="clearLogFilter">清除筛选</el-button>
        </div>
        
        <div class="log-content">
          <el-table :data="logs" height="400" stripe>
            <el-table-column prop="timestamp" label="时间" width="180" />
            <el-table-column prop="level" label="级别" width="80">
              <template #default="{ row }">
                <el-tag :type="getLogLevelType(row.level)">{{ row.level }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="message" label="消息" />
            <el-table-column prop="source" label="来源" width="120" />
          </el-table>
          
          <el-pagination
            v-model:current-page="logPagination.current"
            v-model:page-size="logPagination.pageSize"
            :total="logPagination.total"
            :page-sizes="[20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="fetchLogs"
            @current-change="fetchLogs"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { 
  getSystemInfo, 
  getSystemResources, 
  getSystemConfig,
  updateSystemConfig,
  clearSystemCache,
  backupSystemDatabase,
  getSystemLogs,
  downloadSystemLogs,
  restartSystemService
} from '@/api/system'
import { formatDateTime } from '@/utils'

// 响应式数据
const loading = ref(false)
const saving = ref(false)

const systemInfo = ref({
  version: '1.0.0',
  uptime: 0,
  serverTime: new Date(),
  database: { status: 'connected' },
  redis: { status: 'connected' }
})

const systemResources = ref({
  cpu: 0,
  memory: 0,
  disk: 0,
  network: 0
})

const systemConfig = reactive({
  systemName: '农场智慧管理系统',
  adminEmail: '',
  dataRetentionDays: 90,
  autoBackup: true,
  emailNotification: true
})

const operations = reactive({
  clearCache: false,
  backup: false,
  downloadLogs: false
})

// 日志相关
const logDialogVisible = ref(false)
const logs = ref([])
const logFilter = reactive({
  level: '',
  dateRange: []
})
const logPagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 计算属性
const systemHealth = computed(() => {
  const avgUsage = (systemResources.value.cpu + systemResources.value.memory + systemResources.value.disk) / 3
  
  if (avgUsage < 60) {
    return { type: 'success', text: '良好' }
  } else if (avgUsage < 80) {
    return { type: 'warning', text: '警告' }
  } else {
    return { type: 'danger', text: '危险' }
  }
})

// 方法
const getProgressColor = (percentage) => {
  if (percentage < 60) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

const getLogLevelType = (level) => {
  const typeMap = {
    error: 'danger',
    warn: 'warning',
    info: 'info',
    debug: 'info'
  }
  return typeMap[level] || 'info'
}

const formatUptime = (seconds) => {
  const days = Math.floor(seconds / 86400)
  const hours = Math.floor((seconds % 86400) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  return `${days}天 ${hours}小时 ${minutes}分钟`
}

// 获取系统信息
const fetchSystemInfo = async () => {
  try {
    const response = await getSystemInfo()
    systemInfo.value = response.data
  } catch (error) {
    ElMessage.error('获取系统信息失败')
  }
}

// 获取系统资源
const fetchSystemResources = async () => {
  try {
    const response = await getSystemResources()
    systemResources.value = response.data
  } catch (error) {
    ElMessage.error('获取系统资源失败')
  }
}

// 获取系统配置
const fetchSystemConfig = async () => {
  try {
    const response = await getSystemConfig()
    Object.assign(systemConfig, response.data)
  } catch (error) {
    ElMessage.error('获取系统配置失败')
  }
}

// 刷新系统信息
const refreshSystemInfo = async () => {
  loading.value = true
  try {
    await Promise.all([
      fetchSystemInfo(),
      fetchSystemResources()
    ])
    ElMessage.success('刷新成功')
  } finally {
    loading.value = false
  }
}

// 保存系统配置
const saveSystemConfig = async () => {
  saving.value = true
  try {
    await updateSystemConfig(systemConfig)
    ElMessage.success('配置保存成功')
  } catch (error) {
    ElMessage.error('配置保存失败')
  } finally {
    saving.value = false
  }
}

// 重置系统配置
const resetSystemConfig = () => {
  fetchSystemConfig()
}

// 清理缓存
const clearCache = async () => {
  try {
    await ElMessageBox.confirm('确定要清理系统缓存吗？', '确认操作', {
      type: 'warning'
    })
    
    operations.clearCache = true
    await clearSystemCache()
    ElMessage.success('缓存清理成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('缓存清理失败')
    }
  } finally {
    operations.clearCache = false
  }
}

// 备份数据库
const backupDatabase = async () => {
  try {
    await ElMessageBox.confirm('确定要创建数据库备份吗？', '确认操作', {
      type: 'info'
    })
    
    operations.backup = true
    await backupSystemDatabase()
    ElMessage.success('数据库备份成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('数据库备份失败')
    }
  } finally {
    operations.backup = false
  }
}

// 查看日志
const viewLogs = () => {
  logDialogVisible.value = true
  fetchLogs()
}

// 获取日志
const fetchLogs = async () => {
  try {
    const params = {
      page: logPagination.current,
      limit: logPagination.pageSize,
      level: logFilter.level,
      startTime: logFilter.dateRange?.[0],
      endTime: logFilter.dateRange?.[1]
    }
    
    const response = await getSystemLogs(params)
    logs.value = response.data.logs
    logPagination.total = response.data.total
  } catch (error) {
    ElMessage.error('获取日志失败')
  }
}

// 清除日志筛选
const clearLogFilter = () => {
  logFilter.level = ''
  logFilter.dateRange = []
  fetchLogs()
}

// 下载日志
const downloadLogs = async () => {
  operations.downloadLogs = true
  try {
    const blob = await downloadSystemLogs()
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `system-logs-${new Date().toISOString().split('T')[0]}.zip`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('日志下载成功')
  } catch (error) {
    ElMessage.error('日志下载失败')
  } finally {
    operations.downloadLogs = false
  }
}

// 确认重启服务
const confirmRestartService = async () => {
  try {
    await ElMessageBox.confirm(
      '重启服务将导致系统暂时不可用，确定要继续吗？',
      '危险操作',
      {
        type: 'error',
        confirmButtonText: '确定重启',
        cancelButtonText: '取消'
      }
    )
    
    await restartSystemService()
    ElMessage.success('服务重启指令已发送')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('服务重启失败')
    }
  }
}

// 生命周期
onMounted(() => {
  fetchSystemInfo()
  fetchSystemResources()
  fetchSystemConfig()
  
  // 定时刷新资源使用情况
  setInterval(() => {
    fetchSystemResources()
  }, 30000)
})
</script>

<style scoped>
.system-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #909399;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-list {
  space-y: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  font-weight: 500;
  color: #606266;
}

.value {
  color: #303133;
}

.resource-list {
  space-y: 16px;
}

.resource-item {
  margin-bottom: 20px;
}

.resource-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.percentage {
  font-weight: 600;
  color: #303133;
}

.operation-list {
  space-y: 16px;
}

.operation-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  margin-bottom: 16px;
}

.operation-info h4 {
  margin: 0 0 4px 0;
  color: #303133;
}

.operation-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.log-container {
  height: 500px;
}

.log-filters {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  align-items: center;
}

.log-content {
  height: calc(100% - 60px);
}
</style>
