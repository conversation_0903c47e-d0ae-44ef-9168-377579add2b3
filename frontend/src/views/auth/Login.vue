<template>
  <div class="login-container">
    <div class="login-background">
      <div class="bg-image"></div>
      <div class="bg-overlay"></div>
    </div>

    <div class="login-content">
      <div class="login-form-container">
        <div class="login-header">
          <div class="logo">
            <img src="/logo.svg" alt="Logo" class="logo-image" />
            <h1 class="system-title">农场智慧管理系统</h1>
          </div>
          <p class="system-desc">现代化农场管理，智能化农业生产</p>
        </div>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          size="large"
          @keyup.enter="handleLogin"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名或邮箱"
              prefix-icon="User"
              clearable
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              prefix-icon="Lock"
              show-password
              clearable
            />
          </el-form-item>

          <el-form-item>
            <div class="login-options">
              <el-checkbox v-model="loginForm.rememberMe">
                记住我
              </el-checkbox>
              <el-link type="primary" @click="showForgotPassword">
                忘记密码？
              </el-link>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              class="login-button"
              :loading="loading"
              @click="handleLogin"
            >
              {{ loading ? '登录中...' : '登录' }}
            </el-button>
          </el-form-item>

          <el-form-item>
            <div class="register-link">
              还没有账号？
              <el-link type="primary" @click="showRegister">
                立即注册
              </el-link>
            </div>
          </el-form-item>
        </el-form>

        <div class="demo-accounts">
          <el-divider>演示账号</el-divider>
          <div class="demo-buttons">
            <el-button size="small" @click="fillDemoAccount('admin')">
              管理员
            </el-button>
            <el-button size="small" @click="fillDemoAccount('manager')">
              管理者
            </el-button>
            <el-button size="small" @click="fillDemoAccount('worker')">
              工作人员
            </el-button>
          </div>
        </div>
      </div>

      <div class="login-features">
        <h3>系统特色</h3>
        <div class="feature-list">
          <div class="feature-item">
            <el-icon><MapLocation /></el-icon>
            <div>
              <h4>GIS地块管理</h4>
              <p>可视化地块管理，精确定位每一块农田</p>
            </div>
          </div>
          <div class="feature-item">
            <el-icon><Tools /></el-icon>
            <div>
              <h4>智能农事记录</h4>
              <p>全程记录种植、浇灌、施肥、收获等操作</p>
            </div>
          </div>
          <div class="feature-item">
            <el-icon><DataAnalysis /></el-icon>
            <div>
              <h4>数据分析统计</h4>
              <p>多维度数据分析，科学指导农业生产</p>
            </div>
          </div>
          <div class="feature-item">
            <el-icon><Sunny /></el-icon>
            <div>
              <h4>天气监控预警</h4>
              <p>实时天气信息，农业气象指导</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

const loginFormRef = ref()
const loading = ref(false)

// 登录表单
const loginForm = reactive({
  username: '',
  password: '',
  rememberMe: false
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名或邮箱', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()
    loading.value = true

    await userStore.login(loginForm)

    // 登录成功后跳转
    const redirect = router.currentRoute.value.query.redirect || '/dashboard'
    router.push(redirect)
  } catch (error) {
    console.error('登录失败:', error)
  } finally {
    loading.value = false
  }
}

// 填充演示账号
const fillDemoAccount = (type) => {
  const accounts = {
    admin: { username: 'admin', password: '123456' },
    manager: { username: 'manager', password: '123456' },
    worker: { username: 'worker', password: '123456' }
  }

  const account = accounts[type]
  if (account) {
    loginForm.username = account.username
    loginForm.password = account.password
  }
}

// 显示忘记密码
const showForgotPassword = () => {
  ElMessage.info('忘记密码功能开发中...')
}

// 显示注册
const showRegister = () => {
  ElMessage.info('注册功能开发中...')
}
</script>

<style lang="scss" scoped>
.login-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  .bg-image {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: cover;
    background-position: center;
  }

  .bg-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
  }
}

.login-content {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.login-form-container {
  flex: 1;
  max-width: 480px;
  padding: 60px 40px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;

  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;

    .logo-image {
      width: 48px;
      height: 48px;
      margin-right: 12px;
    }

    .system-title {
      font-size: 28px;
      font-weight: 600;
      color: #2c3e50;
      margin: 0;
    }
  }

  .system-desc {
    color: #7f8c8d;
    font-size: 16px;
    margin: 0;
  }
}

.login-form {
  .login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .login-button {
    width: 100%;
    height: 48px;
    font-size: 16px;
    font-weight: 500;
  }

  .register-link {
    text-align: center;
    color: #7f8c8d;
  }
}

.demo-accounts {
  margin-top: 20px;

  .demo-buttons {
    display: flex;
    justify-content: space-around;
    gap: 8px;
  }
}

.login-features {
  flex: 1;
  padding: 60px 40px;
  color: white;

  h3 {
    font-size: 24px;
    margin-bottom: 30px;
    text-align: center;
  }

  .feature-list {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .feature-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;

    .el-icon {
      font-size: 24px;
      color: #67c23a;
      margin-top: 4px;
    }

    h4 {
      font-size: 18px;
      margin: 0 0 8px 0;
      color: white;
    }

    p {
      margin: 0;
      color: rgba(255, 255, 255, 0.8);
      line-height: 1.5;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-content {
    flex-direction: column;
  }

  .login-form-container {
    max-width: 100%;
    padding: 40px 20px;
  }

  .login-features {
    display: none;
  }
}

@media (max-width: 480px) {
  .login-form-container {
    padding: 20px 16px;
  }

  .login-header .logo .system-title {
    font-size: 24px;
  }
}
</style>
