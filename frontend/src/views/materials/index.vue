<template>
  <page-container title="物资管理" icon="Goods">
    <template #actions>
      <el-button type="primary" @click="handleAdd" v-permission="'material_manage'">
        <el-icon><Plus /></el-icon>
        新增物资
      </el-button>
      <el-button type="success" @click="handleImport" v-permission="'material_manage'">
        <el-icon><Upload /></el-icon>
        导入物资
      </el-button>
      <el-button type="warning" @click="handleExport">
        <el-icon><Download /></el-icon>
        导出数据
      </el-button>
    </template>

    <template #search>
      <el-form :model="searchForm" inline>
        <el-form-item label="物资名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入物资名称"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="物资编码">
          <el-input
            v-model="searchForm.code"
            placeholder="请输入物资编码"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="物资类别">
          <el-select v-model="searchForm.category" placeholder="请选择物资类别" clearable>
            <el-option label="种子" value="种子" />
            <el-option label="肥料" value="肥料" />
            <el-option label="农药" value="农药" />
            <el-option label="工具" value="工具" />
            <el-option label="设备" value="设备" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        <el-form-item label="库存状态">
          <el-select v-model="searchForm.stockStatus" placeholder="请选择库存状态" clearable>
            <el-option label="充足" value="sufficient" />
            <el-option label="不足" value="low" />
            <el-option label="缺货" value="empty" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="活跃" value="active" />
            <el-option label="非活跃" value="inactive" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </template>

    <data-table
      :data="tableData"
      :loading="loading"
      :pagination="pagination"
      @page-change="handlePageChange"
      @size-change="handleSizeChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="code" label="物资编码" width="120" />
      <el-table-column prop="name" label="物资名称" min-width="150" />
      <el-table-column prop="category" label="类别" width="100" />
      <el-table-column prop="subcategory" label="子类别" width="120" />
      <el-table-column prop="specification" label="规格" width="120" />
      <el-table-column prop="unit" label="单位" width="80" />
      <el-table-column prop="currentStock" label="当前库存" width="100" align="right">
        <template #default="{ row }">
          <span :class="getStockClass(row)">
            {{ row.inventory?.currentStock || 0 }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="minStock" label="最低库存" width="100" align="right">
        <template #default="{ row }">
          {{ row.inventory?.minStock || 0 }}
        </template>
      </el-table-column>
      <el-table-column prop="stockStatus" label="库存状态" width="100">
        <template #default="{ row }">
          <status-tag :status="getStockStatus(row)" />
        </template>
      </el-table-column>
      <el-table-column prop="averagePrice" label="平均价格(元)" width="120" align="right">
        <template #default="{ row }">
          {{ formatNumber(row.pricing?.averagePrice || 0, 2) }}
        </template>
      </el-table-column>
      <el-table-column prop="totalValue" label="库存价值(元)" width="120" align="right">
        <template #default="{ row }">
          {{ calculateStockValue(row) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="80">
        <template #default="{ row }">
          <status-tag :status="row.status" />
        </template>
      </el-table-column>
      <el-table-column prop="updatedAt" label="更新时间" width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.updatedAt) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleView(row)">
            查看
          </el-button>
          <el-button 
            type="primary" 
            link 
            @click="handleEdit(row)"
            v-permission="'material_manage'"
          >
            编辑
          </el-button>
          <el-button 
            type="success" 
            link 
            @click="handleStock(row)"
            v-permission="'material_manage'"
          >
            库存
          </el-button>
          <el-button 
            type="danger" 
            link 
            @click="handleDelete(row)"
            v-permission="'material_manage'"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </data-table>

    <!-- 新增/编辑对话框 -->
    <form-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      :confirm-loading="submitLoading"
      width="800px"
      @confirm="handleSubmit"
      @cancel="handleCancel"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="物资编码" prop="code">
              <el-input v-model="formData.code" placeholder="请输入物资编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物资名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入物资名称" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="物资类别" prop="category">
              <el-select v-model="formData.category" placeholder="请选择物资类别" style="width: 100%">
                <el-option label="种子" value="种子" />
                <el-option label="肥料" value="肥料" />
                <el-option label="农药" value="农药" />
                <el-option label="工具" value="工具" />
                <el-option label="设备" value="设备" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="子类别" prop="subcategory">
              <el-input v-model="formData.subcategory" placeholder="请输入子类别" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="规格" prop="specification">
              <el-input v-model="formData.specification" placeholder="请输入规格" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位" prop="unit">
              <el-select v-model="formData.unit" placeholder="请选择单位" style="width: 100%">
                <el-option label="袋" value="袋" />
                <el-option label="瓶" value="瓶" />
                <el-option label="桶" value="桶" />
                <el-option label="箱" value="箱" />
                <el-option label="个" value="个" />
                <el-option label="台" value="台" />
                <el-option label="套" value="套" />
                <el-option label="公斤" value="公斤" />
                <el-option label="吨" value="吨" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 库存信息 -->
        <el-divider content-position="left">库存信息</el-divider>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="当前库存" prop="inventory.currentStock">
              <el-input-number
                v-model="formData.inventory.currentStock"
                :min="0"
                placeholder="请输入当前库存"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最低库存" prop="inventory.minStock">
              <el-input-number
                v-model="formData.inventory.minStock"
                :min="0"
                placeholder="请输入最低库存"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="安全库存" prop="inventory.safetyStock">
              <el-input-number
                v-model="formData.inventory.safetyStock"
                :min="0"
                placeholder="请输入安全库存"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 价格信息 -->
        <el-divider content-position="left">价格信息</el-divider>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="采购价格(元)" prop="pricing.purchasePrice">
              <el-input-number
                v-model="formData.pricing.purchasePrice"
                :min="0"
                :precision="2"
                placeholder="请输入采购价格"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="平均价格(元)" prop="pricing.averagePrice">
              <el-input-number
                v-model="formData.pricing.averagePrice"
                :min="0"
                :precision="2"
                placeholder="请输入平均价格"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最新价格(元)" prop="pricing.lastPrice">
              <el-input-number
                v-model="formData.pricing.lastPrice"
                :min="0"
                :precision="2"
                placeholder="请输入最新价格"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 供应商信息 -->
        <el-divider content-position="left">供应商信息</el-divider>
        <div v-for="(supplier, index) in formData.suppliers" :key="index" class="supplier-item">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-form-item :label="`供应商${index + 1}`">
                <el-input v-model="supplier.name" placeholder="供应商名称" />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="联系人">
                <el-input v-model="supplier.contact" placeholder="联系人" />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="电话">
                <el-input v-model="supplier.phone" placeholder="联系电话" />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="价格(元)">
                <el-input-number
                  v-model="supplier.price"
                  :min="0"
                  :precision="2"
                  placeholder="价格"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-form-item label=" ">
                <el-button type="danger" @click="removeSupplier(index)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <el-button type="primary" @click="addSupplier">
          <el-icon><Plus /></el-icon>
          添加供应商
        </el-button>
        
        <el-form-item label="状态" prop="status" style="margin-top: 20px;">
          <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
            <el-option label="活跃" value="active" />
            <el-option label="非活跃" value="inactive" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="formData.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
    </form-dialog>

    <!-- 库存管理对话框 -->
    <form-dialog
      v-model="stockDialogVisible"
      title="库存管理"
      :confirm-loading="stockSubmitLoading"
      width="600px"
      @confirm="handleStockSubmit"
      @cancel="handleStockCancel"
    >
      <el-form
        ref="stockFormRef"
        :model="stockFormData"
        :rules="stockFormRules"
        label-width="120px"
      >
        <el-form-item label="物资名称">
          <el-input :value="currentMaterial?.name" disabled />
        </el-form-item>
        
        <el-form-item label="当前库存">
          <el-input :value="`${currentMaterial?.inventory?.currentStock || 0} ${currentMaterial?.unit}`" disabled />
        </el-form-item>
        
        <el-form-item label="操作类型" prop="type">
          <el-radio-group v-model="stockFormData.type">
            <el-radio label="in">入库</el-radio>
            <el-radio label="out">出库</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="数量" prop="quantity">
          <el-input-number
            v-model="stockFormData.quantity"
            :min="1"
            placeholder="请输入数量"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="单价(元)" prop="unitPrice" v-if="stockFormData.type === 'in'">
          <el-input-number
            v-model="stockFormData.unitPrice"
            :min="0"
            :precision="2"
            placeholder="请输入单价"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="供应商" prop="supplier" v-if="stockFormData.type === 'in'">
          <el-input v-model="stockFormData.supplier" placeholder="请输入供应商" />
        </el-form-item>
        
        <el-form-item label="用途" prop="purpose" v-if="stockFormData.type === 'out'">
          <el-input v-model="stockFormData.purpose" placeholder="请输入用途" />
        </el-form-item>
        
        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="stockFormData.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
    </form-dialog>
  </page-container>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getMaterials, createMaterial, updateMaterial, deleteMaterial, updateMaterialStock } from '@/api/materials'
import { formatNumber, formatDateTime } from '@/utils'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const stockSubmitLoading = ref(false)
const tableData = ref([])
const dialogVisible = ref(false)
const stockDialogVisible = ref(false)
const dialogTitle = ref('')
const currentRow = ref(null)
const currentMaterial = ref(null)
const formRef = ref()
const stockFormRef = ref()

// 搜索表单
const searchForm = reactive({
  name: '',
  code: '',
  category: '',
  stockStatus: '',
  status: ''
})

// 分页数据
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 表单数据
const formData = reactive({
  code: '',
  name: '',
  category: '',
  subcategory: '',
  specification: '',
  unit: '',
  inventory: {
    currentStock: 0,
    minStock: 0,
    safetyStock: 0
  },
  pricing: {
    purchasePrice: 0,
    averagePrice: 0,
    lastPrice: 0
  },
  suppliers: [],
  status: 'active',
  notes: ''
})

// 库存表单数据
const stockFormData = reactive({
  type: 'in',
  quantity: null,
  unitPrice: null,
  supplier: '',
  purpose: '',
  notes: ''
})

// 表单验证规则
const formRules = {
  code: [
    { required: true, message: '请输入物资编码', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入物资名称', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择物资类别', trigger: 'change' }
  ],
  unit: [
    { required: true, message: '请选择单位', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 库存表单验证规则
const stockFormRules = {
  type: [
    { required: true, message: '请选择操作类型', trigger: 'change' }
  ],
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '数量必须大于0', trigger: 'blur' }
  ]
}

// 方法
const loadData = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.current,
      limit: pagination.pageSize,
      ...searchForm
    }
    
    const response = await getMaterials(params)
    tableData.value = response.data.materials
    pagination.total = response.data.pagination.total
  } catch (error) {
    console.error('加载物资数据失败:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  handleSearch()
}

const handlePageChange = (page) => {
  pagination.current = page
  loadData()
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.current = 1
  loadData()
}

const handleAdd = () => {
  dialogTitle.value = '新增物资'
  currentRow.value = null
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  dialogTitle.value = '编辑物资'
  currentRow.value = row
  Object.keys(formData).forEach(key => {
    if (key === 'inventory' || key === 'pricing') {
      formData[key] = { ...formData[key], ...row[key] }
    } else if (key === 'suppliers') {
      formData[key] = row[key] ? [...row[key]] : []
    } else {
      formData[key] = row[key] || ''
    }
  })
  dialogVisible.value = true
}

const handleView = (row) => {
  router.push(`/materials/${row._id}`)
}

const handleStock = (row) => {
  currentMaterial.value = row
  resetStockForm()
  stockDialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除物资 "${row.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteMaterial(row._id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除物资失败:', error)
    }
  }
}

const handleImport = () => {
  ElMessage.info('导入功能开发中...')
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    if (currentRow.value) {
      await updateMaterial(currentRow.value._id, formData)
      ElMessage.success('更新成功')
    } else {
      await createMaterial(formData)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    loadData()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}

const handleCancel = () => {
  dialogVisible.value = false
}

const handleStockSubmit = async () => {
  try {
    await stockFormRef.value.validate()
    stockSubmitLoading.value = true
    
    await updateMaterialStock(currentMaterial.value._id, stockFormData)
    ElMessage.success('库存更新成功')
    
    stockDialogVisible.value = false
    loadData()
  } catch (error) {
    console.error('库存更新失败:', error)
  } finally {
    stockSubmitLoading.value = false
  }
}

const handleStockCancel = () => {
  stockDialogVisible.value = false
}

const addSupplier = () => {
  formData.suppliers.push({
    name: '',
    contact: '',
    phone: '',
    price: 0
  })
}

const removeSupplier = (index) => {
  formData.suppliers.splice(index, 1)
}

const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'status') {
      formData[key] = 'active'
    } else if (key === 'inventory') {
      formData[key] = {
        currentStock: 0,
        minStock: 0,
        safetyStock: 0
      }
    } else if (key === 'pricing') {
      formData[key] = {
        purchasePrice: 0,
        averagePrice: 0,
        lastPrice: 0
      }
    } else if (key === 'suppliers') {
      formData[key] = []
    } else {
      formData[key] = ''
    }
  })
  formRef.value?.clearValidate()
}

const resetStockForm = () => {
  Object.keys(stockFormData).forEach(key => {
    if (key === 'type') {
      stockFormData[key] = 'in'
    } else if (typeof stockFormData[key] === 'number') {
      stockFormData[key] = null
    } else {
      stockFormData[key] = ''
    }
  })
  stockFormRef.value?.clearValidate()
}

// 工具方法
const getStockStatus = (row) => {
  const current = row.inventory?.currentStock || 0
  const min = row.inventory?.minStock || 0
  const safety = row.inventory?.safetyStock || 0
  
  if (current === 0) return 'empty'
  if (current <= min) return 'low'
  if (current <= safety) return 'low'
  return 'sufficient'
}

const getStockClass = (row) => {
  const status = getStockStatus(row)
  return {
    'text-danger': status === 'empty',
    'text-warning': status === 'low',
    'text-success': status === 'sufficient'
  }
}

const calculateStockValue = (row) => {
  const stock = row.inventory?.currentStock || 0
  const price = row.pricing?.averagePrice || 0
  return formatNumber(stock * price, 2)
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.text-muted {
  color: #909399;
}

.text-success {
  color: #67c23a;
}

.text-warning {
  color: #e6a23c;
}

.text-danger {
  color: #f56c6c;
}

.supplier-item {
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fafafa;
}
</style>
