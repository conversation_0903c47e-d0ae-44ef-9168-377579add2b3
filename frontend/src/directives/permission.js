import { useUserStore } from '@/stores/user'

/**
 * 权限指令
 * 使用方法：
 * v-permission="'user_manage'"
 * v-permission="['user_manage', 'user_view']"
 * v-permission:any="['user_manage', 'user_view']" // 满足任一权限即可
 * v-permission:all="['user_manage', 'user_view']" // 需要满足所有权限
 */
export default {
  mounted(el, binding) {
    checkPermission(el, binding)
  },
  updated(el, binding) {
    checkPermission(el, binding)
  }
}

function checkPermission(el, binding) {
  const { value, arg } = binding
  const userStore = useUserStore()
  
  if (!value) {
    return
  }
  
  let hasPermission = false
  
  if (Array.isArray(value)) {
    if (arg === 'all') {
      // 需要满足所有权限
      hasPermission = userStore.hasAllPermissions(value)
    } else {
      // 默认满足任一权限即可
      hasPermission = userStore.hasAnyPermission(value)
    }
  } else {
    // 单个权限
    hasPermission = userStore.hasPermission(value)
  }
  
  if (!hasPermission) {
    // 移除元素
    el.parentNode && el.parentNode.removeChild(el)
  }
}
