import { ElMessage } from 'element-plus'

/**
 * 复制指令
 * 使用方法：
 * v-copy="'要复制的文本'"
 * v-copy:click="copyText"
 */
export default {
  mounted(el, binding) {
    setupCopy(el, binding)
  },
  updated(el, binding) {
    setupCopy(el, binding)
  },
  unmounted(el) {
    if (el.copyHandler) {
      el.removeEventListener('click', el.copyHandler)
    }
  }
}

function setupCopy(el, binding) {
  const { value, arg } = binding
  const event = arg || 'click'
  
  // 移除之前的事件监听器
  if (el.copyHandler) {
    el.removeEventListener(event, el.copyHandler)
  }
  
  // 创建复制处理函数
  el.copyHandler = async function() {
    let textToCopy = value
    
    // 如果值是函数，则调用函数获取文本
    if (typeof value === 'function') {
      textToCopy = value()
    }
    
    if (!textToCopy) {
      ElMessage.warning('没有可复制的内容')
      return
    }
    
    try {
      // 使用现代 Clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(textToCopy)
        ElMessage.success('复制成功')
      } else {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = textToCopy
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()
        
        const successful = document.execCommand('copy')
        document.body.removeChild(textArea)
        
        if (successful) {
          ElMessage.success('复制成功')
        } else {
          ElMessage.error('复制失败')
        }
      }
    } catch (error) {
      console.error('复制失败:', error)
      ElMessage.error('复制失败')
    }
  }
  
  // 添加事件监听器
  el.addEventListener(event, el.copyHandler)
  
  // 添加样式提示
  el.style.cursor = 'pointer'
  el.title = '点击复制'
}
