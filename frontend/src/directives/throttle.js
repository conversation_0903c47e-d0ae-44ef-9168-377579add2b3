/**
 * 节流指令
 * 使用方法：
 * v-throttle:click="handleClick"
 * v-throttle:click.1000="handleClick" // 自定义间隔时间
 */
export default {
  mounted(el, binding) {
    setupThrottle(el, binding)
  },
  updated(el, binding) {
    setupThrottle(el, binding)
  },
  unmounted(el) {
    if (el.throttleHandler) {
      el.removeEventListener(el.throttleEvent, el.throttleHandler)
    }
  }
}

function setupThrottle(el, binding) {
  const { value, arg, modifiers } = binding
  
  if (typeof value !== 'function') {
    console.warn('v-throttle 指令的值必须是一个函数')
    return
  }
  
  // 获取间隔时间，默认1000ms
  const interval = Object.keys(modifiers)[0] || 1000
  const event = arg || 'click'
  
  // 移除之前的事件监听器
  if (el.throttleHandler) {
    el.removeEventListener(el.throttleEvent, el.throttleHandler)
  }
  
  let lastTime = 0
  
  // 创建节流处理函数
  el.throttleHandler = function(...args) {
    const now = Date.now()
    
    if (now - lastTime >= interval) {
      lastTime = now
      value.apply(this, args)
    }
  }
  
  el.throttleEvent = event
  
  // 添加事件监听器
  el.addEventListener(event, el.throttleHandler)
}
