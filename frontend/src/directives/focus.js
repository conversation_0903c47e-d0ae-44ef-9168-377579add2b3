/**
 * 自动聚焦指令
 * 使用方法：
 * v-focus
 * v-focus.select // 聚焦并选中文本
 */
export default {
  mounted(el, binding) {
    const { modifiers } = binding
    
    // 延迟执行，确保DOM已渲染
    setTimeout(() => {
      // 如果是input或textarea元素
      if (el.tagName === 'INPUT' || el.tagName === 'TEXTAREA') {
        el.focus()
        
        // 如果有select修饰符，选中所有文本
        if (modifiers.select) {
          el.select()
        }
      } else {
        // 查找子元素中的input或textarea
        const input = el.querySelector('input, textarea')
        if (input) {
          input.focus()
          
          if (modifiers.select) {
            input.select()
          }
        }
      }
    }, 100)
  }
}
