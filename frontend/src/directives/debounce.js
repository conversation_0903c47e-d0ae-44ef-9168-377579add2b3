/**
 * 防抖指令
 * 使用方法：
 * v-debounce:click="handleClick"
 * v-debounce:click.500="handleClick" // 自定义延迟时间
 */
export default {
  mounted(el, binding) {
    setupDebounce(el, binding)
  },
  updated(el, binding) {
    setupDebounce(el, binding)
  },
  unmounted(el) {
    if (el.debounceTimer) {
      clearTimeout(el.debounceTimer)
    }
  }
}

function setupDebounce(el, binding) {
  const { value, arg, modifiers } = binding
  
  if (typeof value !== 'function') {
    console.warn('v-debounce 指令的值必须是一个函数')
    return
  }
  
  // 获取延迟时间，默认300ms
  const delay = Object.keys(modifiers)[0] || 300
  const event = arg || 'click'
  
  // 移除之前的事件监听器
  if (el.debounceHandler) {
    el.removeEventListener(event, el.debounceHandler)
  }
  
  // 创建防抖处理函数
  el.debounceHandler = function(...args) {
    if (el.debounceTimer) {
      clearTimeout(el.debounceTimer)
    }
    
    el.debounceTimer = setTimeout(() => {
      value.apply(this, args)
    }, delay)
  }
  
  // 添加事件监听器
  el.addEventListener(event, el.debounceHandler)
}
