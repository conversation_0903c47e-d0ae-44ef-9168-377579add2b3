import { ElLoading } from 'element-plus'

/**
 * 加载指令
 * 使用方法：
 * v-loading="loading"
 * v-loading.fullscreen="loading"
 * v-loading.body="loading"
 */
export default {
  mounted(el, binding) {
    updateLoading(el, binding)
  },
  updated(el, binding) {
    updateLoading(el, binding)
  },
  unmounted(el) {
    if (el.loadingInstance) {
      el.loadingInstance.close()
    }
  }
}

function updateLoading(el, binding) {
  const { value, modifiers } = binding
  
  if (value) {
    // 显示加载
    if (!el.loadingInstance) {
      const options = {
        target: el,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)'
      }
      
      if (modifiers.fullscreen) {
        options.target = document.body
        options.fullscreen = true
      } else if (modifiers.body) {
        options.target = document.body
      }
      
      el.loadingInstance = ElLoading.service(options)
    }
  } else {
    // 隐藏加载
    if (el.loadingInstance) {
      el.loadingInstance.close()
      el.loadingInstance = null
    }
  }
}
