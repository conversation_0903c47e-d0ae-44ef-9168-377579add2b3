<template>
  <div class="navbar">
    <div class="navbar-left">
      <!-- 侧边栏切换按钮 -->
      <el-button
        type="text"
        class="sidebar-toggle"
        @click="toggleSidebar"
      >
        <el-icon><Expand v-if="!sidebarOpened" /><Fold v-else /></el-icon>
      </el-button>
      
      <!-- 面包屑导航 -->
      <el-breadcrumb separator="/" class="breadcrumb">
        <el-breadcrumb-item
          v-for="item in breadcrumbs"
          :key="item.path"
          :to="item.path"
        >
          {{ item.title }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <div class="navbar-right">
      <!-- 全屏按钮 -->
      <el-tooltip content="全屏" placement="bottom">
        <el-button
          type="text"
          class="navbar-btn"
          @click="toggleFullscreen"
        >
          <el-icon><FullScreen /></el-icon>
        </el-button>
      </el-tooltip>

      <!-- 主题切换 -->
      <el-tooltip content="主题切换" placement="bottom">
        <el-button
          type="text"
          class="navbar-btn"
          @click="toggleTheme"
        >
          <el-icon><Sunny v-if="theme === 'light'" /><Moon v-else /></el-icon>
        </el-button>
      </el-tooltip>

      <!-- 消息通知 -->
      <el-dropdown trigger="click" @command="handleNotificationCommand">
        <el-button type="text" class="navbar-btn">
          <el-badge :value="unreadCount" :hidden="unreadCount === 0">
            <el-icon><Bell /></el-icon>
          </el-badge>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <div class="notification-header">
              <span>通知消息</span>
              <el-button type="text" size="small" @click="markAllAsRead">
                全部已读
              </el-button>
            </div>
            <el-scrollbar max-height="300px">
              <div v-if="notifications.length === 0" class="no-notifications">
                暂无通知
              </div>
              <div
                v-for="notification in notifications"
                :key="notification.id"
                class="notification-item"
                :class="{ unread: !notification.read }"
                @click="handleNotificationClick(notification)"
              >
                <div class="notification-content">
                  <div class="notification-title">{{ notification.title }}</div>
                  <div class="notification-message">{{ notification.message }}</div>
                  <div class="notification-time">{{ formatTime(notification.time) }}</div>
                </div>
              </div>
            </el-scrollbar>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <!-- 用户菜单 -->
      <el-dropdown trigger="click" @command="handleUserCommand">
        <div class="user-info">
          <el-avatar :size="32" :src="userAvatar">
            <el-icon><User /></el-icon>
          </el-avatar>
          <span class="username">{{ userName }}</span>
          <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              个人中心
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              系统设置
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { ElMessageBox, ElMessage } from 'element-plus'
import { formatDate } from '@/utils'

const router = useRouter()
const route = useRoute()
const appStore = useAppStore()
const userStore = useUserStore()

// 响应式数据
const notifications = ref([
  {
    id: 1,
    title: '库存预警',
    message: '复合肥库存不足，请及时补充',
    time: new Date(),
    read: false,
    type: 'warning'
  },
  {
    id: 2,
    title: '收获提醒',
    message: '地块A001的玉米预计3天后收获',
    time: new Date(Date.now() - 2 * 60 * 60 * 1000),
    read: false,
    type: 'info'
  }
])

// 计算属性
const sidebarOpened = computed(() => appStore.sidebarOpened)
const theme = computed(() => appStore.currentTheme)
const userName = computed(() => userStore.userName)
const userAvatar = computed(() => userStore.userAvatar)

const unreadCount = computed(() => {
  return notifications.value.filter(n => !n.read).length
})

const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  return matched.map(item => ({
    title: item.meta.title,
    path: item.path
  }))
})

// 方法
const toggleSidebar = () => {
  appStore.toggleSidebar()
}

const toggleTheme = () => {
  appStore.toggleTheme()
}

const toggleFullscreen = () => {
  if (document.fullscreenElement) {
    document.exitFullscreen()
  } else {
    document.documentElement.requestFullscreen()
  }
}

const handleUserCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      handleLogout()
      break
  }
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await userStore.logout()
    router.push('/login')
  } catch (error) {
    // 用户取消
  }
}

const handleNotificationCommand = (command) => {
  // 处理通知相关命令
}

const handleNotificationClick = (notification) => {
  notification.read = true
  // 根据通知类型跳转到相应页面
  switch (notification.type) {
    case 'warning':
      router.push('/materials')
      break
    case 'info':
      router.push('/crops')
      break
  }
}

const markAllAsRead = () => {
  notifications.value.forEach(n => n.read = true)
  ElMessage.success('已标记全部为已读')
}

const formatTime = (time) => {
  const now = new Date()
  const diff = now - time
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  return formatDate(time, 'MM-DD HH:mm')
}

onMounted(() => {
  // 可以在这里加载通知数据
})
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.navbar-left {
  display: flex;
  align-items: center;
  
  .sidebar-toggle {
    margin-right: 16px;
    font-size: 18px;
  }
  
  .breadcrumb {
    font-size: 14px;
  }
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .navbar-btn {
    padding: 8px;
    font-size: 16px;
    
    &:hover {
      background-color: #f5f7fa;
    }
  }
  
  .user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
    
    &:hover {
      background-color: #f5f7fa;
    }
    
    .username {
      font-size: 14px;
      color: #606266;
    }
    
    .dropdown-icon {
      font-size: 12px;
      color: #909399;
    }
  }
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  font-weight: 500;
}

.no-notifications {
  padding: 20px;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

.notification-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f5f7fa;
  cursor: pointer;
  transition: background-color 0.3s;
  
  &:hover {
    background-color: #f5f7fa;
  }
  
  &.unread {
    background-color: #f0f9ff;
    
    .notification-title {
      font-weight: 500;
    }
  }
  
  .notification-content {
    .notification-title {
      font-size: 14px;
      color: #303133;
      margin-bottom: 4px;
    }
    
    .notification-message {
      font-size: 12px;
      color: #606266;
      margin-bottom: 4px;
    }
    
    .notification-time {
      font-size: 12px;
      color: #909399;
    }
  }
}

// 暗色主题
.dark {
  .navbar {
    background: #1f1f1f;
    border-bottom-color: #303030;
    
    .navbar-btn:hover {
      background-color: #2a2a2a;
    }
    
    .user-info:hover {
      background-color: #2a2a2a;
    }
  }
}
</style>
