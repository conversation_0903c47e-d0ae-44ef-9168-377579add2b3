<template>
  <div class="sidebar-wrapper">
    <!-- Logo区域 -->
    <div class="sidebar-logo">
      <router-link to="/dashboard" class="logo-link">
        <img src="/logo.png" alt="Logo" class="logo-image" />
        <h1 v-show="sidebarOpened" class="logo-title">农场管理</h1>
      </router-link>
    </div>

    <!-- 菜单区域 -->
    <el-scrollbar class="sidebar-scrollbar">
      <el-menu
        :default-active="activeMenu"
        :collapse="!sidebarOpened"
        :unique-opened="true"
        :collapse-transition="false"
        mode="vertical"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409eff"
        @select="handleMenuSelect"
      >
        <sidebar-item
          v-for="route in menuRoutes"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import SidebarItem from './SidebarItem.vue'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()

// 计算属性
const sidebarOpened = computed(() => appStore.sidebarOpened)

const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta?.activeMenu) {
    return meta.activeMenu
  }
  return path
})

const menuRoutes = computed(() => {
  // 从路由配置中获取菜单项
  const routes = router.getRoutes()
  const layoutRoute = routes.find(r => r.name === 'Layout')
  
  if (!layoutRoute || !layoutRoute.children) {
    return []
  }
  
  return layoutRoute.children.filter(route => {
    // 过滤掉隐藏的菜单项
    if (route.meta?.hideInMenu) {
      return false
    }
    
    // 权限检查
    if (route.meta?.permission && !userStore.hasPermission(route.meta.permission)) {
      return false
    }
    
    // 角色检查
    if (route.meta?.roles && !userStore.hasRole(route.meta.roles)) {
      return false
    }
    
    return true
  })
})

// 方法
const handleMenuSelect = (index) => {
  if (index !== route.path) {
    router.push(index)
  }
}
</script>

<style lang="scss" scoped>
.sidebar-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sidebar-logo {
  height: 50px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  background: #2b2f3a;
  border-bottom: 1px solid #1f2329;
  
  .logo-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    width: 100%;
    
    .logo-image {
      width: 32px;
      height: 32px;
      margin-right: 12px;
    }
    
    .logo-title {
      font-size: 18px;
      font-weight: 600;
      color: #fff;
      margin: 0;
      white-space: nowrap;
    }
  }
}

.sidebar-scrollbar {
  flex: 1;
  
  :deep(.el-scrollbar__view) {
    height: 100%;
  }
}

:deep(.el-menu) {
  border-right: none;
  height: 100%;
  
  .el-menu-item {
    height: 48px;
    line-height: 48px;
    
    &.is-active {
      background-color: #409eff !important;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background: #fff;
      }
    }
    
    &:hover {
      background-color: #263445 !important;
    }
  }
  
  .el-submenu {
    .el-submenu__title {
      height: 48px;
      line-height: 48px;
      
      &:hover {
        background-color: #263445 !important;
      }
    }
  }
  
  // 折叠状态下的样式
  &.el-menu--collapse {
    .el-menu-item,
    .el-submenu__title {
      padding: 0 20px !important;
      text-align: center;
      
      .el-icon {
        margin-right: 0;
      }
      
      span,
      .el-submenu__icon-arrow {
        display: none;
      }
    }
    
    .el-submenu {
      .el-menu {
        background-color: #1f2329 !important;
        
        .el-menu-item {
          padding-left: 40px !important;
          text-align: left;
          
          span {
            display: inline;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .sidebar-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1001;
    width: 210px;
    height: 100vh;
    background: #304156;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    
    &.opened {
      transform: translateX(0);
    }
  }
}
</style>
