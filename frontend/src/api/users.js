import request from '@/utils/request'

// 获取用户列表
export function getUsers(params) {
  return request({
    url: '/users',
    method: 'get',
    params
  })
}

// 获取用户详情
export function getUserById(id) {
  return request({
    url: `/users/${id}`,
    method: 'get'
  })
}

// 创建用户
export function createUser(data) {
  return request({
    url: '/users',
    method: 'post',
    data
  })
}

// 更新用户
export function updateUser(id, data) {
  return request({
    url: `/users/${id}`,
    method: 'put',
    data
  })
}

// 删除用户
export function deleteUser(id) {
  return request({
    url: `/users/${id}`,
    method: 'delete'
  })
}

// 重置用户密码
export function resetPassword(id, data) {
  return request({
    url: `/users/${id}/reset-password`,
    method: 'put',
    data
  })
}

// 切换用户状态
export function toggleUserStatus(id, data) {
  return request({
    url: `/users/${id}/status`,
    method: 'put',
    data
  })
}

// 获取用户权限
export function getUserPermissions(id) {
  return request({
    url: `/users/${id}/permissions`,
    method: 'get'
  })
}

// 更新用户权限
export function updateUserPermissions(id, data) {
  return request({
    url: `/users/${id}/permissions`,
    method: 'put',
    data
  })
}

// 获取用户统计
export function getUserStats() {
  return request({
    url: '/users/stats',
    method: 'get'
  })
}
