<template>
  <div class="file-upload">
    <el-upload
      ref="uploadRef"
      :action="uploadUrl"
      :headers="uploadHeaders"
      :data="uploadData"
      :multiple="multiple"
      :accept="accept"
      :limit="limit"
      :file-list="fileList"
      :auto-upload="autoUpload"
      :show-file-list="showFileList"
      :list-type="listType"
      :drag="drag"
      :disabled="disabled"
      :before-upload="handleBeforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-progress="handleProgress"
      :on-change="handleChange"
      :on-remove="handleRemove"
      :on-preview="handlePreview"
      :on-exceed="handleExceed"
      class="upload-component"
    >
      <template v-if="drag">
        <div class="upload-dragger">
          <el-icon class="upload-icon">
            <UploadFilled />
          </el-icon>
          <div class="upload-text">
            <p>{{ dragText || '将文件拖到此处，或点击上传' }}</p>
            <p class="upload-tip">{{ tip }}</p>
          </div>
        </div>
      </template>
      
      <template v-else>
        <el-button v-if="listType !== 'picture-card'" type="primary" :loading="uploading">
          <el-icon><Upload /></el-icon>
          {{ buttonText || '选择文件' }}
        </el-button>
        <el-icon v-else class="avatar-uploader-icon">
          <Plus />
        </el-icon>
      </template>
      
      <template #tip v-if="tip && !drag">
        <div class="el-upload__tip">{{ tip }}</div>
      </template>
    </el-upload>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="previewVisible" title="图片预览" width="800px" append-to-body>
      <img :src="previewUrl" alt="预览图片" style="width: 100%; height: auto;" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'

const props = defineProps({
  // 上传地址
  action: {
    type: String,
    default: '/api/upload/single'
  },
  // 文件列表
  modelValue: {
    type: Array,
    default: () => []
  },
  // 是否支持多选
  multiple: {
    type: Boolean,
    default: false
  },
  // 接受的文件类型
  accept: {
    type: String,
    default: ''
  },
  // 最大上传数量
  limit: {
    type: Number,
    default: 1
  },
  // 是否自动上传
  autoUpload: {
    type: Boolean,
    default: true
  },
  // 是否显示文件列表
  showFileList: {
    type: Boolean,
    default: true
  },
  // 列表类型
  listType: {
    type: String,
    default: 'text' // text, picture, picture-card
  },
  // 是否启用拖拽上传
  drag: {
    type: Boolean,
    default: false
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 按钮文字
  buttonText: {
    type: String,
    default: ''
  },
  // 拖拽区域文字
  dragText: {
    type: String,
    default: ''
  },
  // 提示文字
  tip: {
    type: String,
    default: ''
  },
  // 文件大小限制（MB）
  maxSize: {
    type: Number,
    default: 10
  },
  // 上传前处理
  beforeUpload: {
    type: Function,
    default: null
  },
  // 额外的上传数据
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits([
  'update:modelValue',
  'success',
  'error',
  'progress',
  'change',
  'remove',
  'preview',
  'exceed'
])

const userStore = useUserStore()
const uploadRef = ref()
const uploading = ref(false)
const previewVisible = ref(false)
const previewUrl = ref('')

// 计算属性
const uploadUrl = computed(() => {
  const baseUrl = import.meta.env.VITE_API_BASE_URL || ''
  return baseUrl + props.action
})

const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${userStore.token}`
}))

const uploadData = computed(() => ({
  ...props.data
}))

const fileList = computed({
  get: () => props.modelValue || [],
  set: (value) => emit('update:modelValue', value)
})

// 方法
const handleBeforeUpload = (file) => {
  // 文件大小检查
  if (file.size > props.maxSize * 1024 * 1024) {
    ElMessage.error(`文件大小不能超过 ${props.maxSize}MB`)
    return false
  }

  // 自定义上传前处理
  if (props.beforeUpload) {
    return props.beforeUpload(file)
  }

  uploading.value = true
  return true
}

const handleSuccess = (response, file, fileList) => {
  uploading.value = false
  
  if (response.success) {
    ElMessage.success('上传成功')
    
    // 更新文件列表
    const newFileList = fileList.map(item => {
      if (item.uid === file.uid) {
        return {
          ...item,
          url: response.data.url,
          response: response.data
        }
      }
      return item
    })
    
    emit('update:modelValue', newFileList)
    emit('success', response, file, newFileList)
  } else {
    ElMessage.error(response.message || '上传失败')
    emit('error', response, file, fileList)
  }
}

const handleError = (error, file, fileList) => {
  uploading.value = false
  console.error('上传失败:', error)
  
  let message = '上传失败'
  if (error.response && error.response.data && error.response.data.message) {
    message = error.response.data.message
  }
  
  ElMessage.error(message)
  emit('error', error, file, fileList)
}

const handleProgress = (event, file, fileList) => {
  emit('progress', event, file, fileList)
}

const handleChange = (file, fileList) => {
  emit('update:modelValue', fileList)
  emit('change', file, fileList)
}

const handleRemove = async (file, fileList) => {
  try {
    // 如果文件已上传，询问是否删除服务器文件
    if (file.response && file.response.filename) {
      await ElMessageBox.confirm(
        '确定要删除这个文件吗？',
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      
      // 这里可以调用删除API
      // await deleteFile(file.response.filename)
    }
    
    emit('update:modelValue', fileList)
    emit('remove', file, fileList)
  } catch (error) {
    // 用户取消删除
  }
}

const handlePreview = (file) => {
  if (file.url && isImageFile(file)) {
    previewUrl.value = file.url
    previewVisible.value = true
  } else if (file.response && file.response.url && isImageFile(file)) {
    previewUrl.value = file.response.url
    previewVisible.value = true
  } else {
    // 非图片文件，下载
    if (file.url) {
      window.open(file.url, '_blank')
    }
  }
  
  emit('preview', file)
}

const handleExceed = (files, fileList) => {
  ElMessage.warning(`最多只能上传 ${props.limit} 个文件`)
  emit('exceed', files, fileList)
}

// 工具方法
const isImageFile = (file) => {
  const imageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
  return imageTypes.includes(file.type || file.raw?.type)
}

// 暴露方法
const submit = () => {
  uploadRef.value?.submit()
}

const abort = () => {
  uploadRef.value?.abort()
}

const clearFiles = () => {
  uploadRef.value?.clearFiles()
  emit('update:modelValue', [])
}

defineExpose({
  submit,
  abort,
  clearFiles
})
</script>

<style lang="scss" scoped>
.file-upload {
  width: 100%;
  
  .upload-component {
    width: 100%;
  }
  
  .upload-dragger {
    padding: 40px;
    text-align: center;
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    transition: border-color 0.3s;
    
    &:hover {
      border-color: #409eff;
    }
    
    .upload-icon {
      font-size: 48px;
      color: #c0c4cc;
      margin-bottom: 16px;
    }
    
    .upload-text {
      color: #606266;
      
      p {
        margin: 0;
        line-height: 1.5;
      }
      
      .upload-tip {
        font-size: 12px;
        color: #909399;
        margin-top: 8px;
      }
    }
  }
  
  :deep(.el-upload) {
    .el-upload-dragger {
      width: 100%;
    }
    
    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 148px;
      height: 148px;
      line-height: 148px;
      text-align: center;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      transition: border-color 0.3s;
      
      &:hover {
        border-color: #409eff;
      }
    }
  }
  
  :deep(.el-upload-list) {
    .el-upload-list__item {
      transition: all 0.3s;
      
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }
}
</style>
