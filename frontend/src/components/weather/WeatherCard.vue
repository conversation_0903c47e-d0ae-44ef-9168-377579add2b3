<template>
  <el-card class="weather-card" shadow="hover">
    <template #header>
      <div class="weather-header">
        <span class="weather-title">
          <el-icon><Sunny /></el-icon>
          天气信息
        </span>
        <el-button 
          type="text" 
          @click="refreshWeather" 
          :loading="loading"
          size="small"
        >
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </template>

    <div v-if="loading" class="weather-loading">
      <el-skeleton :rows="3" animated />
    </div>

    <div v-else-if="weatherData" class="weather-content">
      <!-- 当前天气 -->
      <div class="current-weather">
        <div class="weather-main">
          <div class="weather-icon">
            <img :src="getWeatherIcon(weatherData.weather?.icon)" :alt="weatherData.weather?.description" />
          </div>
          <div class="weather-info">
            <div class="temperature">{{ weatherData.temperature?.current }}°C</div>
            <div class="condition">{{ weatherData.weather?.description }}</div>
            <div class="location">{{ weatherData.location?.name }}</div>
          </div>
        </div>
        
        <div class="weather-details">
          <div class="detail-item">
            <el-icon><View /></el-icon>
            <span>体感 {{ weatherData.temperature?.feelsLike }}°C</span>
          </div>
          <div class="detail-item">
            <el-icon><Drizzling /></el-icon>
            <span>湿度 {{ weatherData.humidity }}%</span>
          </div>
          <div class="detail-item">
            <el-icon><WindPower /></el-icon>
            <span>风速 {{ weatherData.wind?.speed }}m/s</span>
          </div>
          <div class="detail-item">
            <el-icon><View /></el-icon>
            <span>能见度 {{ weatherData.visibility }}km</span>
          </div>
        </div>
      </div>

      <!-- 农业指数 -->
      <div v-if="showAgriculturalIndex" class="agricultural-index">
        <el-divider content-position="left">农业指数</el-divider>
        <div class="index-grid">
          <div class="index-item">
            <div class="index-label">适宜度</div>
            <el-progress 
              :percentage="agriculturalIndex.suitability || 0" 
              :color="getIndexColor(agriculturalIndex.suitability)"
              :show-text="false"
            />
            <div class="index-value">{{ agriculturalIndex.suitability || 0 }}%</div>
          </div>
          <div class="index-item">
            <div class="index-label">灌溉建议</div>
            <el-progress 
              :percentage="agriculturalIndex.irrigation || 0" 
              :color="getIndexColor(agriculturalIndex.irrigation)"
              :show-text="false"
            />
            <div class="index-value">{{ agriculturalIndex.irrigation || 0 }}%</div>
          </div>
          <div class="index-item">
            <div class="index-label">病虫害风险</div>
            <el-progress 
              :percentage="agriculturalIndex.pestRisk || 0" 
              :color="getRiskColor(agriculturalIndex.pestRisk)"
              :show-text="false"
            />
            <div class="index-value">{{ agriculturalIndex.pestRisk || 0 }}%</div>
          </div>
          <div class="index-item">
            <div class="index-label">作业适宜度</div>
            <el-progress 
              :percentage="agriculturalIndex.workSuitability || 0" 
              :color="getIndexColor(agriculturalIndex.workSuitability)"
              :show-text="false"
            />
            <div class="index-value">{{ agriculturalIndex.workSuitability || 0 }}%</div>
          </div>
        </div>
      </div>

      <!-- 建议信息 -->
      <div v-if="recommendations" class="recommendations">
        <el-divider content-position="left">农事建议</el-divider>
        <div class="recommendation-list">
          <div class="recommendation-item">
            <el-icon class="rec-icon"><Drizzling /></el-icon>
            <span>{{ recommendations.irrigation }}</span>
          </div>
          <div class="recommendation-item">
            <el-icon class="rec-icon"><Tools /></el-icon>
            <span>{{ recommendations.farming }}</span>
          </div>
          <div class="recommendation-item">
            <el-icon class="rec-icon"><Warning /></el-icon>
            <span>{{ recommendations.pestControl }}</span>
          </div>
        </div>
      </div>

      <!-- 更新时间 -->
      <div class="weather-footer">
        <span class="update-time">
          更新时间: {{ formatDateTime(weatherData.timestamp) }}
        </span>
      </div>
    </div>

    <div v-else class="weather-error">
      <empty-state 
        title="获取天气失败" 
        description="无法获取天气信息，请稍后重试"
        :show-action="true"
        action-text="重试"
        @action="refreshWeather"
      />
    </div>
  </el-card>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { getCurrentWeather, getAgriculturalIndex } from '@/api/weather'
import { formatDateTime } from '@/utils'

const props = defineProps({
  location: {
    type: Object,
    default: () => ({
      lat: 39.9042,
      lon: 116.4074,
      name: '农场位置'
    })
  },
  showAgriculturalIndex: {
    type: Boolean,
    default: true
  },
  autoRefresh: {
    type: Boolean,
    default: false
  },
  refreshInterval: {
    type: Number,
    default: 300000 // 5分钟
  }
})

const emit = defineEmits(['weather-update'])

// 响应式数据
const loading = ref(false)
const weatherData = ref(null)
const agriculturalIndex = ref({})
const recommendations = ref(null)
let refreshTimer = null

// 计算属性
const currentLocation = computed(() => ({
  lat: props.location.lat || 39.9042,
  lon: props.location.lon || 116.4074,
  name: props.location.name || '农场位置'
}))

// 方法
const loadWeatherData = async () => {
  try {
    loading.value = true
    
    // 并行获取天气和农业指数
    const [weatherRes, indexRes] = await Promise.all([
      getCurrentWeather(currentLocation.value),
      props.showAgriculturalIndex ? getAgriculturalIndex(currentLocation.value) : Promise.resolve(null)
    ])
    
    weatherData.value = weatherRes.data
    
    if (indexRes) {
      agriculturalIndex.value = indexRes.data.indices || {}
      recommendations.value = indexRes.data.recommendations || null
    }
    
    emit('weather-update', weatherData.value)
    
  } catch (error) {
    console.error('获取天气数据失败:', error)
    weatherData.value = null
  } finally {
    loading.value = false
  }
}

const refreshWeather = () => {
  loadWeatherData()
}

const getWeatherIcon = (icon) => {
  if (!icon) return '/images/weather/default.png'
  
  // 天气图标映射
  const iconMap = {
    '01d': '/images/weather/sunny.png',
    '01n': '/images/weather/clear-night.png',
    '02d': '/images/weather/partly-cloudy.png',
    '02n': '/images/weather/partly-cloudy-night.png',
    '03d': '/images/weather/cloudy.png',
    '03n': '/images/weather/cloudy.png',
    '04d': '/images/weather/overcast.png',
    '04n': '/images/weather/overcast.png',
    '09d': '/images/weather/rain.png',
    '09n': '/images/weather/rain.png',
    '10d': '/images/weather/rain.png',
    '10n': '/images/weather/rain.png',
    '11d': '/images/weather/thunderstorm.png',
    '11n': '/images/weather/thunderstorm.png',
    '13d': '/images/weather/snow.png',
    '13n': '/images/weather/snow.png',
    '50d': '/images/weather/fog.png',
    '50n': '/images/weather/fog.png'
  }
  
  return iconMap[icon] || `/images/weather/${icon}.png`
}

const getIndexColor = (value) => {
  if (value >= 80) return '#67c23a'
  if (value >= 60) return '#e6a23c'
  if (value >= 40) return '#f56c6c'
  return '#909399'
}

const getRiskColor = (value) => {
  if (value >= 80) return '#f56c6c'
  if (value >= 60) return '#e6a23c'
  if (value >= 40) return '#67c23a'
  return '#909399'
}

// 自动刷新
const startAutoRefresh = () => {
  if (props.autoRefresh && props.refreshInterval > 0) {
    refreshTimer = setInterval(() => {
      loadWeatherData()
    }, props.refreshInterval)
  }
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 生命周期
onMounted(() => {
  loadWeatherData()
  startAutoRefresh()
})

// 清理定时器
const { onUnmounted } = require('vue')
onUnmounted(() => {
  stopAutoRefresh()
})

// 暴露方法
defineExpose({
  refresh: refreshWeather,
  startAutoRefresh,
  stopAutoRefresh
})
</script>

<style lang="scss" scoped>
.weather-card {
  .weather-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .weather-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
    }
  }
  
  .weather-loading {
    padding: 20px 0;
  }
  
  .weather-content {
    .current-weather {
      .weather-main {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        
        .weather-icon {
          width: 80px;
          height: 80px;
          margin-right: 16px;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
        }
        
        .weather-info {
          flex: 1;
          
          .temperature {
            font-size: 36px;
            font-weight: 600;
            color: #409eff;
            line-height: 1;
            margin-bottom: 4px;
          }
          
          .condition {
            font-size: 16px;
            color: #606266;
            margin-bottom: 4px;
          }
          
          .location {
            font-size: 14px;
            color: #909399;
          }
        }
      }
      
      .weather-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
        
        .detail-item {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 14px;
          color: #606266;
          
          .el-icon {
            color: #909399;
          }
        }
      }
    }
    
    .agricultural-index {
      margin-top: 16px;
      
      .index-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        
        .index-item {
          .index-label {
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
          }
          
          .index-value {
            font-size: 12px;
            color: #606266;
            text-align: center;
            margin-top: 4px;
          }
        }
      }
    }
    
    .recommendations {
      margin-top: 16px;
      
      .recommendation-list {
        .recommendation-item {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;
          font-size: 14px;
          color: #606266;
          
          .rec-icon {
            color: #409eff;
            flex-shrink: 0;
          }
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
    
    .weather-footer {
      margin-top: 16px;
      padding-top: 12px;
      border-top: 1px solid #f0f0f0;
      
      .update-time {
        font-size: 12px;
        color: #c0c4cc;
      }
    }
  }
  
  .weather-error {
    padding: 20px 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .weather-card {
    .weather-content {
      .current-weather {
        .weather-main {
          .weather-icon {
            width: 60px;
            height: 60px;
          }
          
          .weather-info {
            .temperature {
              font-size: 28px;
            }
          }
        }
        
        .weather-details {
          grid-template-columns: 1fr;
        }
      }
      
      .agricultural-index {
        .index-grid {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}
</style>
