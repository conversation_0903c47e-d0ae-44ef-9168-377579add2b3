<template>
  <el-card class="weather-forecast" shadow="hover">
    <template #header>
      <div class="forecast-header">
        <span class="forecast-title">
          <el-icon><Calendar /></el-icon>
          {{ days }}天天气预报
        </span>
        <el-select v-model="days" size="small" style="width: 100px;" @change="loadForecast">
          <el-option label="3天" :value="3" />
          <el-option label="5天" :value="5" />
          <el-option label="7天" :value="7" />
        </el-select>
      </div>
    </template>

    <div v-if="loading" class="forecast-loading">
      <el-skeleton :rows="3" animated />
    </div>

    <div v-else-if="forecastData && forecastData.length > 0" class="forecast-content">
      <div class="forecast-list">
        <div 
          v-for="(day, index) in forecastData" 
          :key="index"
          class="forecast-item"
          :class="{ 'today': index === 0 }"
        >
          <div class="forecast-date">
            <div class="date-text">{{ formatForecastDate(day.date, index) }}</div>
            <div class="weekday">{{ getWeekday(day.date) }}</div>
          </div>
          
          <div class="forecast-weather">
            <img 
              :src="getWeatherIcon(day.weather?.icon)" 
              :alt="day.weather?.description"
              class="weather-icon"
            />
            <div class="weather-desc">{{ day.weather?.description }}</div>
          </div>
          
          <div class="forecast-temp">
            <div class="temp-range">
              <span class="temp-max">{{ day.temperature?.max }}°</span>
              <span class="temp-separator">/</span>
              <span class="temp-min">{{ day.temperature?.min }}°</span>
            </div>
          </div>
          
          <div class="forecast-details">
            <div class="detail-row">
              <el-icon><Drizzling /></el-icon>
              <span>{{ day.humidity }}%</span>
            </div>
            <div class="detail-row">
              <el-icon><WindPower /></el-icon>
              <span>{{ day.windSpeed }}m/s</span>
            </div>
          </div>
          
          <!-- 农事建议 -->
          <div v-if="showRecommendations" class="forecast-recommendations">
            <el-tag 
              v-for="rec in getDayRecommendations(day)" 
              :key="rec.type"
              :type="rec.level"
              size="small"
              class="rec-tag"
            >
              {{ rec.text }}
            </el-tag>
          </div>
        </div>
      </div>
      
      <!-- 趋势图表 -->
      <div v-if="showChart" class="forecast-chart">
        <el-divider content-position="left">温度趋势</el-divider>
        <div ref="chartRef" class="chart-container"></div>
      </div>
    </div>

    <div v-else class="forecast-error">
      <empty-state 
        title="获取预报失败" 
        description="无法获取天气预报，请稍后重试"
        :show-action="true"
        action-text="重试"
        @action="loadForecast"
      />
    </div>
  </el-card>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { getWeatherForecast } from '@/api/weather'
import * as echarts from 'echarts'

const props = defineProps({
  location: {
    type: Object,
    default: () => ({
      lat: 39.9042,
      lon: 116.4074,
      name: '农场位置'
    })
  },
  defaultDays: {
    type: Number,
    default: 5
  },
  showRecommendations: {
    type: Boolean,
    default: true
  },
  showChart: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['forecast-update'])

// 响应式数据
const loading = ref(false)
const days = ref(props.defaultDays)
const forecastData = ref([])
const chartRef = ref()
let chart = null

// 方法
const loadForecast = async () => {
  try {
    loading.value = true
    
    const response = await getWeatherForecast({
      ...props.location,
      days: days.value
    })
    
    forecastData.value = response.data.forecast || []
    emit('forecast-update', forecastData.value)
    
    // 更新图表
    if (props.showChart) {
      await nextTick()
      updateChart()
    }
    
  } catch (error) {
    console.error('获取天气预报失败:', error)
    forecastData.value = []
  } finally {
    loading.value = false
  }
}

const formatForecastDate = (date, index) => {
  const d = new Date(date)
  if (index === 0) return '今天'
  if (index === 1) return '明天'
  
  return `${d.getMonth() + 1}/${d.getDate()}`
}

const getWeekday = (date) => {
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  return weekdays[new Date(date).getDay()]
}

const getWeatherIcon = (icon) => {
  if (!icon) return '/images/weather/default.png'
  
  const iconMap = {
    '01d': '/images/weather/sunny.png',
    '01n': '/images/weather/clear-night.png',
    '02d': '/images/weather/partly-cloudy.png',
    '02n': '/images/weather/partly-cloudy-night.png',
    '03d': '/images/weather/cloudy.png',
    '03n': '/images/weather/cloudy.png',
    '04d': '/images/weather/overcast.png',
    '04n': '/images/weather/overcast.png',
    '09d': '/images/weather/rain.png',
    '09n': '/images/weather/rain.png',
    '10d': '/images/weather/rain.png',
    '10n': '/images/weather/rain.png',
    '11d': '/images/weather/thunderstorm.png',
    '11n': '/images/weather/thunderstorm.png',
    '13d': '/images/weather/snow.png',
    '13n': '/images/weather/snow.png',
    '50d': '/images/weather/fog.png',
    '50n': '/images/weather/fog.png'
  }
  
  return iconMap[icon] || `/images/weather/${icon}.png`
}

const getDayRecommendations = (day) => {
  const recommendations = []
  
  // 基于天气条件生成建议
  if (day.weather?.main?.includes('Rain')) {
    recommendations.push({ type: 'rain', level: 'warning', text: '不宜作业' })
  } else if (day.temperature?.max > 35) {
    recommendations.push({ type: 'heat', level: 'danger', text: '高温预警' })
  } else if (day.temperature?.min < 5) {
    recommendations.push({ type: 'cold', level: 'info', text: '注意保温' })
  } else if (day.windSpeed > 10) {
    recommendations.push({ type: 'wind', level: 'warning', text: '大风天气' })
  } else {
    recommendations.push({ type: 'suitable', level: 'success', text: '适宜作业' })
  }
  
  // 湿度建议
  if (day.humidity < 40) {
    recommendations.push({ type: 'dry', level: 'warning', text: '建议灌溉' })
  } else if (day.humidity > 80) {
    recommendations.push({ type: 'humid', level: 'info', text: '注意通风' })
  }
  
  return recommendations
}

const updateChart = () => {
  if (!chartRef.value || !forecastData.value.length) return
  
  if (!chart) {
    chart = echarts.init(chartRef.value)
  }
  
  const dates = forecastData.value.map(day => {
    const date = new Date(day.date)
    return `${date.getMonth() + 1}/${date.getDate()}`
  })
  
  const maxTemps = forecastData.value.map(day => day.temperature?.max || 0)
  const minTemps = forecastData.value.map(day => day.temperature?.min || 0)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach(param => {
          result += `${param.seriesName}: ${param.value}°C<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['最高温度', '最低温度'],
      bottom: 0
    },
    grid: {
      top: 20,
      bottom: 40,
      left: 40,
      right: 20
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLine: {
        lineStyle: { color: '#e0e0e0' }
      },
      axisTick: { show: false },
      axisLabel: {
        color: '#666',
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      name: '温度(°C)',
      nameTextStyle: { color: '#666' },
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: {
        color: '#666',
        fontSize: 12
      },
      splitLine: {
        lineStyle: { color: '#f0f0f0' }
      }
    },
    series: [
      {
        name: '最高温度',
        type: 'line',
        data: maxTemps,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: { color: '#f56c6c', width: 2 },
        itemStyle: { color: '#f56c6c' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(245, 108, 108, 0.3)' },
              { offset: 1, color: 'rgba(245, 108, 108, 0.1)' }
            ]
          }
        }
      },
      {
        name: '最低温度',
        type: 'line',
        data: minTemps,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: { color: '#409eff', width: 2 },
        itemStyle: { color: '#409eff' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        }
      }
    ]
  }
  
  chart.setOption(option)
}

const handleResize = () => {
  if (chart) {
    chart.resize()
  }
}

// 生命周期
onMounted(() => {
  loadForecast()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (chart) {
    chart.dispose()
  }
})

// 暴露方法
defineExpose({
  refresh: loadForecast
})
</script>

<style lang="scss" scoped>
.weather-forecast {
  .forecast-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .forecast-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
    }
  }
  
  .forecast-loading {
    padding: 20px 0;
  }
  
  .forecast-content {
    .forecast-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
      
      .forecast-item {
        display: grid;
        grid-template-columns: 80px 1fr 80px 100px auto;
        gap: 16px;
        align-items: center;
        padding: 12px;
        border-radius: 6px;
        transition: background-color 0.3s;
        
        &:hover {
          background-color: #f8f9fa;
        }
        
        &.today {
          background-color: #e8f4fd;
          border: 1px solid #b3d8ff;
        }
        
        .forecast-date {
          .date-text {
            font-weight: 500;
            color: #303133;
            margin-bottom: 2px;
          }
          
          .weekday {
            font-size: 12px;
            color: #909399;
          }
        }
        
        .forecast-weather {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .weather-icon {
            width: 32px;
            height: 32px;
            object-fit: contain;
          }
          
          .weather-desc {
            font-size: 14px;
            color: #606266;
          }
        }
        
        .forecast-temp {
          text-align: center;
          
          .temp-range {
            .temp-max {
              font-weight: 600;
              color: #f56c6c;
            }
            
            .temp-separator {
              margin: 0 4px;
              color: #c0c4cc;
            }
            
            .temp-min {
              color: #409eff;
            }
          }
        }
        
        .forecast-details {
          display: flex;
          flex-direction: column;
          gap: 4px;
          
          .detail-row {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #909399;
          }
        }
        
        .forecast-recommendations {
          display: flex;
          flex-wrap: wrap;
          gap: 4px;
          
          .rec-tag {
            font-size: 11px;
          }
        }
      }
    }
    
    .forecast-chart {
      margin-top: 20px;
      
      .chart-container {
        height: 200px;
        width: 100%;
      }
    }
  }
  
  .forecast-error {
    padding: 20px 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .weather-forecast {
    .forecast-content {
      .forecast-list {
        .forecast-item {
          grid-template-columns: 1fr;
          gap: 8px;
          text-align: center;
          
          .forecast-details {
            flex-direction: row;
            justify-content: center;
            gap: 16px;
          }
          
          .forecast-recommendations {
            justify-content: center;
          }
        }
      }
    }
  }
}
</style>
