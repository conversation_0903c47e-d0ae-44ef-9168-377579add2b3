<template>
  <el-card class="recommendation-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <div class="header-left">
          <el-icon class="type-icon" :style="{ color: getTypeColor(recommendation.type) }">
            <component :is="getTypeIcon(recommendation.type)" />
          </el-icon>
          <span class="card-title">{{ recommendation.title || getTypeTitle(recommendation.type) }}</span>
        </div>
        <div class="header-right">
          <el-tag :type="getPriorityType(recommendation.priority)" size="small">
            {{ getPriorityText(recommendation.priority) }}
          </el-tag>
        </div>
      </div>
    </template>

    <div class="recommendation-content">
      <!-- 推荐评分 -->
      <div class="score-section" v-if="recommendation.score">
        <div class="score-label">推荐指数</div>
        <div class="score-display">
          <el-progress 
            :percentage="recommendation.score" 
            :color="getScoreColor(recommendation.score)"
            :show-text="false"
            :stroke-width="8"
          />
          <span class="score-value">{{ recommendation.score }}%</span>
        </div>
      </div>

      <!-- 推荐描述 -->
      <div class="description-section">
        <p class="recommendation-desc">{{ recommendation.description }}</p>
      </div>

      <!-- 推荐原因 -->
      <div class="reasons-section" v-if="recommendation.reasons && recommendation.reasons.length">
        <div class="section-title">推荐原因</div>
        <ul class="reason-list">
          <li v-for="reason in recommendation.reasons" :key="reason" class="reason-item">
            <el-icon class="reason-icon"><Check /></el-icon>
            <span>{{ reason }}</span>
          </li>
        </ul>
      </div>

      <!-- 操作建议 -->
      <div class="suggestions-section" v-if="recommendation.suggestions && recommendation.suggestions.length">
        <div class="section-title">操作建议</div>
        <div class="suggestion-list">
          <div 
            v-for="(suggestion, index) in recommendation.suggestions" 
            :key="index"
            class="suggestion-item"
          >
            <div class="suggestion-step">{{ index + 1 }}</div>
            <div class="suggestion-content">
              <div class="suggestion-title">{{ suggestion.title }}</div>
              <div class="suggestion-desc">{{ suggestion.description }}</div>
              <div class="suggestion-meta" v-if="suggestion.timing || suggestion.amount">
                <span v-if="suggestion.timing" class="meta-item">
                  <el-icon><Clock /></el-icon>
                  {{ suggestion.timing }}
                </span>
                <span v-if="suggestion.amount" class="meta-item">
                  <el-icon><Box /></el-icon>
                  {{ suggestion.amount }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 风险提示 -->
      <div class="risks-section" v-if="recommendation.risks && recommendation.risks.length">
        <div class="section-title">
          <el-icon class="warning-icon"><Warning /></el-icon>
          风险提示
        </div>
        <div class="risk-list">
          <div 
            v-for="risk in recommendation.risks" 
            :key="risk.type"
            class="risk-item"
            :class="risk.level"
          >
            <el-icon class="risk-icon">
              <component :is="getRiskIcon(risk.level)" />
            </el-icon>
            <div class="risk-content">
              <div class="risk-title">{{ risk.title }}</div>
              <div class="risk-desc">{{ risk.description }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 预期效果 -->
      <div class="effects-section" v-if="recommendation.expectedEffects">
        <div class="section-title">预期效果</div>
        <div class="effects-grid">
          <div 
            v-for="(effect, key) in recommendation.expectedEffects" 
            :key="key"
            class="effect-item"
          >
            <div class="effect-label">{{ getEffectLabel(key) }}</div>
            <div class="effect-value" :class="getEffectClass(effect)">
              {{ formatEffectValue(effect) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="card-footer">
        <div class="footer-left">
          <span class="update-time">
            更新时间: {{ formatDateTime(recommendation.updatedAt || new Date()) }}
          </span>
        </div>
        <div class="footer-right">
          <el-button size="small" @click="handleViewDetails">
            查看详情
          </el-button>
          <el-button 
            type="primary" 
            size="small" 
            @click="handleAccept"
            v-if="showAcceptButton"
          >
            采纳建议
          </el-button>
        </div>
      </div>
    </template>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import { formatDateTime } from '@/utils'

const props = defineProps({
  recommendation: {
    type: Object,
    required: true
  },
  showAcceptButton: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['view-details', 'accept'])

// 方法
const getTypeIcon = (type) => {
  const iconMap = {
    'planting': 'Seedling',
    'irrigation': 'Drizzling',
    'fertilizer': 'Box',
    'pestControl': 'Warning',
    'harvest': 'Goods',
    'weather': 'Sunny',
    'market': 'TrendCharts'
  }
  return iconMap[type] || 'InfoFilled'
}

const getTypeColor = (type) => {
  const colorMap = {
    'planting': '#67c23a',
    'irrigation': '#409eff',
    'fertilizer': '#e6a23c',
    'pestControl': '#f56c6c',
    'harvest': '#909399',
    'weather': '#409eff',
    'market': '#67c23a'
  }
  return colorMap[type] || '#909399'
}

const getTypeTitle = (type) => {
  const titleMap = {
    'planting': '种植推荐',
    'irrigation': '灌溉建议',
    'fertilizer': '施肥建议',
    'pestControl': '病虫害防治',
    'harvest': '收获建议',
    'weather': '天气建议',
    'market': '市场建议'
  }
  return titleMap[type] || '智能推荐'
}

const getPriorityType = (priority) => {
  const typeMap = {
    'urgent': 'danger',
    'high': 'warning',
    'medium': 'primary',
    'low': 'info'
  }
  return typeMap[priority] || 'info'
}

const getPriorityText = (priority) => {
  const textMap = {
    'urgent': '紧急',
    'high': '高',
    'medium': '中',
    'low': '低'
  }
  return textMap[priority] || '普通'
}

const getScoreColor = (score) => {
  if (score >= 90) return '#67c23a'
  if (score >= 80) return '#95d475'
  if (score >= 70) return '#e6a23c'
  if (score >= 60) return '#f78989'
  return '#f56c6c'
}

const getRiskIcon = (level) => {
  const iconMap = {
    'high': 'CircleCloseFilled',
    'medium': 'WarningFilled',
    'low': 'InfoFilled'
  }
  return iconMap[level] || 'InfoFilled'
}

const getEffectLabel = (key) => {
  const labelMap = {
    'yield': '产量提升',
    'quality': '品质改善',
    'cost': '成本节约',
    'time': '时间节省',
    'risk': '风险降低'
  }
  return labelMap[key] || key
}

const getEffectClass = (effect) => {
  if (typeof effect === 'string') {
    if (effect.includes('+') || effect.includes('提升') || effect.includes('增加')) {
      return 'positive'
    } else if (effect.includes('-') || effect.includes('降低') || effect.includes('减少')) {
      return 'negative'
    }
  }
  return 'neutral'
}

const formatEffectValue = (effect) => {
  if (typeof effect === 'number') {
    return effect > 0 ? `+${effect}%` : `${effect}%`
  }
  return effect
}

const handleViewDetails = () => {
  emit('view-details', props.recommendation)
}

const handleAccept = () => {
  emit('accept', props.recommendation)
}
</script>

<style lang="scss" scoped>
.recommendation-card {
  margin-bottom: 16px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .header-left {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .type-icon {
        font-size: 18px;
      }
      
      .card-title {
        font-weight: 500;
        color: #303133;
      }
    }
  }
  
  .recommendation-content {
    .score-section {
      margin-bottom: 16px;
      
      .score-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
      }
      
      .score-display {
        display: flex;
        align-items: center;
        gap: 12px;
        
        .el-progress {
          flex: 1;
        }
        
        .score-value {
          font-size: 18px;
          font-weight: 600;
          color: #303133;
          min-width: 50px;
        }
      }
    }
    
    .description-section {
      margin-bottom: 16px;
      
      .recommendation-desc {
        margin: 0;
        color: #606266;
        line-height: 1.6;
      }
    }
    
    .section-title {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      gap: 4px;
      
      .warning-icon {
        color: #e6a23c;
      }
    }
    
    .reasons-section,
    .suggestions-section,
    .risks-section,
    .effects-section {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .reason-list {
      margin: 0;
      padding: 0;
      list-style: none;
      
      .reason-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;
        font-size: 14px;
        color: #606266;
        
        .reason-icon {
          color: #67c23a;
          font-size: 12px;
        }
      }
    }
    
    .suggestion-list {
      .suggestion-item {
        display: flex;
        gap: 12px;
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .suggestion-step {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background: #409eff;
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: 500;
          flex-shrink: 0;
        }
        
        .suggestion-content {
          flex: 1;
          
          .suggestion-title {
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .suggestion-desc {
            font-size: 14px;
            color: #606266;
            margin-bottom: 8px;
          }
          
          .suggestion-meta {
            display: flex;
            gap: 16px;
            
            .meta-item {
              display: flex;
              align-items: center;
              gap: 4px;
              font-size: 12px;
              color: #909399;
            }
          }
        }
      }
    }
    
    .risk-list {
      .risk-item {
        display: flex;
        gap: 8px;
        margin-bottom: 8px;
        padding: 8px;
        border-radius: 4px;
        
        &.high {
          background: #fef0f0;
          border-left: 3px solid #f56c6c;
        }
        
        &.medium {
          background: #fdf6ec;
          border-left: 3px solid #e6a23c;
        }
        
        &.low {
          background: #f4f4f5;
          border-left: 3px solid #909399;
        }
        
        .risk-icon {
          margin-top: 2px;
          
          &.high { color: #f56c6c; }
          &.medium { color: #e6a23c; }
          &.low { color: #909399; }
        }
        
        .risk-content {
          flex: 1;
          
          .risk-title {
            font-weight: 500;
            color: #303133;
            margin-bottom: 2px;
          }
          
          .risk-desc {
            font-size: 12px;
            color: #606266;
          }
        }
      }
    }
    
    .effects-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 12px;
      
      .effect-item {
        text-align: center;
        padding: 8px;
        background: #f8f9fa;
        border-radius: 4px;
        
        .effect-label {
          font-size: 12px;
          color: #909399;
          margin-bottom: 4px;
        }
        
        .effect-value {
          font-size: 16px;
          font-weight: 600;
          
          &.positive {
            color: #67c23a;
          }
          
          &.negative {
            color: #f56c6c;
          }
          
          &.neutral {
            color: #303133;
          }
        }
      }
    }
  }
  
  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .footer-left {
      .update-time {
        font-size: 12px;
        color: #c0c4cc;
      }
    }
    
    .footer-right {
      display: flex;
      gap: 8px;
    }
  }
}
</style>
