<template>
  <div class="empty-state">
    <div class="empty-image">
      <el-icon v-if="icon" :size="iconSize">
        <component :is="icon" />
      </el-icon>
      <img v-else-if="image" :src="image" :alt="description" />
      <el-icon v-else :size="iconSize">
        <Box />
      </el-icon>
    </div>
    
    <div class="empty-content">
      <h3 class="empty-title">{{ title }}</h3>
      <p v-if="description" class="empty-description">{{ description }}</p>
      
      <div v-if="$slots.actions || showAction" class="empty-actions">
        <slot name="actions">
          <el-button v-if="showAction" type="primary" @click="handleAction">
            {{ actionText }}
          </el-button>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default: '暂无数据'
  },
  description: {
    type: String,
    default: ''
  },
  icon: {
    type: String,
    default: ''
  },
  image: {
    type: String,
    default: ''
  },
  iconSize: {
    type: Number,
    default: 64
  },
  showAction: {
    type: Boolean,
    default: false
  },
  actionText: {
    type: String,
    default: '刷新'
  }
})

const emit = defineEmits(['action'])

const handleAction = () => {
  emit('action')
}
</script>

<style lang="scss" scoped>
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  min-height: 200px;
}

.empty-image {
  margin-bottom: 16px;
  
  .el-icon {
    color: #c0c4cc;
  }
  
  img {
    max-width: 100px;
    max-height: 100px;
    opacity: 0.6;
  }
}

.empty-content {
  .empty-title {
    margin: 0 0 8px;
    font-size: 16px;
    font-weight: 500;
    color: #909399;
  }
  
  .empty-description {
    margin: 0 0 16px;
    font-size: 14px;
    color: #c0c4cc;
    line-height: 1.5;
  }
}

.empty-actions {
  margin-top: 16px;
}
</style>
