// 全局组件注册
import PageContainer from './PageContainer.vue'
import SearchForm from './SearchForm.vue'
import DataTable from './DataTable.vue'
import FormDialog from './FormDialog.vue'
import ConfirmButton from './ConfirmButton.vue'
import StatusTag from './StatusTag.vue'
import EmptyState from './EmptyState.vue'

export default {
  install(app) {
    app.component('PageContainer', PageContainer)
    app.component('SearchForm', SearchForm)
    app.component('DataTable', DataTable)
    app.component('FormDialog', FormDialog)
    app.component('ConfirmButton', ConfirmButton)
    app.component('StatusTag', StatusTag)
    app.component('EmptyState', EmptyState)
  }
}
