<template>
  <el-tag
    :type="tagType"
    :size="size"
    :effect="effect"
    :round="round"
    :closable="closable"
    :disable-transitions="disableTransitions"
    @close="$emit('close')"
  >
    <el-icon v-if="showIcon" class="status-icon">
      <component :is="iconComponent" />
    </el-icon>
    {{ displayText }}
  </el-tag>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  status: {
    type: String,
    required: true
  },
  type: {
    type: String,
    default: 'auto' // auto, success, info, warning, danger
  },
  size: {
    type: String,
    default: 'default'
  },
  effect: {
    type: String,
    default: 'light'
  },
  round: {
    type: Boolean,
    default: false
  },
  closable: {
    type: Boolean,
    default: false
  },
  disableTransitions: {
    type: Boolean,
    default: false
  },
  showIcon: {
    type: Boolean,
    default: false
  },
  customText: {
    type: String,
    default: ''
  },
  statusMap: {
    type: Object,
    default: () => ({})
  }
})

defineEmits(['close'])

// 默认状态映射
const defaultStatusMap = {
  // 通用状态
  active: { text: '活跃', type: 'success', icon: 'CircleCheck' },
  inactive: { text: '非活跃', type: 'info', icon: 'CircleClose' },
  pending: { text: '待处理', type: 'warning', icon: 'Clock' },
  processing: { text: '处理中', type: 'warning', icon: 'Loading' },
  completed: { text: '已完成', type: 'success', icon: 'Check' },
  cancelled: { text: '已取消', type: 'info', icon: 'Close' },
  failed: { text: '失败', type: 'danger', icon: 'CloseBold' },
  
  // 地块状态
  maintenance: { text: '维护中', type: 'warning', icon: 'Tools' },
  
  // 作物状态
  planted: { text: '已种植', type: 'success', icon: 'Cherry' },
  growing: { text: '生长中', type: 'success', icon: 'Sunny' },
  harvested: { text: '已收获', type: 'info', icon: 'Box' },
  
  // 健康状态
  healthy: { text: '健康', type: 'success', icon: 'CircleCheck' },
  normal: { text: '一般', type: 'warning', icon: 'Warning' },
  sick: { text: '病虫害', type: 'danger', icon: 'WarningFilled' },
  drought: { text: '干旱', type: 'danger', icon: 'Sunny' },
  flood: { text: '涝害', type: 'danger', icon: 'Cloudy' },
  
  // 库存状态
  sufficient: { text: '充足', type: 'success', icon: 'CircleCheck' },
  low: { text: '不足', type: 'warning', icon: 'Warning' },
  empty: { text: '缺货', type: 'danger', icon: 'WarningFilled' },
  
  // 操作状态
  planned: { text: '计划中', type: 'info', icon: 'Calendar' },
  in_progress: { text: '进行中', type: 'warning', icon: 'Loading' },
  
  // 质量等级
  excellent: { text: '优质', type: 'success', icon: 'Star' },
  good: { text: '良好', type: 'success', icon: 'StarFilled' },
  average: { text: '一般', type: 'warning', icon: 'Star' },
  poor: { text: '较差', type: 'danger', icon: 'Star' },
  
  // 天气状态
  sunny: { text: '晴天', type: 'success', icon: 'Sunny' },
  cloudy: { text: '多云', type: 'info', icon: 'Cloudy' },
  rainy: { text: '雨天', type: 'primary', icon: 'Drizzling' },
  stormy: { text: '暴雨', type: 'danger', icon: 'Lightning' }
}

// 合并状态映射
const statusConfig = computed(() => {
  const mergedMap = { ...defaultStatusMap, ...props.statusMap }
  return mergedMap[props.status] || { 
    text: props.status, 
    type: 'info', 
    icon: 'InfoFilled' 
  }
})

// 计算标签类型
const tagType = computed(() => {
  if (props.type !== 'auto') {
    return props.type
  }
  return statusConfig.value.type
})

// 计算显示文本
const displayText = computed(() => {
  if (props.customText) {
    return props.customText
  }
  return statusConfig.value.text
})

// 计算图标组件
const iconComponent = computed(() => {
  return statusConfig.value.icon
})
</script>

<style lang="scss" scoped>
.status-icon {
  margin-right: 4px;
  font-size: 12px;
}

:deep(.el-tag) {
  .el-icon {
    vertical-align: middle;
  }
}
</style>
