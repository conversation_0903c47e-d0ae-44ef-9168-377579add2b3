<template>
  <el-popconfirm
    :title="title"
    :confirm-button-text="confirmText"
    :cancel-button-text="cancelText"
    :confirm-button-type="confirmType"
    :cancel-button-type="cancelType"
    :icon="iconComponent"
    :icon-color="iconColor"
    :hide-icon="hideIcon"
    :width="width"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <template #reference>
      <el-button
        :type="buttonType"
        :size="buttonSize"
        :loading="loading"
        :disabled="disabled"
        :plain="plain"
        :round="round"
        :circle="circle"
        :link="link"
        v-bind="$attrs"
      >
        <el-icon v-if="buttonIcon">
          <component :is="buttonIcon" />
        </el-icon>
        <slot>{{ buttonText }}</slot>
      </el-button>
    </template>
  </el-popconfirm>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  // 确认框相关
  title: {
    type: String,
    default: '确定要执行此操作吗？'
  },
  confirmText: {
    type: String,
    default: '确定'
  },
  cancelText: {
    type: String,
    default: '取消'
  },
  confirmType: {
    type: String,
    default: 'primary'
  },
  cancelType: {
    type: String,
    default: 'text'
  },
  icon: {
    type: String,
    default: 'QuestionFilled'
  },
  iconColor: {
    type: String,
    default: '#f90'
  },
  hideIcon: {
    type: Boolean,
    default: false
  },
  width: {
    type: [String, Number],
    default: 'auto'
  },
  
  // 按钮相关
  buttonText: {
    type: String,
    default: '删除'
  },
  buttonType: {
    type: String,
    default: 'danger'
  },
  buttonSize: {
    type: String,
    default: 'default'
  },
  buttonIcon: {
    type: String,
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  plain: {
    type: Boolean,
    default: false
  },
  round: {
    type: Boolean,
    default: false
  },
  circle: {
    type: Boolean,
    default: false
  },
  link: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['confirm', 'cancel'])

// 计算图标组件
const iconComponent = computed(() => {
  return props.icon
})

// 处理确认
const handleConfirm = () => {
  emit('confirm')
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
// 样式继承自 Element Plus
</style>
