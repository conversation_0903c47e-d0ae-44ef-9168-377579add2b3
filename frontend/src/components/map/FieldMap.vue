<template>
  <div class="field-map">
    <div ref="mapContainer" class="map-container" :style="{ height }"></div>
    
    <!-- 地图控制面板 -->
    <div class="map-controls">
      <el-card shadow="never" class="control-panel">
        <template #header>
          <span>地图控制</span>
        </template>
        
        <div class="control-group">
          <el-button-group>
            <el-button 
              :type="mapMode === 'view' ? 'primary' : ''"
              @click="setMapMode('view')"
            >
              查看
            </el-button>
            <el-button 
              :type="mapMode === 'draw' ? 'primary' : ''"
              @click="setMapMode('draw')"
              v-permission="'field_manage'"
            >
              绘制
            </el-button>
            <el-button 
              :type="mapMode === 'edit' ? 'primary' : ''"
              @click="setMapMode('edit')"
              v-permission="'field_manage'"
            >
              编辑
            </el-button>
          </el-button-group>
        </div>
        
        <div class="control-group" v-if="mapMode === 'draw'">
          <el-button @click="startDrawing" type="success" size="small">
            <el-icon><Plus /></el-icon>
            开始绘制
          </el-button>
          <el-button @click="clearDrawing" size="small">
            <el-icon><Delete /></el-icon>
            清除
          </el-button>
        </div>
        
        <div class="control-group">
          <el-switch
            v-model="showLabels"
            active-text="显示标签"
            @change="toggleLabels"
          />
        </div>
        
        <div class="control-group">
          <el-switch
            v-model="showCrops"
            active-text="显示作物"
            @change="toggleCrops"
          />
        </div>
        
        <!-- 图层控制 -->
        <div class="control-group">
          <div class="layer-title">图层控制</div>
          <el-checkbox-group v-model="visibleLayers" @change="updateLayers">
            <el-checkbox label="fields">地块</el-checkbox>
            <el-checkbox label="crops">作物</el-checkbox>
            <el-checkbox label="irrigation">灌溉</el-checkbox>
            <el-checkbox label="weather">天气</el-checkbox>
          </el-checkbox-group>
        </div>
      </el-card>
    </div>
    
    <!-- 地块信息弹窗 -->
    <el-dialog
      v-model="fieldInfoVisible"
      :title="selectedField?.name || '地块信息'"
      width="600px"
      append-to-body
    >
      <div v-if="selectedField" class="field-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="地块编码">
            {{ selectedField.code }}
          </el-descriptions-item>
          <el-descriptions-item label="地块名称">
            {{ selectedField.name }}
          </el-descriptions-item>
          <el-descriptions-item label="面积">
            {{ selectedField.area }} {{ selectedField.unit }}
          </el-descriptions-item>
          <el-descriptions-item label="土壤类型">
            {{ selectedField.soilType }}
          </el-descriptions-item>
          <el-descriptions-item label="土壤pH">
            {{ selectedField.soilPH || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <status-tag :status="selectedField.status" />
          </el-descriptions-item>
          <el-descriptions-item label="负责人">
            {{ selectedField.owner?.name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="管理员">
            {{ selectedField.manager?.name || '-' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <!-- 当前作物信息 -->
        <div v-if="selectedField.currentCrop" class="current-crop">
          <h4>当前作物</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="作物名称">
              {{ selectedField.currentCrop.name }}
            </el-descriptions-item>
            <el-descriptions-item label="品种">
              {{ selectedField.currentCrop.variety }}
            </el-descriptions-item>
            <el-descriptions-item label="种植日期">
              {{ formatDate(selectedField.currentCrop.plantDate) }}
            </el-descriptions-item>
            <el-descriptions-item label="生长阶段">
              <status-tag :status="selectedField.currentCrop.growthStage" />
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="fieldInfoVisible = false">关闭</el-button>
        <el-button 
          type="primary" 
          @click="editField"
          v-permission="'field_manage'"
        >
          编辑地块
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { formatDate } from '@/utils'

// 引入Leaflet（需要先安装：npm install leaflet）
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'

const props = defineProps({
  fields: {
    type: Array,
    default: () => []
  },
  height: {
    type: String,
    default: '500px'
  },
  center: {
    type: Array,
    default: () => [39.9042, 116.4074] // 北京坐标
  },
  zoom: {
    type: Number,
    default: 13
  },
  editable: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'field-click',
  'field-draw',
  'field-edit',
  'field-delete'
])

// 响应式数据
const mapContainer = ref()
const map = ref(null)
const fieldLayers = ref(new Map())
const drawingLayer = ref(null)
const mapMode = ref('view')
const showLabels = ref(true)
const showCrops = ref(true)
const visibleLayers = ref(['fields', 'crops'])
const fieldInfoVisible = ref(false)
const selectedField = ref(null)

// 地图初始化
const initMap = () => {
  if (!mapContainer.value) return
  
  // 创建地图实例
  map.value = L.map(mapContainer.value, {
    center: props.center,
    zoom: props.zoom,
    zoomControl: true,
    attributionControl: false
  })
  
  // 添加瓦片图层
  L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '© OpenStreetMap contributors'
  }).addTo(map.value)
  
  // 添加比例尺
  L.control.scale({
    position: 'bottomleft',
    metric: true,
    imperial: false
  }).addTo(map.value)
  
  // 初始化绘制工具
  initDrawingTools()
  
  // 加载地块数据
  loadFields()
}

// 初始化绘制工具
const initDrawingTools = () => {
  if (!map.value) return
  
  // 创建绘制图层
  drawingLayer.value = L.layerGroup().addTo(map.value)
}

// 加载地块数据
const loadFields = () => {
  if (!map.value || !props.fields) return
  
  // 清除现有图层
  fieldLayers.value.forEach(layer => {
    map.value.removeLayer(layer)
  })
  fieldLayers.value.clear()
  
  // 添加地块图层
  props.fields.forEach(field => {
    if (field.location && field.location.coordinates) {
      addFieldToMap(field)
    }
  })
}

// 添加地块到地图
const addFieldToMap = (field) => {
  if (!map.value || !field.location) return
  
  try {
    const coordinates = field.location.coordinates[0]
    const latLngs = coordinates.map(coord => [coord[1], coord[0]]) // 转换为Leaflet格式
    
    // 创建多边形
    const polygon = L.polygon(latLngs, {
      color: getFieldColor(field),
      fillColor: getFieldFillColor(field),
      fillOpacity: 0.3,
      weight: 2
    })
    
    // 添加点击事件
    polygon.on('click', () => {
      handleFieldClick(field)
    })
    
    // 添加悬停事件
    polygon.on('mouseover', (e) => {
      e.target.setStyle({
        weight: 3,
        fillOpacity: 0.5
      })
    })
    
    polygon.on('mouseout', (e) => {
      e.target.setStyle({
        weight: 2,
        fillOpacity: 0.3
      })
    })
    
    // 添加到地图
    polygon.addTo(map.value)
    fieldLayers.value.set(field._id, polygon)
    
    // 添加标签
    if (showLabels.value) {
      addFieldLabel(field, polygon)
    }
    
  } catch (error) {
    console.error('添加地块到地图失败:', error)
  }
}

// 添加地块标签
const addFieldLabel = (field, polygon) => {
  if (!map.value) return
  
  const center = polygon.getBounds().getCenter()
  const label = L.marker(center, {
    icon: L.divIcon({
      className: 'field-label',
      html: `<div class="label-content">
        <div class="field-name">${field.name}</div>
        <div class="field-area">${field.area}${field.unit}</div>
      </div>`,
      iconSize: [100, 40],
      iconAnchor: [50, 20]
    })
  })
  
  label.addTo(map.value)
  
  // 存储标签引用
  if (!fieldLayers.value.has(`${field._id}_label`)) {
    fieldLayers.value.set(`${field._id}_label`, label)
  }
}

// 获取地块颜色
const getFieldColor = (field) => {
  const colorMap = {
    'active': '#409eff',
    'inactive': '#909399',
    'maintenance': '#e6a23c'
  }
  return colorMap[field.status] || '#409eff'
}

// 获取地块填充颜色
const getFieldFillColor = (field) => {
  if (field.currentCrop) {
    const cropColorMap = {
      '玉米': '#67c23a',
      '小麦': '#f56c6c',
      '大豆': '#e6a23c',
      '水稻': '#409eff'
    }
    return cropColorMap[field.currentCrop.name] || '#67c23a'
  }
  return '#909399'
}

// 处理地块点击
const handleFieldClick = (field) => {
  selectedField.value = field
  fieldInfoVisible.value = true
  emit('field-click', field)
}

// 设置地图模式
const setMapMode = (mode) => {
  mapMode.value = mode
  
  if (mode === 'draw') {
    map.value.getContainer().style.cursor = 'crosshair'
  } else {
    map.value.getContainer().style.cursor = ''
  }
}

// 开始绘制
const startDrawing = () => {
  if (!map.value || !drawingLayer.value) return
  
  ElMessage.info('点击地图开始绘制地块边界')
  
  let points = []
  let tempLine = null
  
  const onMapClick = (e) => {
    points.push([e.latlng.lat, e.latlng.lng])
    
    if (points.length === 1) {
      // 第一个点，开始绘制
      tempLine = L.polyline(points, { color: 'red', dashArray: '5, 5' })
      tempLine.addTo(drawingLayer.value)
    } else {
      // 更新临时线
      tempLine.setLatLngs(points)
    }
  }
  
  const onMapDblClick = (e) => {
    if (points.length >= 3) {
      // 完成绘制
      const polygon = L.polygon(points, {
        color: '#409eff',
        fillColor: '#409eff',
        fillOpacity: 0.3
      })
      
      polygon.addTo(drawingLayer.value)
      
      // 移除临时线
      if (tempLine) {
        drawingLayer.value.removeLayer(tempLine)
      }
      
      // 转换坐标格式
      const coordinates = points.map(point => [point[1], point[0]])
      coordinates.push(coordinates[0]) // 闭合多边形
      
      emit('field-draw', {
        type: 'Polygon',
        coordinates: [coordinates]
      })
      
      ElMessage.success('地块绘制完成')
    }
    
    // 清理事件监听
    map.value.off('click', onMapClick)
    map.value.off('dblclick', onMapDblClick)
    points = []
    tempLine = null
  }
  
  // 添加事件监听
  map.value.on('click', onMapClick)
  map.value.on('dblclick', onMapDblClick)
}

// 清除绘制
const clearDrawing = () => {
  if (drawingLayer.value) {
    drawingLayer.value.clearLayers()
  }
}

// 切换标签显示
const toggleLabels = (show) => {
  fieldLayers.value.forEach((layer, key) => {
    if (key.includes('_label')) {
      if (show) {
        layer.addTo(map.value)
      } else {
        map.value.removeLayer(layer)
      }
    }
  })
}

// 切换作物显示
const toggleCrops = (show) => {
  // 重新加载地块以更新颜色
  loadFields()
}

// 更新图层
const updateLayers = (layers) => {
  // 根据选中的图层更新显示
  if (layers.includes('fields')) {
    loadFields()
  } else {
    fieldLayers.value.forEach(layer => {
      if (!layer._leaflet_id.includes('_label')) {
        map.value.removeLayer(layer)
      }
    })
  }
}

// 编辑地块
const editField = () => {
  if (selectedField.value) {
    emit('field-edit', selectedField.value)
    fieldInfoVisible.value = false
  }
}

// 监听地块数据变化
watch(() => props.fields, () => {
  nextTick(() => {
    loadFields()
  })
}, { deep: true })

// 生命周期
onMounted(() => {
  nextTick(() => {
    initMap()
  })
})

onUnmounted(() => {
  if (map.value) {
    map.value.remove()
  }
})
</script>

<style lang="scss" scoped>
.field-map {
  position: relative;
  width: 100%;
  
  .map-container {
    width: 100%;
    border-radius: 4px;
    overflow: hidden;
  }
  
  .map-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
    
    .control-panel {
      width: 200px;
      
      .control-group {
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .layer-title {
          font-size: 12px;
          color: #606266;
          margin-bottom: 8px;
        }
      }
    }
  }
  
  .field-info {
    .current-crop {
      margin-top: 20px;
      
      h4 {
        margin: 0 0 12px;
        color: #303133;
      }
    }
  }
}

// 地块标签样式
:deep(.field-label) {
  .label-content {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 4px 8px;
    text-align: center;
    font-size: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    .field-name {
      font-weight: 500;
      color: #303133;
    }
    
    .field-area {
      color: #606266;
      font-size: 11px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .field-map {
    .map-controls {
      position: static;
      margin-top: 10px;
      
      .control-panel {
        width: 100%;
      }
    }
  }
}
</style>
