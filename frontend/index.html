<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>农场智慧管理系统</title>
    <meta name="description" content="现代化农场管理系统，集成GIS地块管理、农事操作记录、物资管理等功能" />
    <meta name="keywords" content="农场管理,智慧农业,GIS,农事记录,物资管理" />
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <!-- 预加载关键资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <style>
      /* 加载动画 */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f5f7fa;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e4e7ed;
        border-top: 4px solid #409eff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        margin-left: 16px;
        color: #606266;
        font-size: 14px;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <!-- 加载动画 -->
      <div id="loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">农场管理系统加载中...</div>
      </div>
    </div>
    
    <script type="module" src="/src/main.js"></script>
    
    <script>
      // 隐藏加载动画
      window.addEventListener('load', () => {
        const loading = document.getElementById('loading');
        if (loading) {
          setTimeout(() => {
            loading.style.opacity = '0';
            setTimeout(() => {
              loading.style.display = 'none';
            }, 300);
          }, 500);
        }
      });
    </script>
  </body>
</html>
