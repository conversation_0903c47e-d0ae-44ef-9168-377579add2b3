# 农场智慧管理系统 API 文档

## 基础信息

- **基础URL**: `http://localhost:5000/api`
- **认证方式**: <PERSON><PERSON> (JWT)
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 失败响应
```json
{
  "success": false,
  "message": "错误信息",
  "error": "详细错误信息",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 分页响应
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "list": [],
    "pagination": {
      "current": 1,
      "pageSize": 10,
      "total": 100,
      "pages": 10
    }
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 认证接口

### 用户登录
- **URL**: `/auth/login`
- **方法**: `POST`
- **描述**: 用户登录获取访问令牌

**请求参数**:
```json
{
  "username": "admin",
  "password": "123456"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "user": {
      "_id": "user_id",
      "username": "admin",
      "name": "系统管理员",
      "role": "admin",
      "permissions": ["field_manage", "crop_manage"]
    },
    "token": "jwt_token_here"
  }
}
```

### 用户注册
- **URL**: `/auth/register`
- **方法**: `POST`
- **描述**: 注册新用户

**请求参数**:
```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "password123",
  "name": "用户姓名",
  "phone": "13800138000",
  "role": "worker"
}
```

### 获取当前用户信息
- **URL**: `/auth/me`
- **方法**: `GET`
- **描述**: 获取当前登录用户信息
- **认证**: 需要

### 更新用户资料
- **URL**: `/auth/profile`
- **方法**: `PUT`
- **描述**: 更新用户个人资料
- **认证**: 需要

### 修改密码
- **URL**: `/auth/change-password`
- **方法**: `PUT`
- **描述**: 修改用户密码
- **认证**: 需要

## 地块管理接口

### 获取地块列表
- **URL**: `/fields`
- **方法**: `GET`
- **描述**: 获取地块列表
- **认证**: 需要

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)
- `status`: 地块状态 (active/inactive/maintenance)
- `soilType`: 土壤类型
- `search`: 搜索关键词

### 获取地块详情
- **URL**: `/fields/:id`
- **方法**: `GET`
- **描述**: 获取指定地块详情
- **认证**: 需要

### 创建地块
- **URL**: `/fields`
- **方法**: `POST`
- **描述**: 创建新地块
- **认证**: 需要
- **权限**: field_manage

**请求参数**:
```json
{
  "name": "东区地块A",
  "code": "A001",
  "area": 50.5,
  "unit": "亩",
  "location": {
    "type": "Polygon",
    "coordinates": [[[116.3974, 39.9093], [116.3984, 39.9093]]]
  },
  "soilType": "壤土",
  "irrigation": "滴灌",
  "drainage": "良好"
}
```

### 更新地块
- **URL**: `/fields/:id`
- **方法**: `PUT`
- **描述**: 更新地块信息
- **认证**: 需要
- **权限**: field_manage

### 删除地块
- **URL**: `/fields/:id`
- **方法**: `DELETE`
- **描述**: 删除地块
- **认证**: 需要
- **权限**: field_manage

## 作物管理接口

### 获取作物列表
- **URL**: `/crops`
- **方法**: `GET`
- **描述**: 获取作物列表
- **认证**: 需要

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `field`: 地块ID
- `category`: 作物类别
- `growthStage`: 生长阶段
- `status`: 作物状态
- `season`: 季节
- `year`: 年份

### 获取作物详情
- **URL**: `/crops/:id`
- **方法**: `GET`
- **描述**: 获取指定作物详情
- **认证**: 需要

### 创建作物
- **URL**: `/crops`
- **方法**: `POST`
- **描述**: 创建新作物
- **认证**: 需要
- **权限**: crop_manage

**请求参数**:
```json
{
  "name": "玉米",
  "variety": "先玉335",
  "category": "粮食作物",
  "field": "field_id",
  "plantDate": "2024-04-15",
  "expectedHarvestDate": "2024-09-15",
  "plantingArea": 45.0,
  "expectedYield": 2250,
  "season": "春季",
  "year": 2024
}
```

### 更新作物
- **URL**: `/crops/:id`
- **方法**: `PUT`
- **描述**: 更新作物信息
- **认证**: 需要
- **权限**: crop_manage

### 删除作物
- **URL**: `/crops/:id`
- **方法**: `DELETE`
- **描述**: 删除作物
- **认证**: 需要
- **权限**: crop_manage

## 农事操作接口

### 获取农事操作列表
- **URL**: `/farming`
- **方法**: `GET`
- **描述**: 获取农事操作记录列表
- **认证**: 需要

### 创建农事操作
- **URL**: `/farming`
- **方法**: `POST`
- **描述**: 创建农事操作记录
- **认证**: 需要
- **权限**: farming_manage

**请求参数**:
```json
{
  "type": "浇灌",
  "crop": "crop_id",
  "operationDate": "2024-05-01",
  "quantity": 100,
  "unit": "升",
  "cost": 50,
  "description": "定期浇灌",
  "weather": "晴天"
}
```

## 收获管理接口

### 获取收获记录列表
- **URL**: `/harvest`
- **方法**: `GET`
- **描述**: 获取收获记录列表
- **认证**: 需要

### 创建收获记录
- **URL**: `/harvest`
- **方法**: `POST`
- **描述**: 创建收获记录
- **认证**: 需要
- **权限**: harvest_manage

## 物资管理接口

### 获取物资列表
- **URL**: `/materials`
- **方法**: `GET`
- **描述**: 获取物资列表
- **认证**: 需要

### 创建物资
- **URL**: `/materials`
- **方法**: `POST`
- **描述**: 创建物资记录
- **认证**: 需要
- **权限**: material_manage

## 天气信息接口

### 获取天气信息
- **URL**: `/weather/current`
- **方法**: `GET`
- **描述**: 获取当前天气信息
- **认证**: 需要

### 获取天气预报
- **URL**: `/weather/forecast`
- **方法**: `GET`
- **描述**: 获取天气预报
- **认证**: 需要

## 仪表盘接口

### 获取概览数据
- **URL**: `/dashboard/overview`
- **方法**: `GET`
- **描述**: 获取仪表盘概览数据
- **认证**: 需要

### 获取趋势数据
- **URL**: `/dashboard/trends`
- **方法**: `GET`
- **描述**: 获取趋势分析数据
- **认证**: 需要

## 文件上传接口

### 上传文件
- **URL**: `/upload`
- **方法**: `POST`
- **描述**: 上传文件
- **认证**: 需要
- **Content-Type**: `multipart/form-data`

## 错误码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

## 权限说明

| 权限代码 | 说明 |
|----------|------|
| field_manage | 地块管理权限 |
| crop_manage | 作物管理权限 |
| farming_manage | 农事操作权限 |
| harvest_manage | 收获管理权限 |
| material_manage | 物资管理权限 |
| weather_view | 天气查看权限 |
| user_manage | 用户管理权限 |
| system_manage | 系统管理权限 |
