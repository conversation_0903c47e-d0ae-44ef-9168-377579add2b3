#!/bin/bash

echo "========================================"
echo "启动农场智慧管理系统 - 第三阶段"
echo "========================================"
echo

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "错误: Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "错误: npm 未安装，请先安装 npm"
    exit 1
fi

# 启动后端服务
echo "正在启动后端服务..."
cd backend
if [ ! -d "node_modules" ]; then
    echo "安装后端依赖..."
    npm install
fi
npm run dev &
BACKEND_PID=$!
cd ..

# 等待后端服务启动
echo "等待后端服务启动..."
sleep 3

# 启动前端应用
echo "正在启动前端应用..."
cd frontend
if [ ! -d "node_modules" ]; then
    echo "安装前端依赖..."
    npm install
fi
npm run dev &
FRONTEND_PID=$!
cd ..

# 等待前端应用启动
echo "等待前端应用启动..."
sleep 3

# 启动数据大屏
echo "正在启动数据大屏..."
cd dashboard
if [ ! -d "node_modules" ]; then
    echo "安装数据大屏依赖..."
    npm install
fi
npm run dev &
DASHBOARD_PID=$!
cd ..

echo
echo "========================================"
echo "所有服务启动完成！"
echo "========================================"
echo "后端API: http://localhost:5000"
echo "前端应用: http://localhost:3000"
echo "数据大屏: http://localhost:3001"
echo "========================================"
echo

# 等待用户输入来停止服务
echo "按 Ctrl+C 停止所有服务..."

# 捕获中断信号
trap 'echo "正在停止所有服务..."; kill $BACKEND_PID $FRONTEND_PID $DASHBOARD_PID 2>/dev/null; exit 0' INT

# 等待所有后台进程
wait
