@echo off
echo ========================================
echo 安装移动端开发环境和依赖
echo ========================================
echo.

echo 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js环境检查通过
echo.

echo 检查是否已安装Expo CLI...
expo --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装Expo CLI...
    npm install -g @expo/cli
    if %errorlevel% neq 0 (
        echo ❌ Expo CLI安装失败
        pause
        exit /b 1
    )
    echo ✅ Expo CLI安装成功
) else (
    echo ✅ Expo CLI已安装
)
echo.

echo 进入移动端目录...
cd mobile
if %errorlevel% neq 0 (
    echo ❌ 移动端目录不存在
    pause
    exit /b 1
)

echo 安装移动端依赖包...
npm install
if %errorlevel% neq 0 (
    echo ❌ 依赖包安装失败
    pause
    exit /b 1
)

echo ✅ 移动端依赖包安装成功
echo.

echo 安装视频处理依赖...
npm install fluent-ffmpeg
if %errorlevel% neq 0 (
    echo ⚠️ 视频处理依赖安装失败，视频功能可能受限
)

echo.
echo ========================================
echo 移动端环境安装完成！
echo ========================================
echo.
echo 📱 支持的平台:
echo - Android (需要Android Studio)
echo - iOS (需要Xcode，仅限macOS)
echo - Web浏览器
echo.
echo 🚀 启动命令:
echo - 启动开发服务器: npm start
echo - 启动Android模拟器: npm run android
echo - 启动iOS模拟器: npm run ios
echo - 启动Web版本: npm run web
echo.
echo 📋 开发工具推荐:
echo - Expo Go App (手机扫码调试)
echo - Android Studio (Android开发)
echo - Xcode (iOS开发，仅限macOS)
echo.
echo ========================================
echo.
pause
