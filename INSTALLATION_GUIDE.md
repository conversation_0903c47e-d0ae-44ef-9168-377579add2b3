# 农场智慧管理系统安装指南

## 📋 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上 (推荐8GB)
- **存储**: 20GB以上可用空间
- **网络**: 稳定的网络连接

### 操作系统支持
- **Windows**: Windows 10/11, Windows Server 2016+
- **Linux**: Ubuntu 18.04+, CentOS 7+, Debian 9+
- **macOS**: macOS 10.14+

## 🛠️ 环境准备

### 1. Node.js 安装

#### Windows 安装
1. 访问 [Node.js官网](https://nodejs.org/)
2. 下载LTS版本 (推荐16.x或18.x)
3. 运行安装程序，按默认设置安装
4. 验证安装：
```bash
node --version
npm --version
```

#### Linux 安装 (Ubuntu/Debian)
```bash
# 使用NodeSource仓库安装
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

#### Linux 安装 (CentOS/RHEL)
```bash
# 使用NodeSource仓库安装
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# 验证安装
node --version
npm --version
```

#### macOS 安装
```bash
# 使用Homebrew安装
brew install node

# 验证安装
node --version
npm --version
```

### 2. MongoDB 安装

#### Windows 安装
1. 访问 [MongoDB官网](https://www.mongodb.com/try/download/community)
2. 下载MongoDB Community Server
3. 运行安装程序，选择"Complete"安装
4. 安装MongoDB Compass (可选的图形界面工具)
5. 启动MongoDB服务：
```bash
# 启动MongoDB服务
net start MongoDB

# 验证安装
mongosh --version
```

#### Linux 安装 (Ubuntu)
```bash
# 导入MongoDB公钥
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -

# 添加MongoDB仓库
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list

# 更新包列表并安装
sudo apt-get update
sudo apt-get install -y mongodb-org

# 启动MongoDB服务
sudo systemctl start mongod
sudo systemctl enable mongod

# 验证安装
mongosh --version
```

#### macOS 安装
```bash
# 使用Homebrew安装
brew tap mongodb/brew
brew install mongodb-community

# 启动MongoDB服务
brew services start mongodb/brew/mongodb-community

# 验证安装
mongosh --version
```

### 3. Git 安装

#### Windows
1. 访问 [Git官网](https://git-scm.com/)
2. 下载并安装Git for Windows
3. 验证安装：
```bash
git --version
```

#### Linux
```bash
# Ubuntu/Debian
sudo apt-get install git

# CentOS/RHEL
sudo yum install git

# 验证安装
git --version
```

#### macOS
```bash
# 使用Homebrew安装
brew install git

# 验证安装
git --version
```

## 📦 项目安装

### 1. 获取项目代码

```bash
# 克隆项目仓库
git clone <项目仓库地址>
cd farm-management-system

# 或者解压项目压缩包
unzip farm-management-system.zip
cd farm-management-system
```

### 2. 一键安装脚本

#### Windows 用户
```bash
# 运行安装脚本
start.bat

# 选择选项 5 - 安装依赖
```

#### Linux/macOS 用户
```bash
# 给脚本执行权限
chmod +x start.sh

# 运行安装脚本
./start.sh

# 选择选项 5 - 安装依赖
```

### 3. 手动安装

如果一键安装失败，可以手动安装各模块：

#### 后端安装
```bash
cd backend
npm install
```

#### 前端安装
```bash
cd frontend
npm install
```

#### 数据大屏安装
```bash
cd dashboard
npm install
```

#### 移动端安装 (可选)
```bash
cd mobile
npm install
```

## ⚙️ 配置设置

### 1. 后端配置

#### 环境变量配置
```bash
cd backend

# 复制环境配置模板
cp .env.example .env

# 编辑配置文件
# Windows: notepad .env
# Linux/macOS: nano .env
```

#### 主要配置项说明
```bash
# 应用配置
NODE_ENV=development
PORT=5000
APP_NAME=农场智慧管理系统

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/farm_management

# JWT配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRE=7d

# 前端地址
FRONTEND_URL=http://localhost:3000
DASHBOARD_URL=http://localhost:3001

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# 邮件配置 (可选)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
```

### 2. 数据库初始化

```bash
cd backend

# 初始化数据库
npm run init-db
```

初始化完成后会创建默认用户：
- **管理员**: admin / 123456
- **经理**: manager / 123456  
- **工人**: worker / 123456

## 🚀 启动系统

### 1. 一键启动

#### Windows
```bash
start.bat
# 选择选项 1 - 完整启动
```

#### Linux/macOS
```bash
./start.sh
# 选择选项 1 - 完整启动
```

### 2. 手动启动

#### 启动后端
```bash
cd backend
npm run dev
```

#### 启动前端
```bash
cd frontend
npm run dev
```

#### 启动数据大屏
```bash
cd dashboard
npm run dev
```

### 3. 访问系统

启动成功后，可以通过以下地址访问：

- **前端管理界面**: http://localhost:3000
- **数据大屏**: http://localhost:3001
- **后端API**: http://localhost:5000
- **API文档**: http://localhost:5000/api-docs (如果启用)

## 🐳 Docker 部署 (可选)

### 1. 安装Docker

#### Windows
1. 下载 [Docker Desktop for Windows](https://www.docker.com/products/docker-desktop)
2. 安装并启动Docker Desktop

#### Linux
```bash
# Ubuntu
sudo apt-get update
sudo apt-get install docker.io docker-compose
sudo systemctl start docker
sudo systemctl enable docker

# 添加用户到docker组
sudo usermod -aG docker $USER
```

#### macOS
1. 下载 [Docker Desktop for Mac](https://www.docker.com/products/docker-desktop)
2. 安装并启动Docker Desktop

### 2. Docker部署

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 🔧 常见问题解决

### 1. Node.js相关问题

#### 问题：npm install 失败
```bash
# 清理npm缓存
npm cache clean --force

# 使用淘宝镜像
npm config set registry https://registry.npm.taobao.org

# 重新安装
npm install
```

#### 问题：权限错误 (Linux/macOS)
```bash
# 修复npm权限
sudo chown -R $(whoami) ~/.npm
```

### 2. MongoDB相关问题

#### 问题：MongoDB连接失败
```bash
# 检查MongoDB服务状态
# Windows
net start MongoDB

# Linux
sudo systemctl status mongod
sudo systemctl start mongod

# macOS
brew services start mongodb/brew/mongodb-community
```

#### 问题：数据库初始化失败
```bash
# 检查MongoDB是否正常运行
mongosh

# 手动创建数据库
use farm_management
```

### 3. 端口冲突问题

#### 修改端口配置
```bash
# 后端端口修改 (backend/.env)
PORT=5001

# 前端端口修改 (frontend/vite.config.js)
server: {
  port: 3001
}

# 数据大屏端口修改 (dashboard/vite.config.js)
server: {
  port: 3002
}
```

### 4. 防火墙问题

#### Windows
```bash
# 允许Node.js通过防火墙
# 控制面板 -> 系统和安全 -> Windows Defender 防火墙 -> 允许应用通过防火墙
```

#### Linux
```bash
# 开放端口
sudo ufw allow 3000
sudo ufw allow 3001
sudo ufw allow 5000
```

## 📚 开发环境设置

### 1. 推荐的开发工具

- **代码编辑器**: Visual Studio Code
- **API测试**: Postman 或 Insomnia
- **数据库管理**: MongoDB Compass
- **版本控制**: Git

### 2. VS Code 扩展推荐

```json
{
  "recommendations": [
    "ms-vscode.vscode-json",
    "bradlc.vscode-tailwindcss",
    "vue.volar",
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint"
  ]
}
```

## 🎯 下一步

安装完成后，您可以：

1. **登录系统**: 使用默认账号登录管理界面
2. **配置农场**: 添加地块、作物等基础信息
3. **设备接入**: 配置IoT设备和摄像头
4. **数据录入**: 开始记录农事操作数据
5. **查看报表**: 在数据大屏查看农场运营情况

## 📞 技术支持

如果在安装过程中遇到问题，请：

1. 查看本文档的常见问题部分
2. 检查系统日志文件
3. 联系技术支持团队

---

🎉 **恭喜！农场智慧管理系统安装完成！**
