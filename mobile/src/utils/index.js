import { Dimensions, Platform, PixelRatio } from 'react-native';

// 获取屏幕尺寸
export const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// 判断是否为iOS
export const isIOS = Platform.OS === 'ios';

// 判断是否为Android
export const isAndroid = Platform.OS === 'android';

// 获取像素密度
export const pixelRatio = PixelRatio.get();

// 响应式尺寸计算
export const wp = (percentage) => {
  const value = (percentage * SCREEN_WIDTH) / 100;
  return Math.round(PixelRatio.roundToNearestPixel(value));
};

export const hp = (percentage) => {
  const value = (percentage * SCREEN_HEIGHT) / 100;
  return Math.round(PixelRatio.roundToNearestPixel(value));
};

// 字体大小适配
export const normalize = (size) => {
  const scale = SCREEN_WIDTH / 320;
  const newSize = size * scale;
  
  if (isIOS) {
    return Math.round(PixelRatio.roundToNearestPixel(newSize));
  } else {
    return Math.round(PixelRatio.roundToNearestPixel(newSize)) - 2;
  }
};

// 日期格式化
export const formatDate = (date, format = 'YYYY-MM-DD') => {
  if (!date) return '';
  
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
};

// 时间格式化
export const formatTime = (date) => {
  return formatDate(date, 'HH:mm');
};

// 日期时间格式化
export const formatDateTime = (date) => {
  return formatDate(date, 'YYYY-MM-DD HH:mm');
};

// 相对时间格式化
export const formatRelativeTime = (date) => {
  if (!date) return '';
  
  const now = new Date();
  const target = new Date(date);
  const diff = now - target;
  
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) {
    return `${days}天前`;
  } else if (hours > 0) {
    return `${hours}小时前`;
  } else if (minutes > 0) {
    return `${minutes}分钟前`;
  } else {
    return '刚刚';
  }
};

// 数字格式化
export const formatNumber = (num, decimals = 0) => {
  if (typeof num !== 'number') return num;
  
  return num.toLocaleString('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });
};

// 文件大小格式化
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 百分比格式化
export const formatPercentage = (value, total) => {
  if (!total || total === 0) return '0%';
  return `${Math.round((value / total) * 100)}%`;
};

// 验证邮箱
export const validateEmail = (email) => {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(email);
};

// 验证手机号
export const validatePhone = (phone) => {
  const re = /^1[3-9]\d{9}$/;
  return re.test(phone);
};

// 验证密码强度
export const validatePassword = (password) => {
  if (password.length < 6) {
    return { valid: false, message: '密码至少6位' };
  }
  
  if (password.length < 8) {
    return { valid: true, strength: 'weak', message: '密码强度：弱' };
  }
  
  const hasNumber = /\d/.test(password);
  const hasLetter = /[a-zA-Z]/.test(password);
  const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  
  const score = [hasNumber, hasLetter, hasSpecial].filter(Boolean).length;
  
  if (score === 3) {
    return { valid: true, strength: 'strong', message: '密码强度：强' };
  } else if (score === 2) {
    return { valid: true, strength: 'medium', message: '密码强度：中' };
  } else {
    return { valid: true, strength: 'weak', message: '密码强度：弱' };
  }
};

// 防抖函数
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 节流函数
export const throttle = (func, limit) => {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// 深拷贝
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  if (typeof obj === 'object') {
    const clonedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
};

// 生成唯一ID
export const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// 颜色处理
export const hexToRgba = (hex, alpha = 1) => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

// 获取状态颜色
export const getStatusColor = (status) => {
  const colors = {
    online: '#4CAF50',
    offline: '#F44336',
    maintenance: '#FF9800',
    warning: '#FF9800',
    error: '#F44336',
    success: '#4CAF50',
    info: '#2196F3',
    default: '#757575',
  };
  return colors[status] || colors.default;
};

// 获取状态文本
export const getStatusText = (status) => {
  const texts = {
    online: '在线',
    offline: '离线',
    maintenance: '维护中',
    warning: '警告',
    error: '错误',
    success: '成功',
    info: '信息',
    active: '激活',
    inactive: '未激活',
    pending: '待处理',
    completed: '已完成',
    cancelled: '已取消',
  };
  return texts[status] || status;
};

// 数组去重
export const uniqueArray = (arr, key) => {
  if (!key) {
    return [...new Set(arr)];
  }
  
  const seen = new Set();
  return arr.filter(item => {
    const value = item[key];
    if (seen.has(value)) {
      return false;
    }
    seen.add(value);
    return true;
  });
};

// 数组分组
export const groupBy = (arr, key) => {
  return arr.reduce((groups, item) => {
    const group = item[key];
    groups[group] = groups[group] || [];
    groups[group].push(item);
    return groups;
  }, {});
};

// 安全的JSON解析
export const safeJsonParse = (str, defaultValue = null) => {
  try {
    return JSON.parse(str);
  } catch (e) {
    return defaultValue;
  }
};

// 检查网络连接
export const checkNetworkConnection = async () => {
  try {
    const response = await fetch('https://www.google.com', {
      method: 'HEAD',
      timeout: 5000,
    });
    return response.ok;
  } catch (error) {
    return false;
  }
};

export default {
  wp,
  hp,
  normalize,
  formatDate,
  formatTime,
  formatDateTime,
  formatRelativeTime,
  formatNumber,
  formatFileSize,
  formatPercentage,
  validateEmail,
  validatePhone,
  validatePassword,
  debounce,
  throttle,
  deepClone,
  generateId,
  hexToRgba,
  getStatusColor,
  getStatusText,
  uniqueArray,
  groupBy,
  safeJsonParse,
  checkNetworkConnection,
};
