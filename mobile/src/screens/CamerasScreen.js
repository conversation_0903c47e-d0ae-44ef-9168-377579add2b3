import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  Alert,
  Dimensions,
} from 'react-native';
import {
  Text,
  Card,
  Chip,
  FAB,
  Searchbar,
  Menu,
  IconButton,
  Badge,
  Button,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useFocusEffect } from '@react-navigation/native';

import { theme } from '../styles/theme';
import LoadingSpinner, { EmptyState } from '../components/common/LoadingSpinner';
import ErrorMessage from '../components/common/ErrorMessage';
import { formatDateTime, getStatusColor, getStatusText } from '../utils';
import { getCameras, deleteCamera, startRecording, stopRecording } from '../services/api';

const { width: screenWidth } = Dimensions.get('window');

const CamerasScreen = ({ navigation }) => {
  const [cameras, setCameras] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredCameras, setFilteredCameras] = useState([]);
  const [menuVisible, setMenuVisible] = useState({});
  const [recordingStates, setRecordingStates] = useState({});

  // 获取摄像头列表
  const fetchCameras = useCallback(async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);
      setError(null);
      
      const response = await getCameras();
      setCameras(response.data.cameras || []);
    } catch (err) {
      setError(err.message || '获取摄像头列表失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  // 下拉刷新
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchCameras(false);
  }, [fetchCameras]);

  // 搜索过滤
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredCameras(cameras);
    } else {
      const filtered = cameras.filter(camera =>
        camera.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        camera.position?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        camera.field?.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredCameras(filtered);
    }
  }, [cameras, searchQuery]);

  // 页面聚焦时刷新数据
  useFocusEffect(
    useCallback(() => {
      fetchCameras();
    }, [fetchCameras])
  );

  // 切换录像状态
  const handleToggleRecording = async (cameraId, isRecording) => {
    try {
      setRecordingStates(prev => ({ ...prev, [cameraId]: 'loading' }));
      
      if (isRecording) {
        await stopRecording(cameraId);
        setRecordingStates(prev => ({ ...prev, [cameraId]: 'stopped' }));
      } else {
        await startRecording(cameraId);
        setRecordingStates(prev => ({ ...prev, [cameraId]: 'recording' }));
      }
      
      // 更新摄像头状态
      setCameras(prev => prev.map(camera => 
        camera._id === cameraId 
          ? { ...camera, isRecording: !isRecording }
          : camera
      ));
    } catch (err) {
      setRecordingStates(prev => ({ ...prev, [cameraId]: 'error' }));
      Alert.alert('操作失败', err.message || '录像操作失败');
    }
  };

  // 删除摄像头
  const handleDeleteCamera = (cameraId, cameraName) => {
    Alert.alert(
      '确认删除',
      `确定要删除摄像头"${cameraName}"吗？此操作不可恢复。`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteCamera(cameraId);
              setCameras(prev => prev.filter(camera => camera._id !== cameraId));
            } catch (err) {
              Alert.alert('删除失败', err.message || '删除摄像头失败');
            }
          },
        },
      ]
    );
  };

  // 切换菜单显示
  const toggleMenu = (cameraId) => {
    setMenuVisible(prev => ({
      ...prev,
      [cameraId]: !prev[cameraId],
    }));
  };

  // 获取录像状态文本
  const getRecordingStatusText = (camera) => {
    const state = recordingStates[camera._id];
    if (state === 'loading') return '处理中...';
    if (state === 'error') return '操作失败';
    return camera.isRecording ? '录制中' : '未录制';
  };

  // 获取录像状态颜色
  const getRecordingStatusColor = (camera) => {
    const state = recordingStates[camera._id];
    if (state === 'loading') return theme.custom.colors.warning;
    if (state === 'error') return theme.custom.colors.error;
    return camera.isRecording ? theme.custom.colors.error : theme.custom.colors.textSecondary;
  };

  // 渲染摄像头卡片
  const renderCameraCard = ({ item: camera }) => {
    const statusColor = getStatusColor(camera.status);
    const statusText = getStatusText(camera.status);
    const isOnline = camera.isOnline;

    return (
      <Card style={styles.cameraCard}>
        <TouchableOpacity
          onPress={() => navigation.navigate('CameraDetail', { cameraId: camera._id })}
          activeOpacity={0.7}
        >
          {/* 摄像头预览区域 */}
          <View style={styles.previewContainer}>
            {isOnline ? (
              <View style={styles.videoPreview}>
                <Icon name="videocam" size={48} color={theme.custom.colors.textSecondary} />
                <Text style={styles.previewText}>点击查看实时画面</Text>
              </View>
            ) : (
              <View style={styles.offlinePreview}>
                <Icon name="videocam-off" size={48} color={theme.custom.colors.offline} />
                <Text style={styles.offlineText}>摄像头离线</Text>
              </View>
            )}
            
            {/* 在线状态指示器 */}
            <View style={styles.statusIndicator}>
              <Badge
                size={12}
                style={[
                  styles.onlineBadge,
                  { backgroundColor: isOnline ? theme.custom.colors.online : theme.custom.colors.offline }
                ]}
              />
            </View>

            {/* 录制状态指示器 */}
            {camera.isRecording && (
              <View style={styles.recordingIndicator}>
                <Icon name="fiber-manual-record" size={16} color={theme.custom.colors.error} />
                <Text style={styles.recordingText}>REC</Text>
              </View>
            )}
          </View>

          <View style={styles.cardContent}>
            <View style={styles.cardHeader}>
              <View style={styles.cameraInfo}>
                <Text style={styles.cameraName}>{camera.name}</Text>
                <Text style={styles.cameraPosition}>
                  {camera.position || '未设置位置'}
                </Text>
              </View>
              
              <View style={styles.cardActions}>
                <Chip
                  mode="outlined"
                  textStyle={[styles.statusText, { color: statusColor }]}
                  style={[styles.statusChip, { borderColor: statusColor }]}
                >
                  {statusText}
                </Chip>
                
                <Menu
                  visible={menuVisible[camera._id] || false}
                  onDismiss={() => toggleMenu(camera._id)}
                  anchor={
                    <IconButton
                      icon="more-vert"
                      size={20}
                      onPress={() => toggleMenu(camera._id)}
                    />
                  }
                >
                  <Menu.Item
                    onPress={() => {
                      toggleMenu(camera._id);
                      navigation.navigate('EditCamera', { cameraId: camera._id });
                    }}
                    title="编辑"
                    leadingIcon="edit"
                  />
                  <Menu.Item
                    onPress={() => {
                      toggleMenu(camera._id);
                      navigation.navigate('CameraRecords', { cameraId: camera._id });
                    }}
                    title="录像记录"
                    leadingIcon="video-library"
                  />
                  <Menu.Item
                    onPress={() => {
                      toggleMenu(camera._id);
                      handleDeleteCamera(camera._id, camera.name);
                    }}
                    title="删除"
                    leadingIcon="delete"
                  />
                </Menu>
              </View>
            </View>

            <View style={styles.cameraDetails}>
              {camera.field && (
                <View style={styles.detailItem}>
                  <Icon name="location-on" size={16} color={theme.custom.colors.textSecondary} />
                  <Text style={styles.detailText}>地块: {camera.field.name}</Text>
                </View>
              )}
              
              <View style={styles.detailItem}>
                <Icon name="high-quality" size={16} color={theme.custom.colors.textSecondary} />
                <Text style={styles.detailText}>
                  分辨率: {camera.resolution || '1920x1080'}
                </Text>
              </View>
              
              <View style={styles.detailItem}>
                <Icon 
                  name={camera.isRecording ? "fiber-manual-record" : "stop"} 
                  size={16} 
                  color={getRecordingStatusColor(camera)} 
                />
                <Text style={[styles.detailText, { color: getRecordingStatusColor(camera) }]}>
                  {getRecordingStatusText(camera)}
                </Text>
              </View>
            </View>

            {/* 操作按钮 */}
            <View style={styles.actionButtons}>
              <Button
                mode="outlined"
                icon="play-arrow"
                onPress={() => navigation.navigate('LiveView', { cameraId: camera._id })}
                disabled={!isOnline}
                style={styles.actionButton}
                labelStyle={styles.actionButtonText}
              >
                实时查看
              </Button>
              
              <Button
                mode={camera.isRecording ? "contained" : "outlined"}
                icon={camera.isRecording ? "stop" : "fiber-manual-record"}
                onPress={() => handleToggleRecording(camera._id, camera.isRecording)}
                disabled={!isOnline || recordingStates[camera._id] === 'loading'}
                style={[
                  styles.actionButton,
                  camera.isRecording && { backgroundColor: theme.custom.colors.error }
                ]}
                labelStyle={styles.actionButtonText}
              >
                {camera.isRecording ? '停止录制' : '开始录制'}
              </Button>
            </View>
          </View>
        </TouchableOpacity>
      </Card>
    );
  };

  // 渲染空状态
  const renderEmptyState = () => (
    <EmptyState
      icon="videocam"
      title="暂无摄像头"
      message="还没有添加任何摄像头，点击右下角按钮开始添加"
      actionText="添加摄像头"
      onAction={() => navigation.navigate('AddCamera')}
    />
  );

  if (loading) {
    return <LoadingSpinner visible={true} text="加载摄像头列表..." />;
  }

  if (error) {
    return (
      <ErrorMessage
        visible={true}
        title="加载失败"
        message={error}
        onRetry={() => fetchCameras()}
        fullScreen={true}
      />
    );
  }

  return (
    <View style={styles.container}>
      {/* 搜索栏 */}
      <Searchbar
        placeholder="搜索摄像头名称或位置"
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchbar}
      />

      {/* 摄像头列表 */}
      <FlatList
        data={filteredCameras}
        renderItem={renderCameraCard}
        keyExtractor={(item) => item._id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
          />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />

      {/* 添加按钮 */}
      <FAB
        icon="add"
        style={styles.fab}
        onPress={() => navigation.navigate('AddCamera')}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  
  searchbar: {
    margin: theme.custom.spacing.md,
    elevation: 2,
  },
  
  listContainer: {
    flexGrow: 1,
    paddingHorizontal: theme.custom.spacing.sm,
    paddingBottom: theme.custom.spacing.xl,
  },
  
  cameraCard: {
    marginVertical: theme.custom.spacing.xs,
    marginHorizontal: theme.custom.spacing.xs,
    elevation: 2,
    overflow: 'hidden',
  },
  
  previewContainer: {
    height: 180,
    backgroundColor: '#000',
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  videoPreview: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  previewText: {
    color: theme.custom.colors.textSecondary,
    fontSize: theme.custom.fontSizes.sm,
    marginTop: theme.custom.spacing.sm,
  },
  
  offlinePreview: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  offlineText: {
    color: theme.custom.colors.offline,
    fontSize: theme.custom.fontSizes.sm,
    marginTop: theme.custom.spacing.sm,
  },
  
  statusIndicator: {
    position: 'absolute',
    top: theme.custom.spacing.sm,
    right: theme.custom.spacing.sm,
  },
  
  onlineBadge: {
    borderWidth: 2,
    borderColor: '#fff',
  },
  
  recordingIndicator: {
    position: 'absolute',
    top: theme.custom.spacing.sm,
    left: theme.custom.spacing.sm,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: theme.custom.spacing.sm,
    paddingVertical: theme.custom.spacing.xs,
    borderRadius: theme.custom.borderRadius.sm,
  },
  
  recordingText: {
    color: '#fff',
    fontSize: theme.custom.fontSizes.xs,
    fontWeight: 'bold',
    marginLeft: theme.custom.spacing.xs,
  },
  
  cardContent: {
    padding: theme.custom.spacing.md,
  },
  
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.custom.spacing.sm,
  },
  
  cameraInfo: {
    flex: 1,
  },
  
  cameraName: {
    fontSize: theme.custom.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.custom.spacing.xs,
  },
  
  cameraPosition: {
    fontSize: theme.custom.fontSizes.sm,
    color: theme.custom.colors.textSecondary,
  },
  
  cardActions: {
    alignItems: 'flex-end',
  },
  
  statusChip: {
    marginBottom: theme.custom.spacing.xs,
  },
  
  statusText: {
    fontSize: theme.custom.fontSizes.xs,
  },
  
  cameraDetails: {
    marginBottom: theme.custom.spacing.md,
  },
  
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.custom.spacing.xs,
  },
  
  detailText: {
    fontSize: theme.custom.fontSizes.sm,
    color: theme.custom.colors.textSecondary,
    marginLeft: theme.custom.spacing.xs,
  },
  
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: theme.custom.spacing.sm,
  },
  
  actionButton: {
    flex: 1,
  },
  
  actionButtonText: {
    fontSize: theme.custom.fontSizes.sm,
  },
  
  fab: {
    position: 'absolute',
    margin: theme.custom.spacing.md,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.primary,
  },
});

export default CamerasScreen;
