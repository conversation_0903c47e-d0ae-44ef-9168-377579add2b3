import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Chip,
  FAB,
  Searchbar,
  Menu,
  IconButton,
  Badge,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useFocusEffect } from '@react-navigation/native';

import { theme } from '../styles/theme';
import LoadingSpinner, { EmptyState } from '../components/common/LoadingSpinner';
import ErrorMessage from '../components/common/ErrorMessage';
import { formatDateTime, getStatusColor, getStatusText } from '../utils';
import { getDevices, deleteDevice, toggleDeviceStatus } from '../services/api';

const DevicesScreen = ({ navigation }) => {
  const [devices, setDevices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredDevices, setFilteredDevices] = useState([]);
  const [menuVisible, setMenuVisible] = useState({});

  // 获取设备列表
  const fetchDevices = useCallback(async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);
      setError(null);
      
      const response = await getDevices();
      setDevices(response.data.devices || []);
    } catch (err) {
      setError(err.message || '获取设备列表失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  // 下拉刷新
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchDevices(false);
  }, [fetchDevices]);

  // 搜索过滤
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredDevices(devices);
    } else {
      const filtered = devices.filter(device =>
        device.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        device.deviceId.toLowerCase().includes(searchQuery.toLowerCase()) ||
        device.type.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredDevices(filtered);
    }
  }, [devices, searchQuery]);

  // 页面聚焦时刷新数据
  useFocusEffect(
    useCallback(() => {
      fetchDevices();
    }, [fetchDevices])
  );

  // 切换设备状态
  const handleToggleStatus = async (deviceId, currentStatus) => {
    try {
      const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
      await toggleDeviceStatus(deviceId, { status: newStatus });
      
      setDevices(prev => prev.map(device => 
        device._id === deviceId 
          ? { ...device, status: newStatus }
          : device
      ));
    } catch (err) {
      Alert.alert('操作失败', err.message || '切换设备状态失败');
    }
  };

  // 删除设备
  const handleDeleteDevice = (deviceId, deviceName) => {
    Alert.alert(
      '确认删除',
      `确定要删除设备"${deviceName}"吗？此操作不可恢复。`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteDevice(deviceId);
              setDevices(prev => prev.filter(device => device._id !== deviceId));
            } catch (err) {
              Alert.alert('删除失败', err.message || '删除设备失败');
            }
          },
        },
      ]
    );
  };

  // 切换菜单显示
  const toggleMenu = (deviceId) => {
    setMenuVisible(prev => ({
      ...prev,
      [deviceId]: !prev[deviceId],
    }));
  };

  // 获取设备类型图标
  const getDeviceIcon = (type) => {
    const iconMap = {
      sensor: 'sensors',
      camera: 'videocam',
      controller: 'settings-remote',
      irrigation: 'water-drop',
      weather: 'wb-sunny',
      default: 'device-hub',
    };
    return iconMap[type] || iconMap.default;
  };

  // 获取设备类型文本
  const getDeviceTypeText = (type) => {
    const typeMap = {
      sensor: '传感器',
      camera: '摄像头',
      controller: '控制器',
      irrigation: '灌溉设备',
      weather: '气象站',
    };
    return typeMap[type] || type;
  };

  // 渲染设备卡片
  const renderDeviceCard = ({ item: device }) => {
    const statusColor = getStatusColor(device.status);
    const statusText = getStatusText(device.status);
    const isOnline = device.isOnline;

    return (
      <Card style={styles.deviceCard}>
        <TouchableOpacity
          onPress={() => navigation.navigate('DeviceDetail', { deviceId: device._id })}
          activeOpacity={0.7}
        >
          <View style={styles.cardHeader}>
            <View style={styles.deviceIcon}>
              <Icon
                name={getDeviceIcon(device.type)}
                size={24}
                color={theme.colors.primary}
              />
              {isOnline && (
                <Badge
                  size={8}
                  style={[styles.onlineBadge, { backgroundColor: theme.custom.colors.online }]}
                />
              )}
            </View>
            
            <View style={styles.deviceInfo}>
              <Text style={styles.deviceName}>{device.name}</Text>
              <Text style={styles.deviceId}>ID: {device.deviceId}</Text>
              <Text style={styles.deviceType}>{getDeviceTypeText(device.type)}</Text>
            </View>
            
            <View style={styles.cardActions}>
              <Chip
                mode="outlined"
                textStyle={[styles.statusText, { color: statusColor }]}
                style={[styles.statusChip, { borderColor: statusColor }]}
              >
                {statusText}
              </Chip>
              
              <Menu
                visible={menuVisible[device._id] || false}
                onDismiss={() => toggleMenu(device._id)}
                anchor={
                  <IconButton
                    icon="more-vert"
                    size={20}
                    onPress={() => toggleMenu(device._id)}
                  />
                }
              >
                <Menu.Item
                  onPress={() => {
                    toggleMenu(device._id);
                    navigation.navigate('EditDevice', { deviceId: device._id });
                  }}
                  title="编辑"
                  leadingIcon="edit"
                />
                <Menu.Item
                  onPress={() => {
                    toggleMenu(device._id);
                    handleToggleStatus(device._id, device.status);
                  }}
                  title={device.status === 'active' ? '禁用' : '启用'}
                  leadingIcon={device.status === 'active' ? 'pause' : 'play-arrow'}
                />
                <Menu.Item
                  onPress={() => {
                    toggleMenu(device._id);
                    handleDeleteDevice(device._id, device.name);
                  }}
                  title="删除"
                  leadingIcon="delete"
                />
              </Menu>
            </View>
          </View>

          <View style={styles.cardContent}>
            {device.field && (
              <View style={styles.deviceDetail}>
                <Icon name="location-on" size={16} color={theme.custom.colors.textSecondary} />
                <Text style={styles.detailText}>地块: {device.field.name}</Text>
              </View>
            )}
            
            <View style={styles.deviceDetail}>
              <Icon 
                name={isOnline ? 'wifi' : 'wifi-off'} 
                size={16} 
                color={isOnline ? theme.custom.colors.online : theme.custom.colors.offline} 
              />
              <Text style={[
                styles.detailText, 
                { color: isOnline ? theme.custom.colors.online : theme.custom.colors.offline }
              ]}>
                {isOnline ? '在线' : '离线'}
              </Text>
            </View>
            
            {device.lastDataTime && (
              <View style={styles.deviceDetail}>
                <Icon name="schedule" size={16} color={theme.custom.colors.textSecondary} />
                <Text style={styles.detailText}>
                  最后数据: {formatDateTime(device.lastDataTime)}
                </Text>
              </View>
            )}
          </View>

          {device.description && (
            <View style={styles.cardFooter}>
              <Text style={styles.description} numberOfLines={2}>
                {device.description}
              </Text>
            </View>
          )}
        </TouchableOpacity>
      </Card>
    );
  };

  // 渲染空状态
  const renderEmptyState = () => (
    <EmptyState
      icon="device-hub"
      title="暂无设备"
      message="还没有添加任何设备，点击右下角按钮开始添加"
      actionText="添加设备"
      onAction={() => navigation.navigate('AddDevice')}
    />
  );

  if (loading) {
    return <LoadingSpinner visible={true} text="加载设备列表..." />;
  }

  if (error) {
    return (
      <ErrorMessage
        visible={true}
        title="加载失败"
        message={error}
        onRetry={() => fetchDevices()}
        fullScreen={true}
      />
    );
  }

  return (
    <View style={styles.container}>
      {/* 搜索栏 */}
      <Searchbar
        placeholder="搜索设备名称、ID或类型"
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchbar}
      />

      {/* 设备列表 */}
      <FlatList
        data={filteredDevices}
        renderItem={renderDeviceCard}
        keyExtractor={(item) => item._id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
          />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />

      {/* 添加按钮 */}
      <FAB
        icon="add"
        style={styles.fab}
        onPress={() => navigation.navigate('AddDevice')}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  
  searchbar: {
    margin: theme.custom.spacing.md,
    elevation: 2,
  },
  
  listContainer: {
    flexGrow: 1,
    paddingHorizontal: theme.custom.spacing.sm,
    paddingBottom: theme.custom.spacing.xl,
  },
  
  deviceCard: {
    marginVertical: theme.custom.spacing.xs,
    marginHorizontal: theme.custom.spacing.xs,
    elevation: 2,
  },
  
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: theme.custom.spacing.md,
    paddingBottom: theme.custom.spacing.sm,
  },
  
  deviceIcon: {
    position: 'relative',
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.custom.spacing.md,
  },
  
  onlineBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
  },
  
  deviceInfo: {
    flex: 1,
  },
  
  deviceName: {
    fontSize: theme.custom.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.custom.spacing.xs,
  },
  
  deviceId: {
    fontSize: theme.custom.fontSizes.sm,
    color: theme.custom.colors.textSecondary,
    marginBottom: theme.custom.spacing.xs,
  },
  
  deviceType: {
    fontSize: theme.custom.fontSizes.sm,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  
  cardActions: {
    alignItems: 'flex-end',
  },
  
  statusChip: {
    marginBottom: theme.custom.spacing.xs,
  },
  
  statusText: {
    fontSize: theme.custom.fontSizes.xs,
  },
  
  cardContent: {
    paddingHorizontal: theme.custom.spacing.md,
    paddingBottom: theme.custom.spacing.sm,
  },
  
  deviceDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.custom.spacing.xs,
  },
  
  detailText: {
    fontSize: theme.custom.fontSizes.sm,
    color: theme.custom.colors.textSecondary,
    marginLeft: theme.custom.spacing.xs,
  },
  
  cardFooter: {
    paddingHorizontal: theme.custom.spacing.md,
    paddingBottom: theme.custom.spacing.md,
    borderTopWidth: 1,
    borderTopColor: theme.custom.colors.divider,
    marginTop: theme.custom.spacing.sm,
    paddingTop: theme.custom.spacing.sm,
  },
  
  description: {
    fontSize: theme.custom.fontSizes.sm,
    color: theme.custom.colors.textSecondary,
    lineHeight: 18,
  },
  
  fab: {
    position: 'absolute',
    margin: theme.custom.spacing.md,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.primary,
  },
});

export default DevicesScreen;
