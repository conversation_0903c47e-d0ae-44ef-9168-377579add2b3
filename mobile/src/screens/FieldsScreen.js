import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Chip,
  FAB,
  Searchbar,
  Menu,
  IconButton,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useFocusEffect } from '@react-navigation/native';

import { theme } from '../styles/theme';
import LoadingSpinner, { EmptyState } from '../components/common/LoadingSpinner';
import ErrorMessage from '../components/common/ErrorMessage';
import { formatNumber, getStatusColor, getStatusText } from '../utils';
import { getFields, deleteField } from '../services/api';

const FieldsScreen = ({ navigation }) => {
  const [fields, setFields] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredFields, setFilteredFields] = useState([]);
  const [menuVisible, setMenuVisible] = useState({});

  // 获取地块列表
  const fetchFields = useCallback(async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);
      setError(null);
      
      const response = await getFields();
      setFields(response.data.fields || []);
    } catch (err) {
      setError(err.message || '获取地块列表失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  // 下拉刷新
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchFields(false);
  }, [fetchFields]);

  // 搜索过滤
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredFields(fields);
    } else {
      const filtered = fields.filter(field =>
        field.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        field.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
        field.location.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredFields(filtered);
    }
  }, [fields, searchQuery]);

  // 页面聚焦时刷新数据
  useFocusEffect(
    useCallback(() => {
      fetchFields();
    }, [fetchFields])
  );

  // 删除地块
  const handleDeleteField = (fieldId, fieldName) => {
    Alert.alert(
      '确认删除',
      `确定要删除地块"${fieldName}"吗？此操作不可恢复。`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteField(fieldId);
              setFields(prev => prev.filter(field => field._id !== fieldId));
              // 可以添加成功提示
            } catch (err) {
              Alert.alert('删除失败', err.message || '删除地块失败');
            }
          },
        },
      ]
    );
  };

  // 切换菜单显示
  const toggleMenu = (fieldId) => {
    setMenuVisible(prev => ({
      ...prev,
      [fieldId]: !prev[fieldId],
    }));
  };

  // 渲染地块卡片
  const renderFieldCard = ({ item: field }) => {
    const statusColor = getStatusColor(field.status);
    const statusText = getStatusText(field.status);

    return (
      <Card style={styles.fieldCard}>
        <TouchableOpacity
          onPress={() => navigation.navigate('FieldDetail', { fieldId: field._id })}
          activeOpacity={0.7}
        >
          <View style={styles.cardHeader}>
            <View style={styles.fieldInfo}>
              <Text style={styles.fieldName}>{field.name}</Text>
              <Text style={styles.fieldCode}>编号: {field.code}</Text>
            </View>
            
            <View style={styles.cardActions}>
              <Chip
                mode="outlined"
                textStyle={[styles.statusText, { color: statusColor }]}
                style={[styles.statusChip, { borderColor: statusColor }]}
              >
                {statusText}
              </Chip>
              
              <Menu
                visible={menuVisible[field._id] || false}
                onDismiss={() => toggleMenu(field._id)}
                anchor={
                  <IconButton
                    icon="more-vert"
                    size={20}
                    onPress={() => toggleMenu(field._id)}
                  />
                }
              >
                <Menu.Item
                  onPress={() => {
                    toggleMenu(field._id);
                    navigation.navigate('EditField', { fieldId: field._id });
                  }}
                  title="编辑"
                  leadingIcon="edit"
                />
                <Menu.Item
                  onPress={() => {
                    toggleMenu(field._id);
                    handleDeleteField(field._id, field.name);
                  }}
                  title="删除"
                  leadingIcon="delete"
                />
              </Menu>
            </View>
          </View>

          <View style={styles.cardContent}>
            <View style={styles.fieldDetail}>
              <Icon name="location-on" size={16} color={theme.custom.colors.textSecondary} />
              <Text style={styles.detailText}>{field.location}</Text>
            </View>
            
            <View style={styles.fieldDetail}>
              <Icon name="crop-landscape" size={16} color={theme.custom.colors.textSecondary} />
              <Text style={styles.detailText}>
                面积: {formatNumber(field.area, 2)} 亩
              </Text>
            </View>
            
            {field.soilType && (
              <View style={styles.fieldDetail}>
                <Icon name="terrain" size={16} color={theme.custom.colors.textSecondary} />
                <Text style={styles.detailText}>土壤: {field.soilType}</Text>
              </View>
            )}
          </View>

          {field.description && (
            <View style={styles.cardFooter}>
              <Text style={styles.description} numberOfLines={2}>
                {field.description}
              </Text>
            </View>
          )}
        </TouchableOpacity>
      </Card>
    );
  };

  // 渲染空状态
  const renderEmptyState = () => (
    <EmptyState
      icon="landscape"
      title="暂无地块"
      message="还没有添加任何地块，点击右下角按钮开始添加"
      actionText="添加地块"
      onAction={() => navigation.navigate('AddField')}
    />
  );

  if (loading) {
    return <LoadingSpinner visible={true} text="加载地块列表..." />;
  }

  if (error) {
    return (
      <ErrorMessage
        visible={true}
        title="加载失败"
        message={error}
        onRetry={() => fetchFields()}
        fullScreen={true}
      />
    );
  }

  return (
    <View style={styles.container}>
      {/* 搜索栏 */}
      <Searchbar
        placeholder="搜索地块名称、编号或位置"
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchbar}
      />

      {/* 地块列表 */}
      <FlatList
        data={filteredFields}
        renderItem={renderFieldCard}
        keyExtractor={(item) => item._id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
          />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />

      {/* 添加按钮 */}
      <FAB
        icon="add"
        style={styles.fab}
        onPress={() => navigation.navigate('AddField')}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  
  searchbar: {
    margin: theme.custom.spacing.md,
    elevation: 2,
  },
  
  listContainer: {
    flexGrow: 1,
    paddingHorizontal: theme.custom.spacing.sm,
    paddingBottom: theme.custom.spacing.xl,
  },
  
  fieldCard: {
    marginVertical: theme.custom.spacing.xs,
    marginHorizontal: theme.custom.spacing.xs,
    elevation: 2,
  },
  
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: theme.custom.spacing.md,
    paddingBottom: theme.custom.spacing.sm,
  },
  
  fieldInfo: {
    flex: 1,
  },
  
  fieldName: {
    fontSize: theme.custom.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.custom.spacing.xs,
  },
  
  fieldCode: {
    fontSize: theme.custom.fontSizes.sm,
    color: theme.custom.colors.textSecondary,
  },
  
  cardActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  statusChip: {
    marginRight: theme.custom.spacing.xs,
  },
  
  statusText: {
    fontSize: theme.custom.fontSizes.xs,
  },
  
  cardContent: {
    paddingHorizontal: theme.custom.spacing.md,
    paddingBottom: theme.custom.spacing.sm,
  },
  
  fieldDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.custom.spacing.xs,
  },
  
  detailText: {
    fontSize: theme.custom.fontSizes.sm,
    color: theme.custom.colors.textSecondary,
    marginLeft: theme.custom.spacing.xs,
  },
  
  cardFooter: {
    paddingHorizontal: theme.custom.spacing.md,
    paddingBottom: theme.custom.spacing.md,
    borderTopWidth: 1,
    borderTopColor: theme.custom.colors.divider,
    marginTop: theme.custom.spacing.sm,
    paddingTop: theme.custom.spacing.sm,
  },
  
  description: {
    fontSize: theme.custom.fontSizes.sm,
    color: theme.custom.colors.textSecondary,
    lineHeight: 18,
  },
  
  fab: {
    position: 'absolute',
    margin: theme.custom.spacing.md,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.primary,
  },
});

export default FieldsScreen;
