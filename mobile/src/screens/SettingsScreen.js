import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  List,
  Switch,
  Divider,
  Button,
  Dialog,
  Portal,
  RadioButton,
} from 'react-native-paper';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { theme } from '../styles/theme';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { formatFileSize } from '../utils';
import NotificationService from '../services/NotificationService';

const SettingsScreen = ({ navigation }) => {
  const [loading, setLoading] = useState(true);
  const [settings, setSettings] = useState({
    notifications: true,
    darkMode: false,
    autoSync: true,
    dataUsage: 'wifi',
    cacheSize: 0,
    language: 'zh',
    syncInterval: '30',
  });
  const [dialogVisible, setDialogVisible] = useState({
    dataUsage: false,
    language: false,
    syncInterval: false,
    clearCache: false,
  });

  // 数据使用选项
  const dataUsageOptions = [
    { value: 'wifi', label: '仅WiFi', description: '只在WiFi环境下同步数据' },
    { value: 'cellular', label: '移动网络', description: '允许使用移动数据同步' },
    { value: 'always', label: '总是', description: '在任何网络环境下都同步数据' },
  ];

  // 语言选项
  const languageOptions = [
    { value: 'zh', label: '中文简体' },
    { value: 'en', label: 'English' },
    { value: 'zh-TW', label: '中文繁體' },
  ];

  // 同步间隔选项
  const syncIntervalOptions = [
    { value: '15', label: '15分钟' },
    { value: '30', label: '30分钟' },
    { value: '60', label: '1小时' },
    { value: '180', label: '3小时' },
    { value: 'manual', label: '手动同步' },
  ];

  useEffect(() => {
    loadSettings();
  }, []);

  // 加载设置
  const loadSettings = async () => {
    try {
      setLoading(true);
      
      const keys = [
        'notification_enabled',
        'dark_mode',
        'auto_sync',
        'data_usage',
        'language',
        'sync_interval',
      ];
      
      const values = await AsyncStorage.multiGet(keys);
      const newSettings = { ...settings };
      
      values.forEach(([key, value]) => {
        if (value !== null) {
          switch (key) {
            case 'notification_enabled':
              newSettings.notifications = value === 'true';
              break;
            case 'dark_mode':
              newSettings.darkMode = value === 'true';
              break;
            case 'auto_sync':
              newSettings.autoSync = value === 'true';
              break;
            case 'data_usage':
              newSettings.dataUsage = value;
              break;
            case 'language':
              newSettings.language = value;
              break;
            case 'sync_interval':
              newSettings.syncInterval = value;
              break;
          }
        }
      });
      
      // 获取缓存大小
      const cacheSize = await getCacheSize();
      newSettings.cacheSize = cacheSize;
      
      setSettings(newSettings);
    } catch (error) {
      console.error('加载设置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 保存设置
  const saveSetting = async (key, value) => {
    try {
      await AsyncStorage.setItem(key, value.toString());
    } catch (error) {
      console.error('保存设置失败:', error);
    }
  };

  // 获取缓存大小
  const getCacheSize = async () => {
    try {
      // 这里应该计算实际的缓存大小
      // 暂时返回模拟数据
      return Math.random() * 100 * 1024 * 1024; // 随机大小，单位字节
    } catch (error) {
      return 0;
    }
  };

  // 切换通知设置
  const handleNotificationToggle = async (enabled) => {
    setSettings(prev => ({ ...prev, notifications: enabled }));
    await saveSetting('notification_enabled', enabled);
    
    if (enabled) {
      const permissions = await NotificationService.requestPermissions();
      if (!permissions.alert) {
        Alert.alert(
          '权限提醒',
          '请在系统设置中开启通知权限以接收重要消息',
          [
            { text: '取消' },
            { text: '去设置', onPress: () => {/* 打开系统设置 */} },
          ]
        );
      }
    }
  };

  // 切换深色模式
  const handleDarkModeToggle = async (enabled) => {
    setSettings(prev => ({ ...prev, darkMode: enabled }));
    await saveSetting('dark_mode', enabled);
    Alert.alert('提示', '深色模式将在下次启动时生效');
  };

  // 切换自动同步
  const handleAutoSyncToggle = async (enabled) => {
    setSettings(prev => ({ ...prev, autoSync: enabled }));
    await saveSetting('auto_sync', enabled);
  };

  // 设置数据使用策略
  const handleDataUsageChange = async (value) => {
    setSettings(prev => ({ ...prev, dataUsage: value }));
    await saveSetting('data_usage', value);
    setDialogVisible(prev => ({ ...prev, dataUsage: false }));
  };

  // 设置语言
  const handleLanguageChange = async (value) => {
    setSettings(prev => ({ ...prev, language: value }));
    await saveSetting('language', value);
    setDialogVisible(prev => ({ ...prev, language: false }));
    Alert.alert('提示', '语言设置将在下次启动时生效');
  };

  // 设置同步间隔
  const handleSyncIntervalChange = async (value) => {
    setSettings(prev => ({ ...prev, syncInterval: value }));
    await saveSetting('sync_interval', value);
    setDialogVisible(prev => ({ ...prev, syncInterval: false }));
  };

  // 清除缓存
  const handleClearCache = async () => {
    try {
      // 这里应该实现实际的缓存清理逻辑
      await AsyncStorage.removeItem('cache_data');
      setSettings(prev => ({ ...prev, cacheSize: 0 }));
      setDialogVisible(prev => ({ ...prev, clearCache: false }));
      Alert.alert('成功', '缓存已清除');
    } catch (error) {
      Alert.alert('失败', '清除缓存失败');
    }
  };

  // 重置所有设置
  const handleResetSettings = () => {
    Alert.alert(
      '确认重置',
      '确定要重置所有设置到默认值吗？此操作不可恢复。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '重置',
          style: 'destructive',
          onPress: async () => {
            try {
              await AsyncStorage.multiRemove([
                'notification_enabled',
                'dark_mode',
                'auto_sync',
                'data_usage',
                'language',
                'sync_interval',
              ]);
              await loadSettings();
              Alert.alert('成功', '设置已重置');
            } catch (error) {
              Alert.alert('失败', '重置设置失败');
            }
          },
        },
      ]
    );
  };

  // 获取选项显示文本
  const getOptionText = (options, value) => {
    const option = options.find(opt => opt.value === value);
    return option ? option.label : value;
  };

  if (loading) {
    return <LoadingSpinner visible={true} text="加载设置..." />;
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* 通知设置 */}
      <Card style={styles.sectionCard}>
        <Text style={styles.sectionTitle}>通知设置</Text>
        
        <List.Item
          title="推送通知"
          description="接收重要消息和预警通知"
          left={(props) => <List.Icon {...props} icon="notifications" />}
          right={() => (
            <Switch
              value={settings.notifications}
              onValueChange={handleNotificationToggle}
            />
          )}
        />
      </Card>

      {/* 外观设置 */}
      <Card style={styles.sectionCard}>
        <Text style={styles.sectionTitle}>外观设置</Text>
        
        <List.Item
          title="深色模式"
          description="使用深色主题界面"
          left={(props) => <List.Icon {...props} icon="theme-light-dark" />}
          right={() => (
            <Switch
              value={settings.darkMode}
              onValueChange={handleDarkModeToggle}
            />
          )}
        />
        
        <Divider />
        
        <List.Item
          title="语言设置"
          description={getOptionText(languageOptions, settings.language)}
          left={(props) => <List.Icon {...props} icon="translate" />}
          right={(props) => <List.Icon {...props} icon="chevron-right" />}
          onPress={() => setDialogVisible(prev => ({ ...prev, language: true }))}
        />
      </Card>

      {/* 数据同步 */}
      <Card style={styles.sectionCard}>
        <Text style={styles.sectionTitle}>数据同步</Text>
        
        <List.Item
          title="自动同步"
          description="自动同步数据到云端"
          left={(props) => <List.Icon {...props} icon="sync" />}
          right={() => (
            <Switch
              value={settings.autoSync}
              onValueChange={handleAutoSyncToggle}
            />
          )}
        />
        
        <Divider />
        
        <List.Item
          title="数据使用"
          description={getOptionText(dataUsageOptions, settings.dataUsage)}
          left={(props) => <List.Icon {...props} icon="data-usage" />}
          right={(props) => <List.Icon {...props} icon="chevron-right" />}
          onPress={() => setDialogVisible(prev => ({ ...prev, dataUsage: true }))}
        />
        
        <Divider />
        
        <List.Item
          title="同步间隔"
          description={getOptionText(syncIntervalOptions, settings.syncInterval)}
          left={(props) => <List.Icon {...props} icon="schedule" />}
          right={(props) => <List.Icon {...props} icon="chevron-right" />}
          onPress={() => setDialogVisible(prev => ({ ...prev, syncInterval: true }))}
        />
      </Card>

      {/* 存储管理 */}
      <Card style={styles.sectionCard}>
        <Text style={styles.sectionTitle}>存储管理</Text>
        
        <List.Item
          title="缓存大小"
          description={formatFileSize(settings.cacheSize)}
          left={(props) => <List.Icon {...props} icon="storage" />}
          right={(props) => <List.Icon {...props} icon="chevron-right" />}
          onPress={() => setDialogVisible(prev => ({ ...prev, clearCache: true }))}
        />
      </Card>

      {/* 高级设置 */}
      <Card style={styles.sectionCard}>
        <Text style={styles.sectionTitle}>高级设置</Text>
        
        <List.Item
          title="重置设置"
          description="恢复所有设置到默认值"
          left={(props) => <List.Icon {...props} icon="restore" />}
          right={(props) => <List.Icon {...props} icon="chevron-right" />}
          onPress={handleResetSettings}
        />
      </Card>

      {/* 对话框 */}
      <Portal>
        {/* 数据使用对话框 */}
        <Dialog
          visible={dialogVisible.dataUsage}
          onDismiss={() => setDialogVisible(prev => ({ ...prev, dataUsage: false }))}
        >
          <Dialog.Title>数据使用策略</Dialog.Title>
          <Dialog.Content>
            <RadioButton.Group
              onValueChange={handleDataUsageChange}
              value={settings.dataUsage}
            >
              {dataUsageOptions.map((option) => (
                <View key={option.value}>
                  <RadioButton.Item
                    label={option.label}
                    value={option.value}
                  />
                  <Text style={styles.optionDescription}>
                    {option.description}
                  </Text>
                </View>
              ))}
            </RadioButton.Group>
          </Dialog.Content>
        </Dialog>

        {/* 语言对话框 */}
        <Dialog
          visible={dialogVisible.language}
          onDismiss={() => setDialogVisible(prev => ({ ...prev, language: false }))}
        >
          <Dialog.Title>选择语言</Dialog.Title>
          <Dialog.Content>
            <RadioButton.Group
              onValueChange={handleLanguageChange}
              value={settings.language}
            >
              {languageOptions.map((option) => (
                <RadioButton.Item
                  key={option.value}
                  label={option.label}
                  value={option.value}
                />
              ))}
            </RadioButton.Group>
          </Dialog.Content>
        </Dialog>

        {/* 同步间隔对话框 */}
        <Dialog
          visible={dialogVisible.syncInterval}
          onDismiss={() => setDialogVisible(prev => ({ ...prev, syncInterval: false }))}
        >
          <Dialog.Title>同步间隔</Dialog.Title>
          <Dialog.Content>
            <RadioButton.Group
              onValueChange={handleSyncIntervalChange}
              value={settings.syncInterval}
            >
              {syncIntervalOptions.map((option) => (
                <RadioButton.Item
                  key={option.value}
                  label={option.label}
                  value={option.value}
                />
              ))}
            </RadioButton.Group>
          </Dialog.Content>
        </Dialog>

        {/* 清除缓存对话框 */}
        <Dialog
          visible={dialogVisible.clearCache}
          onDismiss={() => setDialogVisible(prev => ({ ...prev, clearCache: false }))}
        >
          <Dialog.Title>清除缓存</Dialog.Title>
          <Dialog.Content>
            <Text>当前缓存大小: {formatFileSize(settings.cacheSize)}</Text>
            <Text style={styles.warningText}>
              清除缓存将删除所有本地存储的临时数据，可能需要重新下载一些内容。
            </Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setDialogVisible(prev => ({ ...prev, clearCache: false }))}>
              取消
            </Button>
            <Button onPress={handleClearCache}>清除</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  
  sectionCard: {
    margin: theme.custom.spacing.md,
    marginBottom: 0,
    elevation: 2,
  },
  
  sectionTitle: {
    fontSize: theme.custom.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text,
    padding: theme.custom.spacing.md,
    paddingBottom: theme.custom.spacing.sm,
  },
  
  optionDescription: {
    fontSize: theme.custom.fontSizes.sm,
    color: theme.custom.colors.textSecondary,
    marginLeft: theme.custom.spacing.xl,
    marginBottom: theme.custom.spacing.sm,
  },
  
  warningText: {
    fontSize: theme.custom.fontSizes.sm,
    color: theme.custom.colors.warning,
    marginTop: theme.custom.spacing.sm,
  },
});

export default SettingsScreen;
