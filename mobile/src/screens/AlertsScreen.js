import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Chip,
  Searchbar,
  Menu,
  IconButton,
  SegmentedButtons,
  Badge,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useFocusEffect } from '@react-navigation/native';

import { theme } from '../styles/theme';
import LoadingSpinner, { EmptyState } from '../components/common/LoadingSpinner';
import ErrorMessage from '../components/common/ErrorMessage';
import { formatDateTime, formatRelativeTime, getStatusColor } from '../utils';
import { getAlerts, markAlertAsRead, deleteAlert, resolveAlert } from '../services/api';

const AlertsScreen = ({ navigation }) => {
  const [alerts, setAlerts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredAlerts, setFilteredAlerts] = useState([]);
  const [filterStatus, setFilterStatus] = useState('all');
  const [menuVisible, setMenuVisible] = useState({});

  // 状态过滤选项
  const statusOptions = [
    { value: 'all', label: '全部' },
    { value: 'unread', label: '未读' },
    { value: 'active', label: '活跃' },
    { value: 'resolved', label: '已解决' },
  ];

  // 获取预警列表
  const fetchAlerts = useCallback(async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);
      setError(null);
      
      const response = await getAlerts({
        status: filterStatus === 'all' ? undefined : filterStatus,
      });
      setAlerts(response.data.alerts || []);
    } catch (err) {
      setError(err.message || '获取预警列表失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [filterStatus]);

  // 下拉刷新
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchAlerts(false);
  }, [fetchAlerts]);

  // 搜索和过滤
  useEffect(() => {
    let filtered = alerts;
    
    // 搜索过滤
    if (searchQuery.trim()) {
      filtered = filtered.filter(alert =>
        alert.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        alert.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
        alert.source?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    setFilteredAlerts(filtered);
  }, [alerts, searchQuery]);

  // 页面聚焦时刷新数据
  useFocusEffect(
    useCallback(() => {
      fetchAlerts();
    }, [fetchAlerts])
  );

  // 状态过滤改变时重新获取数据
  useEffect(() => {
    fetchAlerts();
  }, [filterStatus, fetchAlerts]);

  // 标记为已读
  const handleMarkAsRead = async (alertId) => {
    try {
      await markAlertAsRead(alertId);
      setAlerts(prev => prev.map(alert => 
        alert._id === alertId 
          ? { ...alert, isRead: true, readAt: new Date() }
          : alert
      ));
    } catch (err) {
      Alert.alert('操作失败', err.message || '标记已读失败');
    }
  };

  // 解决预警
  const handleResolveAlert = async (alertId, resolution) => {
    try {
      await resolveAlert(alertId, { resolution });
      setAlerts(prev => prev.map(alert => 
        alert._id === alertId 
          ? { ...alert, status: 'resolved', resolvedAt: new Date(), resolution }
          : alert
      ));
    } catch (err) {
      Alert.alert('操作失败', err.message || '解决预警失败');
    }
  };

  // 删除预警
  const handleDeleteAlert = (alertId, alertTitle) => {
    Alert.alert(
      '确认删除',
      `确定要删除预警"${alertTitle}"吗？`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteAlert(alertId);
              setAlerts(prev => prev.filter(alert => alert._id !== alertId));
            } catch (err) {
              Alert.alert('删除失败', err.message || '删除预警失败');
            }
          },
        },
      ]
    );
  };

  // 切换菜单显示
  const toggleMenu = (alertId) => {
    setMenuVisible(prev => ({
      ...prev,
      [alertId]: !prev[alertId],
    }));
  };

  // 获取预警级别配置
  const getAlertLevelConfig = (level) => {
    const configs = {
      critical: {
        color: theme.custom.colors.error,
        icon: 'error',
        text: '严重',
        bgColor: theme.custom.colors.error + '10',
      },
      warning: {
        color: theme.custom.colors.warning,
        icon: 'warning',
        text: '警告',
        bgColor: theme.custom.colors.warning + '10',
      },
      info: {
        color: theme.custom.colors.info,
        icon: 'info',
        text: '信息',
        bgColor: theme.custom.colors.info + '10',
      },
    };
    return configs[level] || configs.info;
  };

  // 获取预警类型图标
  const getAlertTypeIcon = (type) => {
    const iconMap = {
      device: 'device-hub',
      sensor: 'sensors',
      weather: 'wb-sunny',
      system: 'settings',
      security: 'security',
      default: 'notifications',
    };
    return iconMap[type] || iconMap.default;
  };

  // 渲染预警卡片
  const renderAlertCard = ({ item: alert }) => {
    const levelConfig = getAlertLevelConfig(alert.level);
    const typeIcon = getAlertTypeIcon(alert.type);
    const isUnread = !alert.isRead;

    return (
      <Card style={[
        styles.alertCard,
        isUnread && styles.unreadCard,
        { borderLeftColor: levelConfig.color }
      ]}>
        <TouchableOpacity
          onPress={() => {
            if (isUnread) {
              handleMarkAsRead(alert._id);
            }
            navigation.navigate('AlertDetail', { alertId: alert._id });
          }}
          activeOpacity={0.7}
        >
          <View style={styles.cardHeader}>
            <View style={styles.alertIcon}>
              <Icon
                name={typeIcon}
                size={24}
                color={levelConfig.color}
              />
              {isUnread && (
                <Badge
                  size={8}
                  style={[styles.unreadBadge, { backgroundColor: theme.colors.primary }]}
                />
              )}
            </View>
            
            <View style={styles.alertInfo}>
              <View style={styles.titleRow}>
                <Text style={[styles.alertTitle, isUnread && styles.unreadTitle]}>
                  {alert.title}
                </Text>
                <Chip
                  mode="outlined"
                  textStyle={[styles.levelText, { color: levelConfig.color }]}
                  style={[styles.levelChip, { borderColor: levelConfig.color }]}
                >
                  {levelConfig.text}
                </Chip>
              </View>
              
              <Text style={styles.alertMessage} numberOfLines={2}>
                {alert.message}
              </Text>
              
              <View style={styles.alertMeta}>
                <Text style={styles.metaText}>
                  来源: {alert.source || '系统'}
                </Text>
                <Text style={styles.metaText}>
                  {formatRelativeTime(alert.createdAt)}
                </Text>
              </View>
            </View>
            
            <Menu
              visible={menuVisible[alert._id] || false}
              onDismiss={() => toggleMenu(alert._id)}
              anchor={
                <IconButton
                  icon="more-vert"
                  size={20}
                  onPress={() => toggleMenu(alert._id)}
                />
              }
            >
              {isUnread && (
                <Menu.Item
                  onPress={() => {
                    toggleMenu(alert._id);
                    handleMarkAsRead(alert._id);
                  }}
                  title="标记已读"
                  leadingIcon="mark-email-read"
                />
              )}
              
              {alert.status !== 'resolved' && (
                <Menu.Item
                  onPress={() => {
                    toggleMenu(alert._id);
                    Alert.prompt(
                      '解决预警',
                      '请输入解决方案:',
                      [
                        { text: '取消', style: 'cancel' },
                        {
                          text: '确定',
                          onPress: (resolution) => {
                            if (resolution?.trim()) {
                              handleResolveAlert(alert._id, resolution);
                            }
                          },
                        },
                      ],
                      'plain-text'
                    );
                  }}
                  title="标记解决"
                  leadingIcon="check-circle"
                />
              )}
              
              <Menu.Item
                onPress={() => {
                  toggleMenu(alert._id);
                  handleDeleteAlert(alert._id, alert.title);
                }}
                title="删除"
                leadingIcon="delete"
              />
            </Menu>
          </View>

          {/* 状态指示器 */}
          <View style={styles.statusIndicators}>
            {alert.status === 'resolved' && (
              <Chip
                mode="flat"
                icon="check-circle"
                textStyle={styles.resolvedText}
                style={styles.resolvedChip}
              >
                已解决
              </Chip>
            )}
            
            {alert.isRead && (
              <Chip
                mode="flat"
                icon="visibility"
                textStyle={styles.readText}
                style={styles.readChip}
              >
                已读
              </Chip>
            )}
          </View>

          {/* 时间信息 */}
          <View style={styles.timeInfo}>
            <Text style={styles.timeText}>
              创建时间: {formatDateTime(alert.createdAt)}
            </Text>
            {alert.resolvedAt && (
              <Text style={styles.timeText}>
                解决时间: {formatDateTime(alert.resolvedAt)}
              </Text>
            )}
          </View>
        </TouchableOpacity>
      </Card>
    );
  };

  // 渲染空状态
  const renderEmptyState = () => (
    <EmptyState
      icon="notifications"
      title="暂无预警"
      message={
        filterStatus === 'all' 
          ? "当前没有任何预警信息" 
          : `当前没有${statusOptions.find(opt => opt.value === filterStatus)?.label}的预警`
      }
    />
  );

  if (loading) {
    return <LoadingSpinner visible={true} text="加载预警列表..." />;
  }

  if (error) {
    return (
      <ErrorMessage
        visible={true}
        title="加载失败"
        message={error}
        onRetry={() => fetchAlerts()}
        fullScreen={true}
      />
    );
  }

  return (
    <View style={styles.container}>
      {/* 搜索栏 */}
      <Searchbar
        placeholder="搜索预警标题或内容"
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchbar}
      />

      {/* 状态过滤 */}
      <View style={styles.filterContainer}>
        <SegmentedButtons
          value={filterStatus}
          onValueChange={setFilterStatus}
          buttons={statusOptions}
          style={styles.segmentedButtons}
        />
      </View>

      {/* 预警列表 */}
      <FlatList
        data={filteredAlerts}
        renderItem={renderAlertCard}
        keyExtractor={(item) => item._id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
          />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  
  searchbar: {
    margin: theme.custom.spacing.md,
    elevation: 2,
  },
  
  filterContainer: {
    paddingHorizontal: theme.custom.spacing.md,
    paddingBottom: theme.custom.spacing.sm,
  },
  
  segmentedButtons: {
    backgroundColor: theme.colors.surface,
  },
  
  listContainer: {
    flexGrow: 1,
    paddingHorizontal: theme.custom.spacing.sm,
    paddingBottom: theme.custom.spacing.xl,
  },
  
  alertCard: {
    marginVertical: theme.custom.spacing.xs,
    marginHorizontal: theme.custom.spacing.xs,
    elevation: 2,
    borderLeftWidth: 4,
  },
  
  unreadCard: {
    backgroundColor: theme.colors.primary + '05',
  },
  
  cardHeader: {
    flexDirection: 'row',
    padding: theme.custom.spacing.md,
    alignItems: 'flex-start',
  },
  
  alertIcon: {
    position: 'relative',
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.custom.spacing.md,
    elevation: 1,
  },
  
  unreadBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
  },
  
  alertInfo: {
    flex: 1,
  },
  
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.custom.spacing.xs,
  },
  
  alertTitle: {
    flex: 1,
    fontSize: theme.custom.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text,
    marginRight: theme.custom.spacing.sm,
  },
  
  unreadTitle: {
    fontWeight: '600',
    color: theme.colors.primary,
  },
  
  levelChip: {
    height: 24,
  },
  
  levelText: {
    fontSize: theme.custom.fontSizes.xs,
  },
  
  alertMessage: {
    fontSize: theme.custom.fontSizes.sm,
    color: theme.custom.colors.textSecondary,
    lineHeight: 18,
    marginBottom: theme.custom.spacing.sm,
  },
  
  alertMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  
  metaText: {
    fontSize: theme.custom.fontSizes.xs,
    color: theme.custom.colors.textSecondary,
  },
  
  statusIndicators: {
    flexDirection: 'row',
    paddingHorizontal: theme.custom.spacing.md,
    paddingBottom: theme.custom.spacing.sm,
    gap: theme.custom.spacing.sm,
  },
  
  resolvedChip: {
    backgroundColor: theme.custom.colors.success + '20',
    height: 24,
  },
  
  resolvedText: {
    fontSize: theme.custom.fontSizes.xs,
    color: theme.custom.colors.success,
  },
  
  readChip: {
    backgroundColor: theme.custom.colors.info + '20',
    height: 24,
  },
  
  readText: {
    fontSize: theme.custom.fontSizes.xs,
    color: theme.custom.colors.info,
  },
  
  timeInfo: {
    paddingHorizontal: theme.custom.spacing.md,
    paddingBottom: theme.custom.spacing.md,
    borderTopWidth: 1,
    borderTopColor: theme.custom.colors.divider,
    marginTop: theme.custom.spacing.sm,
    paddingTop: theme.custom.spacing.sm,
  },
  
  timeText: {
    fontSize: theme.custom.fontSizes.xs,
    color: theme.custom.colors.textSecondary,
    marginBottom: theme.custom.spacing.xs,
  },
});

export default AlertsScreen;
