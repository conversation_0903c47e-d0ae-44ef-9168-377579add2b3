import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Image,
} from 'react-native';
import {
  Text,
  Card,
  List,
  Avatar,
  Button,
  Divider,
  Switch,
  Badge,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useFocusEffect } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { theme } from '../styles/theme';
import LoadingSpinner from '../components/common/LoadingSpinner';
import ErrorMessage from '../components/common/ErrorMessage';
import { formatDateTime } from '../utils';
import { getUserProfile, logout } from '../services/api';
import NotificationService from '../services/NotificationService';

const ProfileScreen = ({ navigation }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [notificationEnabled, setNotificationEnabled] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  const [autoSync, setAutoSync] = useState(true);

  // 获取用户信息
  const fetchUserProfile = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await getUserProfile();
      setUser(response.data.user);
      
      // 加载设置
      await loadSettings();
    } catch (err) {
      setError(err.message || '获取用户信息失败');
    } finally {
      setLoading(false);
    }
  }, []);

  // 加载设置
  const loadSettings = async () => {
    try {
      const settings = await AsyncStorage.multiGet([
        'notification_enabled',
        'dark_mode',
        'auto_sync',
      ]);
      
      settings.forEach(([key, value]) => {
        if (value !== null) {
          const boolValue = value === 'true';
          switch (key) {
            case 'notification_enabled':
              setNotificationEnabled(boolValue);
              break;
            case 'dark_mode':
              setDarkMode(boolValue);
              break;
            case 'auto_sync':
              setAutoSync(boolValue);
              break;
          }
        }
      });
    } catch (error) {
      console.error('加载设置失败:', error);
    }
  };

  // 保存设置
  const saveSetting = async (key, value) => {
    try {
      await AsyncStorage.setItem(key, value.toString());
    } catch (error) {
      console.error('保存设置失败:', error);
    }
  };

  // 页面聚焦时刷新数据
  useFocusEffect(
    useCallback(() => {
      fetchUserProfile();
    }, [fetchUserProfile])
  );

  // 切换通知设置
  const handleNotificationToggle = async (enabled) => {
    setNotificationEnabled(enabled);
    await saveSetting('notification_enabled', enabled);
    
    if (enabled) {
      const permissions = await NotificationService.requestPermissions();
      if (!permissions.alert) {
        Alert.alert(
          '权限提醒',
          '请在系统设置中开启通知权限以接收重要消息',
          [
            { text: '取消' },
            { text: '去设置', onPress: () => {/* 打开系统设置 */} },
          ]
        );
      }
    }
  };

  // 切换深色模式
  const handleDarkModeToggle = async (enabled) => {
    setDarkMode(enabled);
    await saveSetting('dark_mode', enabled);
    // TODO: 实现主题切换
    Alert.alert('提示', '深色模式将在下次启动时生效');
  };

  // 切换自动同步
  const handleAutoSyncToggle = async (enabled) => {
    setAutoSync(enabled);
    await saveSetting('auto_sync', enabled);
  };

  // 退出登录
  const handleLogout = () => {
    Alert.alert(
      '确认退出',
      '确定要退出登录吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '退出',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
              await AsyncStorage.multiRemove([
                'user_token',
                'user_info',
                'notification_enabled',
                'dark_mode',
                'auto_sync',
              ]);
              navigation.reset({
                index: 0,
                routes: [{ name: 'Login' }],
              });
            } catch (error) {
              Alert.alert('退出失败', error.message || '退出登录失败');
            }
          },
        },
      ]
    );
  };

  // 获取用户角色文本
  const getRoleText = (role) => {
    const roleMap = {
      admin: '管理员',
      manager: '经理',
      worker: '工作人员',
      viewer: '查看者',
    };
    return roleMap[role] || role;
  };

  // 获取用户角色颜色
  const getRoleColor = (role) => {
    const colorMap = {
      admin: theme.custom.colors.error,
      manager: theme.custom.colors.warning,
      worker: theme.custom.colors.primary,
      viewer: theme.custom.colors.info,
    };
    return colorMap[role] || theme.custom.colors.textSecondary;
  };

  if (loading) {
    return <LoadingSpinner visible={true} text="加载用户信息..." />;
  }

  if (error) {
    return (
      <ErrorMessage
        visible={true}
        title="加载失败"
        message={error}
        onRetry={fetchUserProfile}
        fullScreen={true}
      />
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* 用户信息卡片 */}
      <Card style={styles.userCard}>
        <View style={styles.userHeader}>
          <TouchableOpacity
            style={styles.avatarContainer}
            onPress={() => navigation.navigate('EditProfile')}
          >
            {user?.avatar ? (
              <Image source={{ uri: user.avatar }} style={styles.avatar} />
            ) : (
              <Avatar.Text
                size={80}
                label={user?.name?.charAt(0) || 'U'}
                style={styles.avatarText}
              />
            )}
            <View style={styles.editBadge}>
              <Icon name="edit" size={16} color={theme.colors.surface} />
            </View>
          </TouchableOpacity>
          
          <View style={styles.userInfo}>
            <Text style={styles.userName}>{user?.name || '未设置姓名'}</Text>
            <Text style={styles.userEmail}>{user?.email}</Text>
            <View style={styles.roleContainer}>
              <Badge
                style={[styles.roleBadge, { backgroundColor: getRoleColor(user?.role) }]}
              >
                {getRoleText(user?.role)}
              </Badge>
            </View>
          </View>
        </View>
        
        <View style={styles.userStats}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{user?.loginCount || 0}</Text>
            <Text style={styles.statLabel}>登录次数</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {user?.lastLoginAt ? formatDateTime(user.lastLoginAt).split(' ')[0] : '从未'}
            </Text>
            <Text style={styles.statLabel}>最后登录</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{user?.department || '未设置'}</Text>
            <Text style={styles.statLabel}>部门</Text>
          </View>
        </View>
      </Card>

      {/* 账户管理 */}
      <Card style={styles.sectionCard}>
        <Text style={styles.sectionTitle}>账户管理</Text>
        
        <List.Item
          title="个人信息"
          description="编辑个人资料和联系方式"
          left={(props) => <List.Icon {...props} icon="account-edit" />}
          right={(props) => <List.Icon {...props} icon="chevron-right" />}
          onPress={() => navigation.navigate('EditProfile')}
        />
        
        <Divider />
        
        <List.Item
          title="修改密码"
          description="更改登录密码"
          left={(props) => <List.Icon {...props} icon="lock-reset" />}
          right={(props) => <List.Icon {...props} icon="chevron-right" />}
          onPress={() => navigation.navigate('ChangePassword')}
        />
        
        <Divider />
        
        <List.Item
          title="安全设置"
          description="账户安全和隐私设置"
          left={(props) => <List.Icon {...props} icon="security" />}
          right={(props) => <List.Icon {...props} icon="chevron-right" />}
          onPress={() => navigation.navigate('SecuritySettings')}
        />
      </Card>

      {/* 应用设置 */}
      <Card style={styles.sectionCard}>
        <Text style={styles.sectionTitle}>应用设置</Text>
        
        <List.Item
          title="推送通知"
          description="接收重要消息和预警通知"
          left={(props) => <List.Icon {...props} icon="notifications" />}
          right={() => (
            <Switch
              value={notificationEnabled}
              onValueChange={handleNotificationToggle}
            />
          )}
        />
        
        <Divider />
        
        <List.Item
          title="深色模式"
          description="使用深色主题界面"
          left={(props) => <List.Icon {...props} icon="theme-light-dark" />}
          right={() => (
            <Switch
              value={darkMode}
              onValueChange={handleDarkModeToggle}
            />
          )}
        />
        
        <Divider />
        
        <List.Item
          title="自动同步"
          description="自动同步数据到云端"
          left={(props) => <List.Icon {...props} icon="sync" />}
          right={() => (
            <Switch
              value={autoSync}
              onValueChange={handleAutoSyncToggle}
            />
          )}
        />
        
        <Divider />
        
        <List.Item
          title="语言设置"
          description="选择应用显示语言"
          left={(props) => <List.Icon {...props} icon="translate" />}
          right={(props) => <List.Icon {...props} icon="chevron-right" />}
          onPress={() => navigation.navigate('LanguageSettings')}
        />
      </Card>

      {/* 帮助与支持 */}
      <Card style={styles.sectionCard}>
        <Text style={styles.sectionTitle}>帮助与支持</Text>
        
        <List.Item
          title="使用帮助"
          description="查看应用使用指南"
          left={(props) => <List.Icon {...props} icon="help-circle" />}
          right={(props) => <List.Icon {...props} icon="chevron-right" />}
          onPress={() => navigation.navigate('Help')}
        />
        
        <Divider />
        
        <List.Item
          title="意见反馈"
          description="提交问题和建议"
          left={(props) => <List.Icon {...props} icon="message-text" />}
          right={(props) => <List.Icon {...props} icon="chevron-right" />}
          onPress={() => navigation.navigate('Feedback')}
        />
        
        <Divider />
        
        <List.Item
          title="关于我们"
          description="应用版本和开发信息"
          left={(props) => <List.Icon {...props} icon="information" />}
          right={(props) => <List.Icon {...props} icon="chevron-right" />}
          onPress={() => navigation.navigate('About')}
        />
      </Card>

      {/* 退出登录 */}
      <View style={styles.logoutContainer}>
        <Button
          mode="outlined"
          icon="logout"
          onPress={handleLogout}
          style={styles.logoutButton}
          labelStyle={styles.logoutButtonText}
        >
          退出登录
        </Button>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  
  userCard: {
    margin: theme.custom.spacing.md,
    elevation: 2,
  },
  
  userHeader: {
    flexDirection: 'row',
    padding: theme.custom.spacing.lg,
    alignItems: 'center',
  },
  
  avatarContainer: {
    position: 'relative',
    marginRight: theme.custom.spacing.lg,
  },
  
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  
  avatarText: {
    backgroundColor: theme.colors.primary,
  },
  
  editBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: theme.colors.surface,
  },
  
  userInfo: {
    flex: 1,
  },
  
  userName: {
    fontSize: theme.custom.fontSizes.xl,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.custom.spacing.xs,
  },
  
  userEmail: {
    fontSize: theme.custom.fontSizes.md,
    color: theme.custom.colors.textSecondary,
    marginBottom: theme.custom.spacing.sm,
  },
  
  roleContainer: {
    alignSelf: 'flex-start',
  },
  
  roleBadge: {
    paddingHorizontal: theme.custom.spacing.sm,
    paddingVertical: theme.custom.spacing.xs,
  },
  
  userStats: {
    flexDirection: 'row',
    paddingHorizontal: theme.custom.spacing.lg,
    paddingBottom: theme.custom.spacing.lg,
    borderTopWidth: 1,
    borderTopColor: theme.custom.colors.divider,
    marginTop: theme.custom.spacing.md,
    paddingTop: theme.custom.spacing.md,
  },
  
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  
  statValue: {
    fontSize: theme.custom.fontSizes.lg,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: theme.custom.spacing.xs,
  },
  
  statLabel: {
    fontSize: theme.custom.fontSizes.sm,
    color: theme.custom.colors.textSecondary,
  },
  
  statDivider: {
    width: 1,
    backgroundColor: theme.custom.colors.divider,
    marginHorizontal: theme.custom.spacing.md,
  },
  
  sectionCard: {
    margin: theme.custom.spacing.md,
    marginTop: 0,
    elevation: 2,
  },
  
  sectionTitle: {
    fontSize: theme.custom.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text,
    padding: theme.custom.spacing.md,
    paddingBottom: theme.custom.spacing.sm,
  },
  
  logoutContainer: {
    padding: theme.custom.spacing.lg,
    paddingBottom: theme.custom.spacing.xl,
  },
  
  logoutButton: {
    borderColor: theme.custom.colors.error,
  },
  
  logoutButtonText: {
    color: theme.custom.colors.error,
  },
});

export default ProfileScreen;
