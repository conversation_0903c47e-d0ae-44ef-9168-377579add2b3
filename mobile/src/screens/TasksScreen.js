import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Chip,
  FAB,
  Searchbar,
  Menu,
  IconButton,
  SegmentedButtons,
  ProgressBar,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useFocusEffect } from '@react-navigation/native';

import { theme } from '../styles/theme';
import LoadingSpinner, { EmptyState } from '../components/common/LoadingSpinner';
import ErrorMessage from '../components/common/ErrorMessage';
import { formatDateTime, formatRelativeTime, getStatusColor } from '../utils';
import { getTasks, updateTaskStatus, deleteTask } from '../services/api';

const TasksScreen = ({ navigation }) => {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredTasks, setFilteredTasks] = useState([]);
  const [filterStatus, setFilterStatus] = useState('all');
  const [menuVisible, setMenuVisible] = useState({});

  // 状态过滤选项
  const statusOptions = [
    { value: 'all', label: '全部' },
    { value: 'pending', label: '待处理' },
    { value: 'in_progress', label: '进行中' },
    { value: 'completed', label: '已完成' },
  ];

  // 获取任务列表
  const fetchTasks = useCallback(async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);
      setError(null);
      
      const response = await getTasks({
        status: filterStatus === 'all' ? undefined : filterStatus,
      });
      setTasks(response.data.tasks || []);
    } catch (err) {
      setError(err.message || '获取任务列表失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [filterStatus]);

  // 下拉刷新
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchTasks(false);
  }, [fetchTasks]);

  // 搜索和过滤
  useEffect(() => {
    let filtered = tasks;
    
    // 搜索过滤
    if (searchQuery.trim()) {
      filtered = filtered.filter(task =>
        task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.type?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    setFilteredTasks(filtered);
  }, [tasks, searchQuery]);

  // 页面聚焦时刷新数据
  useFocusEffect(
    useCallback(() => {
      fetchTasks();
    }, [fetchTasks])
  );

  // 状态过滤改变时重新获取数据
  useEffect(() => {
    fetchTasks();
  }, [filterStatus, fetchTasks]);

  // 更新任务状态
  const handleUpdateStatus = async (taskId, newStatus) => {
    try {
      await updateTaskStatus(taskId, { status: newStatus });
      setTasks(prev => prev.map(task => 
        task._id === taskId 
          ? { ...task, status: newStatus, updatedAt: new Date() }
          : task
      ));
    } catch (err) {
      Alert.alert('操作失败', err.message || '更新任务状态失败');
    }
  };

  // 删除任务
  const handleDeleteTask = (taskId, taskTitle) => {
    Alert.alert(
      '确认删除',
      `确定要删除任务"${taskTitle}"吗？`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteTask(taskId);
              setTasks(prev => prev.filter(task => task._id !== taskId));
            } catch (err) {
              Alert.alert('删除失败', err.message || '删除任务失败');
            }
          },
        },
      ]
    );
  };

  // 切换菜单显示
  const toggleMenu = (taskId) => {
    setMenuVisible(prev => ({
      ...prev,
      [taskId]: !prev[taskId],
    }));
  };

  // 获取任务优先级配置
  const getPriorityConfig = (priority) => {
    const configs = {
      high: {
        color: theme.custom.colors.error,
        text: '高',
        bgColor: theme.custom.colors.error + '10',
      },
      medium: {
        color: theme.custom.colors.warning,
        text: '中',
        bgColor: theme.custom.colors.warning + '10',
      },
      low: {
        color: theme.custom.colors.info,
        text: '低',
        bgColor: theme.custom.colors.info + '10',
      },
    };
    return configs[priority] || configs.medium;
  };

  // 获取任务类型图标
  const getTaskTypeIcon = (type) => {
    const iconMap = {
      irrigation: 'water-drop',
      fertilization: 'eco',
      harvesting: 'agriculture',
      planting: 'local-florist',
      maintenance: 'build',
      inspection: 'search',
      default: 'assignment',
    };
    return iconMap[type] || iconMap.default;
  };

  // 获取任务状态文本
  const getStatusText = (status) => {
    const statusMap = {
      pending: '待处理',
      in_progress: '进行中',
      completed: '已完成',
      cancelled: '已取消',
    };
    return statusMap[status] || status;
  };

  // 计算任务进度
  const getTaskProgress = (task) => {
    if (task.status === 'completed') return 1;
    if (task.status === 'cancelled') return 0;
    if (task.status === 'pending') return 0;
    
    // 根据时间计算进度
    const now = new Date();
    const start = new Date(task.startDate || task.createdAt);
    const end = new Date(task.dueDate);
    
    if (now >= end) return 1;
    if (now <= start) return 0;
    
    const total = end - start;
    const elapsed = now - start;
    return Math.min(elapsed / total, 1);
  };

  // 检查任务是否逾期
  const isTaskOverdue = (task) => {
    if (task.status === 'completed' || task.status === 'cancelled') return false;
    return new Date() > new Date(task.dueDate);
  };

  // 渲染任务卡片
  const renderTaskCard = ({ item: task }) => {
    const priorityConfig = getPriorityConfig(task.priority);
    const typeIcon = getTaskTypeIcon(task.type);
    const statusColor = getStatusColor(task.status);
    const statusText = getStatusText(task.status);
    const progress = getTaskProgress(task);
    const isOverdue = isTaskOverdue(task);

    return (
      <Card style={[
        styles.taskCard,
        isOverdue && styles.overdueCard,
        { borderLeftColor: priorityConfig.color }
      ]}>
        <TouchableOpacity
          onPress={() => navigation.navigate('TaskDetail', { taskId: task._id })}
          activeOpacity={0.7}
        >
          <View style={styles.cardHeader}>
            <View style={styles.taskIcon}>
              <Icon
                name={typeIcon}
                size={24}
                color={priorityConfig.color}
              />
            </View>
            
            <View style={styles.taskInfo}>
              <View style={styles.titleRow}>
                <Text style={styles.taskTitle} numberOfLines={1}>
                  {task.title}
                </Text>
                <Chip
                  mode="outlined"
                  textStyle={[styles.priorityText, { color: priorityConfig.color }]}
                  style={[styles.priorityChip, { borderColor: priorityConfig.color }]}
                >
                  {priorityConfig.text}
                </Chip>
              </View>
              
              <Text style={styles.taskDescription} numberOfLines={2}>
                {task.description || '暂无描述'}
              </Text>
              
              <View style={styles.taskMeta}>
                <View style={styles.metaItem}>
                  <Icon name="schedule" size={14} color={theme.custom.colors.textSecondary} />
                  <Text style={styles.metaText}>
                    {formatRelativeTime(task.dueDate)}
                  </Text>
                </View>
                
                {task.assignedTo && (
                  <View style={styles.metaItem}>
                    <Icon name="person" size={14} color={theme.custom.colors.textSecondary} />
                    <Text style={styles.metaText}>
                      {task.assignedTo.name}
                    </Text>
                  </View>
                )}
              </View>
            </View>
            
            <Menu
              visible={menuVisible[task._id] || false}
              onDismiss={() => toggleMenu(task._id)}
              anchor={
                <IconButton
                  icon="more-vert"
                  size={20}
                  onPress={() => toggleMenu(task._id)}
                />
              }
            >
              {task.status === 'pending' && (
                <Menu.Item
                  onPress={() => {
                    toggleMenu(task._id);
                    handleUpdateStatus(task._id, 'in_progress');
                  }}
                  title="开始任务"
                  leadingIcon="play-arrow"
                />
              )}
              
              {task.status === 'in_progress' && (
                <Menu.Item
                  onPress={() => {
                    toggleMenu(task._id);
                    handleUpdateStatus(task._id, 'completed');
                  }}
                  title="完成任务"
                  leadingIcon="check-circle"
                />
              )}
              
              <Menu.Item
                onPress={() => {
                  toggleMenu(task._id);
                  navigation.navigate('EditTask', { taskId: task._id });
                }}
                title="编辑"
                leadingIcon="edit"
              />
              
              <Menu.Item
                onPress={() => {
                  toggleMenu(task._id);
                  handleDeleteTask(task._id, task.title);
                }}
                title="删除"
                leadingIcon="delete"
              />
            </Menu>
          </View>

          {/* 进度条 */}
          {task.status === 'in_progress' && (
            <View style={styles.progressContainer}>
              <ProgressBar
                progress={progress}
                color={isOverdue ? theme.custom.colors.error : theme.colors.primary}
                style={styles.progressBar}
              />
              <Text style={styles.progressText}>
                {Math.round(progress * 100)}%
              </Text>
            </View>
          )}

          {/* 状态和时间信息 */}
          <View style={styles.cardFooter}>
            <Chip
              mode="flat"
              textStyle={[styles.statusText, { color: statusColor }]}
              style={[styles.statusChip, { backgroundColor: statusColor + '20' }]}
            >
              {statusText}
            </Chip>
            
            {isOverdue && (
              <Chip
                mode="flat"
                icon="warning"
                textStyle={styles.overdueText}
                style={styles.overdueChip}
              >
                已逾期
              </Chip>
            )}
            
            <Text style={styles.timeText}>
              截止: {formatDateTime(task.dueDate)}
            </Text>
          </View>
        </TouchableOpacity>
      </Card>
    );
  };

  // 渲染空状态
  const renderEmptyState = () => (
    <EmptyState
      icon="assignment"
      title="暂无任务"
      message={
        filterStatus === 'all' 
          ? "当前没有任何任务，点击右下角按钮创建新任务" 
          : `当前没有${statusOptions.find(opt => opt.value === filterStatus)?.label}的任务`
      }
      actionText={filterStatus === 'all' ? "创建任务" : undefined}
      onAction={filterStatus === 'all' ? () => navigation.navigate('AddTask') : undefined}
    />
  );

  if (loading) {
    return <LoadingSpinner visible={true} text="加载任务列表..." />;
  }

  if (error) {
    return (
      <ErrorMessage
        visible={true}
        title="加载失败"
        message={error}
        onRetry={() => fetchTasks()}
        fullScreen={true}
      />
    );
  }

  return (
    <View style={styles.container}>
      {/* 搜索栏 */}
      <Searchbar
        placeholder="搜索任务标题或描述"
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchbar}
      />

      {/* 状态过滤 */}
      <View style={styles.filterContainer}>
        <SegmentedButtons
          value={filterStatus}
          onValueChange={setFilterStatus}
          buttons={statusOptions}
          style={styles.segmentedButtons}
        />
      </View>

      {/* 任务列表 */}
      <FlatList
        data={filteredTasks}
        renderItem={renderTaskCard}
        keyExtractor={(item) => item._id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
          />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />

      {/* 添加按钮 */}
      <FAB
        icon="add"
        style={styles.fab}
        onPress={() => navigation.navigate('AddTask')}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  
  searchbar: {
    margin: theme.custom.spacing.md,
    elevation: 2,
  },
  
  filterContainer: {
    paddingHorizontal: theme.custom.spacing.md,
    paddingBottom: theme.custom.spacing.sm,
  },
  
  segmentedButtons: {
    backgroundColor: theme.colors.surface,
  },
  
  listContainer: {
    flexGrow: 1,
    paddingHorizontal: theme.custom.spacing.sm,
    paddingBottom: theme.custom.spacing.xl,
  },
  
  taskCard: {
    marginVertical: theme.custom.spacing.xs,
    marginHorizontal: theme.custom.spacing.xs,
    elevation: 2,
    borderLeftWidth: 4,
  },
  
  overdueCard: {
    backgroundColor: theme.custom.colors.error + '05',
  },
  
  cardHeader: {
    flexDirection: 'row',
    padding: theme.custom.spacing.md,
    alignItems: 'flex-start',
  },
  
  taskIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.custom.spacing.md,
    elevation: 1,
  },
  
  taskInfo: {
    flex: 1,
  },
  
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.custom.spacing.xs,
  },
  
  taskTitle: {
    flex: 1,
    fontSize: theme.custom.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.text,
    marginRight: theme.custom.spacing.sm,
  },
  
  priorityChip: {
    height: 24,
  },
  
  priorityText: {
    fontSize: theme.custom.fontSizes.xs,
  },
  
  taskDescription: {
    fontSize: theme.custom.fontSizes.sm,
    color: theme.custom.colors.textSecondary,
    lineHeight: 18,
    marginBottom: theme.custom.spacing.sm,
  },
  
  taskMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  metaText: {
    fontSize: theme.custom.fontSizes.xs,
    color: theme.custom.colors.textSecondary,
    marginLeft: theme.custom.spacing.xs,
  },
  
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.custom.spacing.md,
    paddingBottom: theme.custom.spacing.sm,
  },
  
  progressBar: {
    flex: 1,
    height: 4,
    marginRight: theme.custom.spacing.sm,
  },
  
  progressText: {
    fontSize: theme.custom.fontSizes.xs,
    color: theme.custom.colors.textSecondary,
    minWidth: 30,
  },
  
  cardFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.custom.spacing.md,
    paddingBottom: theme.custom.spacing.md,
    borderTopWidth: 1,
    borderTopColor: theme.custom.colors.divider,
    marginTop: theme.custom.spacing.sm,
    paddingTop: theme.custom.spacing.sm,
    flexWrap: 'wrap',
    gap: theme.custom.spacing.sm,
  },
  
  statusChip: {
    height: 24,
  },
  
  statusText: {
    fontSize: theme.custom.fontSizes.xs,
  },
  
  overdueChip: {
    backgroundColor: theme.custom.colors.error + '20',
    height: 24,
  },
  
  overdueText: {
    fontSize: theme.custom.fontSizes.xs,
    color: theme.custom.colors.error,
  },
  
  timeText: {
    fontSize: theme.custom.fontSizes.xs,
    color: theme.custom.colors.textSecondary,
    marginLeft: 'auto',
  },
  
  fab: {
    position: 'absolute',
    margin: theme.custom.spacing.md,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.primary,
  },
});

export default TasksScreen;
