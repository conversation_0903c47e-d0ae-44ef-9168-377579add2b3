import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {
  Text,
  Card,
  Chip,
  SegmentedButtons,
  Surface,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { LineChart } from 'react-native-chart-kit';
import { useFocusEffect } from '@react-navigation/native';

import { theme } from '../styles/theme';
import LoadingSpinner, { EmptyState } from '../components/common/LoadingSpinner';
import ErrorMessage from '../components/common/ErrorMessage';
import { formatDateTime, formatNumber } from '../utils';
import { getSensorData, getDevices } from '../services/api';

const { width: screenWidth } = Dimensions.get('window');

const SensorsScreen = ({ navigation }) => {
  const [sensors, setSensors] = useState([]);
  const [sensorData, setSensorData] = useState({});
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [timeRange, setTimeRange] = useState('24h');
  const [selectedSensor, setSelectedSensor] = useState(null);

  // 时间范围选项
  const timeRangeOptions = [
    { value: '1h', label: '1小时' },
    { value: '24h', label: '24小时' },
    { value: '7d', label: '7天' },
    { value: '30d', label: '30天' },
  ];

  // 获取传感器列表
  const fetchSensors = useCallback(async () => {
    try {
      const response = await getDevices({ type: 'sensor' });
      const sensorDevices = response.data.devices || [];
      setSensors(sensorDevices);
      
      if (sensorDevices.length > 0 && !selectedSensor) {
        setSelectedSensor(sensorDevices[0]);
      }
    } catch (err) {
      setError(err.message || '获取传感器列表失败');
    }
  }, [selectedSensor]);

  // 获取传感器数据
  const fetchSensorData = useCallback(async (showLoading = true) => {
    if (!selectedSensor) return;
    
    try {
      if (showLoading) setLoading(true);
      setError(null);
      
      const response = await getSensorData(selectedSensor._id, {
        timeRange,
        limit: 100,
      });
      
      setSensorData(prev => ({
        ...prev,
        [selectedSensor._id]: response.data.sensorData || [],
      }));
    } catch (err) {
      setError(err.message || '获取传感器数据失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [selectedSensor, timeRange]);

  // 下拉刷新
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchSensorData(false);
  }, [fetchSensorData]);

  // 页面聚焦时刷新数据
  useFocusEffect(
    useCallback(() => {
      fetchSensors();
    }, [fetchSensors])
  );

  // 当选中传感器或时间范围改变时获取数据
  useEffect(() => {
    if (selectedSensor) {
      fetchSensorData();
    }
  }, [selectedSensor, timeRange, fetchSensorData]);

  // 获取传感器数据类型配置
  const getSensorConfig = (dataType) => {
    const configs = {
      temperature: {
        label: '温度',
        unit: '°C',
        icon: 'thermostat',
        color: '#FF6B6B',
        min: -10,
        max: 50,
      },
      humidity: {
        label: '湿度',
        unit: '%',
        icon: 'water-drop',
        color: '#4ECDC4',
        min: 0,
        max: 100,
      },
      soilMoisture: {
        label: '土壤湿度',
        unit: '%',
        icon: 'grass',
        color: '#45B7D1',
        min: 0,
        max: 100,
      },
      ph: {
        label: 'pH值',
        unit: '',
        icon: 'science',
        color: '#96CEB4',
        min: 0,
        max: 14,
      },
      light: {
        label: '光照强度',
        unit: 'lux',
        icon: 'wb-sunny',
        color: '#FFEAA7',
        min: 0,
        max: 100000,
      },
    };
    return configs[dataType] || {
      label: dataType,
      unit: '',
      icon: 'sensors',
      color: '#DDA0DD',
      min: 0,
      max: 100,
    };
  };

  // 处理图表数据
  const getChartData = (dataType) => {
    const data = sensorData[selectedSensor?._id] || [];
    const config = getSensorConfig(dataType);
    
    if (data.length === 0) {
      return null;
    }

    const values = data.map(item => item.data[dataType] || 0);
    const labels = data.map(item => {
      const date = new Date(item.timestamp);
      return timeRange === '1h' || timeRange === '24h' 
        ? date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
        : date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
    });

    return {
      labels: labels.length > 10 ? labels.filter((_, index) => index % Math.ceil(labels.length / 10) === 0) : labels,
      datasets: [{
        data: values.length > 10 ? values.filter((_, index) => index % Math.ceil(values.length / 10) === 0) : values,
        color: () => config.color,
        strokeWidth: 2,
      }],
    };
  };

  // 获取最新数据值
  const getLatestValue = (dataType) => {
    const data = sensorData[selectedSensor?._id] || [];
    if (data.length === 0) return null;
    
    const latest = data[data.length - 1];
    return latest.data[dataType];
  };

  // 渲染传感器选择器
  const renderSensorSelector = () => (
    <Surface style={styles.sensorSelector}>
      <Text style={styles.selectorTitle}>选择传感器</Text>
      <FlatList
        horizontal
        data={sensors}
        keyExtractor={(item) => item._id}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.sensorChip,
              selectedSensor?._id === item._id && styles.selectedSensorChip,
            ]}
            onPress={() => setSelectedSensor(item)}
          >
            <Text
              style={[
                styles.sensorChipText,
                selectedSensor?._id === item._id && styles.selectedSensorChipText,
              ]}
            >
              {item.name}
            </Text>
          </TouchableOpacity>
        )}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.sensorList}
      />
    </Surface>
  );

  // 渲染数据卡片
  const renderDataCard = (dataType) => {
    const config = getSensorConfig(dataType);
    const latestValue = getLatestValue(dataType);
    const chartData = getChartData(dataType);

    if (latestValue === null || latestValue === undefined) {
      return null;
    }

    return (
      <Card key={dataType} style={styles.dataCard}>
        <View style={styles.cardHeader}>
          <View style={styles.dataInfo}>
            <Icon name={config.icon} size={24} color={config.color} />
            <View style={styles.dataText}>
              <Text style={styles.dataLabel}>{config.label}</Text>
              <Text style={[styles.dataValue, { color: config.color }]}>
                {formatNumber(latestValue, 1)}{config.unit}
              </Text>
            </View>
          </View>
        </View>

        {chartData && (
          <View style={styles.chartContainer}>
            <LineChart
              data={chartData}
              width={screenWidth - 60}
              height={120}
              chartConfig={{
                backgroundColor: 'transparent',
                backgroundGradientFrom: theme.colors.surface,
                backgroundGradientTo: theme.colors.surface,
                decimalPlaces: 1,
                color: () => config.color,
                labelColor: () => theme.custom.colors.textSecondary,
                style: {
                  borderRadius: 0,
                },
                propsForDots: {
                  r: '3',
                  strokeWidth: '1',
                  stroke: config.color,
                },
              }}
              bezier
              style={styles.chart}
              withInnerLines={false}
              withOuterLines={false}
              withVerticalLabels={true}
              withHorizontalLabels={true}
            />
          </View>
        )}
      </Card>
    );
  };

  // 渲染空状态
  const renderEmptyState = () => (
    <EmptyState
      icon="sensors"
      title="暂无传感器数据"
      message="还没有传感器数据，请检查设备连接状态"
    />
  );

  if (loading && !selectedSensor) {
    return <LoadingSpinner visible={true} text="加载传感器列表..." />;
  }

  if (error) {
    return (
      <ErrorMessage
        visible={true}
        title="加载失败"
        message={error}
        onRetry={() => {
          fetchSensors();
          fetchSensorData();
        }}
        fullScreen={true}
      />
    );
  }

  if (sensors.length === 0) {
    return renderEmptyState();
  }

  const currentData = sensorData[selectedSensor?._id] || [];
  const dataTypes = currentData.length > 0 
    ? Object.keys(currentData[0].data || {})
    : [];

  return (
    <View style={styles.container}>
      {/* 传感器选择器 */}
      {renderSensorSelector()}

      {/* 时间范围选择 */}
      <Surface style={styles.timeRangeSelector}>
        <SegmentedButtons
          value={timeRange}
          onValueChange={setTimeRange}
          buttons={timeRangeOptions}
          style={styles.segmentedButtons}
        />
      </Surface>

      {/* 数据列表 */}
      <FlatList
        data={dataTypes}
        keyExtractor={(item) => item}
        renderItem={({ item }) => renderDataCard(item)}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
          />
        }
        ListEmptyComponent={
          <EmptyState
            icon="sensors"
            title="暂无数据"
            message="该传感器暂无数据记录"
          />
        }
        showsVerticalScrollIndicator={false}
      />

      {loading && (
        <LoadingSpinner
          visible={true}
          text="加载数据中..."
          overlay={true}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  
  sensorSelector: {
    margin: theme.custom.spacing.md,
    padding: theme.custom.spacing.md,
    elevation: 2,
  },
  
  selectorTitle: {
    fontSize: theme.custom.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.custom.spacing.sm,
  },
  
  sensorList: {
    paddingVertical: theme.custom.spacing.xs,
  },
  
  sensorChip: {
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.custom.colors.border,
    borderRadius: theme.custom.borderRadius.md,
    paddingHorizontal: theme.custom.spacing.md,
    paddingVertical: theme.custom.spacing.sm,
    marginRight: theme.custom.spacing.sm,
  },
  
  selectedSensorChip: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  
  sensorChipText: {
    fontSize: theme.custom.fontSizes.sm,
    color: theme.colors.text,
  },
  
  selectedSensorChipText: {
    color: theme.colors.surface,
  },
  
  timeRangeSelector: {
    margin: theme.custom.spacing.md,
    marginTop: 0,
    padding: theme.custom.spacing.md,
    elevation: 2,
  },
  
  segmentedButtons: {
    backgroundColor: theme.colors.surface,
  },
  
  listContainer: {
    flexGrow: 1,
    paddingHorizontal: theme.custom.spacing.sm,
    paddingBottom: theme.custom.spacing.xl,
  },
  
  dataCard: {
    marginVertical: theme.custom.spacing.xs,
    marginHorizontal: theme.custom.spacing.xs,
    elevation: 2,
  },
  
  cardHeader: {
    padding: theme.custom.spacing.md,
  },
  
  dataInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  dataText: {
    marginLeft: theme.custom.spacing.md,
  },
  
  dataLabel: {
    fontSize: theme.custom.fontSizes.md,
    color: theme.custom.colors.textSecondary,
    marginBottom: theme.custom.spacing.xs,
  },
  
  dataValue: {
    fontSize: theme.custom.fontSizes.xl,
    fontWeight: 'bold',
  },
  
  chartContainer: {
    alignItems: 'center',
    paddingBottom: theme.custom.spacing.md,
  },
  
  chart: {
    borderRadius: 0,
  },
});

export default SensorsScreen;
