import PushNotification from 'react-native-push-notification';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

class NotificationService {
  constructor() {
    this.configure();
    this.lastId = 0;
  }

  configure() {
    PushNotification.configure({
      // iOS权限请求
      onRegister: function (token) {
        console.log('TOKEN:', token);
        // 将token发送到服务器
        this.sendTokenToServer(token.token);
      }.bind(this),

      // 接收到通知时的回调
      onNotification: function (notification) {
        console.log('NOTIFICATION:', notification);

        // 处理通知点击
        if (notification.userInteraction) {
          this.handleNotificationOpen(notification);
        }

        // iOS需要调用finish
        if (Platform.OS === 'ios') {
          notification.finish(PushNotificationIOS.FetchResult.NoData);
        }
      }.bind(this),

      // Android权限
      permissions: {
        alert: true,
        badge: true,
        sound: true,
      },

      // 弹出初始通知权限请求
      popInitialNotification: true,

      // 请求权限
      requestPermissions: Platform.OS === 'ios',
    });

    // 创建默认通道 (Android)
    if (Platform.OS === 'android') {
      PushNotification.createChannel(
        {
          channelId: 'farm-default',
          channelName: '农场管理',
          channelDescription: '农场管理系统通知',
          playSound: true,
          soundName: 'default',
          importance: 4,
          vibrate: true,
        },
        (created) => console.log(`createChannel returned '${created}'`)
      );

      // 创建高优先级通道
      PushNotification.createChannel(
        {
          channelId: 'farm-urgent',
          channelName: '紧急通知',
          channelDescription: '农场紧急情况通知',
          playSound: true,
          soundName: 'default',
          importance: 5,
          vibrate: true,
        },
        (created) => console.log(`createChannel urgent returned '${created}'`)
      );
    }
  }

  // 发送token到服务器
  async sendTokenToServer(token) {
    try {
      await AsyncStorage.setItem('fcm_token', token);
      // TODO: 发送到后端服务器
      console.log('Token saved:', token);
    } catch (error) {
      console.error('Error saving token:', error);
    }
  }

  // 获取存储的token
  async getToken() {
    try {
      return await AsyncStorage.getItem('fcm_token');
    } catch (error) {
      console.error('Error getting token:', error);
      return null;
    }
  }

  // 显示本地通知
  showLocalNotification(options) {
    this.lastId++;
    
    const notification = {
      id: this.lastId,
      title: options.title || '农场管理系统',
      message: options.message || '',
      playSound: options.playSound !== false,
      soundName: options.soundName || 'default',
      number: options.badge || 0,
      userInfo: options.data || {},
      actions: options.actions || [],
      ...Platform.select({
        android: {
          channelId: options.urgent ? 'farm-urgent' : 'farm-default',
          largeIcon: options.largeIcon || 'ic_launcher',
          smallIcon: options.smallIcon || 'ic_notification',
          bigText: options.bigText || options.message,
          subText: options.subText || '',
          color: options.color || '#4CAF50',
          vibrate: options.vibrate !== false,
          vibration: options.vibration || 300,
          priority: options.urgent ? 'max' : 'high',
          importance: options.urgent ? 'max' : 'high',
        },
        ios: {
          alertAction: options.alertAction || 'view',
          category: options.category || '',
          userInfo: options.data || {},
        },
      }),
    };

    PushNotification.localNotification(notification);
    return this.lastId;
  }

  // 显示设备状态通知
  showDeviceNotification(device, status, message) {
    const isUrgent = status === 'offline' || status === 'error';
    
    return this.showLocalNotification({
      title: `设备${status === 'offline' ? '离线' : status === 'error' ? '异常' : '状态更新'}`,
      message: `${device.name}: ${message}`,
      urgent: isUrgent,
      data: {
        type: 'device_status',
        deviceId: device._id,
        status: status,
      },
      actions: ['查看详情'],
    });
  }

  // 显示传感器预警通知
  showSensorAlert(sensor, dataType, value, threshold) {
    return this.showLocalNotification({
      title: '传感器预警',
      message: `${sensor.name}的${dataType}值${value}超过阈值${threshold}`,
      urgent: true,
      data: {
        type: 'sensor_alert',
        sensorId: sensor._id,
        dataType: dataType,
        value: value,
        threshold: threshold,
      },
      actions: ['查看详情', '忽略'],
    });
  }

  // 显示任务提醒通知
  showTaskReminder(task) {
    return this.showLocalNotification({
      title: '任务提醒',
      message: `任务"${task.title}"即将到期`,
      data: {
        type: 'task_reminder',
        taskId: task._id,
      },
      actions: ['查看任务', '标记完成'],
    });
  }

  // 显示天气预警通知
  showWeatherAlert(weather) {
    return this.showLocalNotification({
      title: '天气预警',
      message: `${weather.type}: ${weather.description}`,
      urgent: weather.level === 'severe',
      data: {
        type: 'weather_alert',
        weatherId: weather._id,
        level: weather.level,
      },
      actions: ['查看详情'],
    });
  }

  // 显示收获提醒通知
  showHarvestReminder(crop) {
    return this.showLocalNotification({
      title: '收获提醒',
      message: `作物"${crop.name}"已到收获期`,
      data: {
        type: 'harvest_reminder',
        cropId: crop._id,
      },
      actions: ['查看详情', '记录收获'],
    });
  }

  // 显示灌溉提醒通知
  showIrrigationReminder(field, moistureLevel) {
    return this.showLocalNotification({
      title: '灌溉提醒',
      message: `地块"${field.name}"土壤湿度${moistureLevel}%，需要灌溉`,
      data: {
        type: 'irrigation_reminder',
        fieldId: field._id,
        moistureLevel: moistureLevel,
      },
      actions: ['开始灌溉', '稍后提醒'],
    });
  }

  // 取消通知
  cancelNotification(id) {
    PushNotification.cancelLocalNotifications({ id: id.toString() });
  }

  // 取消所有通知
  cancelAllNotifications() {
    PushNotification.cancelAllLocalNotifications();
  }

  // 获取已发送的通知
  getDeliveredNotifications(callback) {
    PushNotification.getDeliveredNotifications(callback);
  }

  // 设置应用图标徽章数字
  setApplicationIconBadgeNumber(number) {
    PushNotification.setApplicationIconBadgeNumber(number);
  }

  // 获取应用图标徽章数字
  getApplicationIconBadgeNumber(callback) {
    PushNotification.getApplicationIconBadgeNumber(callback);
  }

  // 处理通知点击
  handleNotificationOpen(notification) {
    const { userInfo } = notification;
    
    if (!userInfo || !userInfo.type) {
      return;
    }

    // 根据通知类型进行路由跳转
    switch (userInfo.type) {
      case 'device_status':
        this.navigateToDevice(userInfo.deviceId);
        break;
      case 'sensor_alert':
        this.navigateToSensor(userInfo.sensorId);
        break;
      case 'task_reminder':
        this.navigateToTask(userInfo.taskId);
        break;
      case 'weather_alert':
        this.navigateToWeather();
        break;
      case 'harvest_reminder':
        this.navigateToCrop(userInfo.cropId);
        break;
      case 'irrigation_reminder':
        this.navigateToField(userInfo.fieldId);
        break;
      default:
        console.log('Unknown notification type:', userInfo.type);
    }
  }

  // 导航方法 (需要在应用中设置导航引用)
  setNavigationRef(navigationRef) {
    this.navigationRef = navigationRef;
  }

  navigateToDevice(deviceId) {
    if (this.navigationRef) {
      this.navigationRef.navigate('DeviceDetail', { deviceId });
    }
  }

  navigateToSensor(sensorId) {
    if (this.navigationRef) {
      this.navigationRef.navigate('SensorDetail', { sensorId });
    }
  }

  navigateToTask(taskId) {
    if (this.navigationRef) {
      this.navigationRef.navigate('TaskDetail', { taskId });
    }
  }

  navigateToWeather() {
    if (this.navigationRef) {
      this.navigationRef.navigate('Weather');
    }
  }

  navigateToCrop(cropId) {
    if (this.navigationRef) {
      this.navigationRef.navigate('CropDetail', { cropId });
    }
  }

  navigateToField(fieldId) {
    if (this.navigationRef) {
      this.navigationRef.navigate('FieldDetail', { fieldId });
    }
  }

  // 检查通知权限
  async checkPermissions() {
    return new Promise((resolve) => {
      PushNotification.checkPermissions((permissions) => {
        resolve(permissions);
      });
    });
  }

  // 请求通知权限
  async requestPermissions() {
    return new Promise((resolve) => {
      PushNotification.requestPermissions((permissions) => {
        resolve(permissions);
      });
    });
  }

  // 清理资源
  cleanup() {
    this.cancelAllNotifications();
    this.navigationRef = null;
  }
}

export default new NotificationService();
