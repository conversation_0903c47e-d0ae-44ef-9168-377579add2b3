import { DefaultTheme } from 'react-native-paper';

// 颜色定义
const colors = {
  // 主色调
  primary: '#4CAF50',
  primaryDark: '#388E3C',
  primaryLight: '#81C784',
  
  // 辅助色
  secondary: '#2196F3',
  secondaryDark: '#1976D2',
  secondaryLight: '#64B5F6',
  
  // 状态色
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',
  
  // 中性色
  background: '#F5F5F5',
  surface: '#FFFFFF',
  text: '#212121',
  textSecondary: '#757575',
  textDisabled: '#BDBDBD',
  
  // 边框和分割线
  border: '#E0E0E0',
  divider: '#EEEEEE',
  
  // 透明度变化
  overlay: 'rgba(0, 0, 0, 0.5)',
  shadow: 'rgba(0, 0, 0, 0.1)',
  
  // 农业相关色彩
  soil: '#8D6E63',
  water: '#03A9F4',
  plant: '#4CAF50',
  sun: '#FFC107',
  
  // 设备状态色
  online: '#4CAF50',
  offline: '#F44336',
  maintenance: '#FF9800',
  
  // 图表色彩
  chart: {
    primary: '#4CAF50',
    secondary: '#2196F3',
    tertiary: '#FF9800',
    quaternary: '#9C27B0',
    quinary: '#F44336',
  }
};

// 字体大小
const fontSizes = {
  xs: 10,
  sm: 12,
  md: 14,
  lg: 16,
  xl: 18,
  xxl: 20,
  xxxl: 24,
  title: 28,
  header: 32,
};

// 间距
const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

// 圆角
const borderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  round: 50,
};

// 阴影
const shadows = {
  sm: {
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
};

// React Native Paper主题
export const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: colors.primary,
    accent: colors.secondary,
    background: colors.background,
    surface: colors.surface,
    text: colors.text,
    error: colors.error,
    onSurface: colors.text,
    disabled: colors.textDisabled,
    placeholder: colors.textSecondary,
    backdrop: colors.overlay,
  },
  // 自定义属性
  custom: {
    colors,
    fontSizes,
    spacing,
    borderRadius,
    shadows,
  },
};

// 通用样式
export const commonStyles = {
  // 容器样式
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  
  // 卡片样式
  card: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.md,
    padding: spacing.md,
    margin: spacing.sm,
    ...shadows.sm,
  },
  
  // 按钮样式
  button: {
    borderRadius: borderRadius.md,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
  },
  
  // 输入框样式
  input: {
    borderRadius: borderRadius.sm,
    borderWidth: 1,
    borderColor: colors.border,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: fontSizes.md,
    color: colors.text,
  },
  
  // 文本样式
  text: {
    title: {
      fontSize: fontSizes.title,
      fontWeight: 'bold',
      color: colors.text,
    },
    header: {
      fontSize: fontSizes.xl,
      fontWeight: '600',
      color: colors.text,
    },
    body: {
      fontSize: fontSizes.md,
      color: colors.text,
    },
    caption: {
      fontSize: fontSizes.sm,
      color: colors.textSecondary,
    },
    error: {
      fontSize: fontSizes.sm,
      color: colors.error,
    },
  },
  
  // 布局样式
  layout: {
    row: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    column: {
      flexDirection: 'column',
    },
    center: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    spaceBetween: {
      justifyContent: 'space-between',
    },
    spaceAround: {
      justifyContent: 'space-around',
    },
  },
  
  // 状态样式
  status: {
    online: {
      backgroundColor: colors.online,
      color: colors.surface,
    },
    offline: {
      backgroundColor: colors.offline,
      color: colors.surface,
    },
    maintenance: {
      backgroundColor: colors.maintenance,
      color: colors.surface,
    },
  },
};

// 动画配置
export const animations = {
  timing: {
    fast: 200,
    normal: 300,
    slow: 500,
  },
  easing: {
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
  },
};

// 响应式断点
export const breakpoints = {
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
};

export default theme;
