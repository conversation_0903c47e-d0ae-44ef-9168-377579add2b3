import React from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';
import { ActivityIndicator, Text } from 'react-native-paper';
import { theme } from '../../styles/theme';

const LoadingSpinner = ({ 
  visible = true, 
  text = '加载中...', 
  size = 'large',
  color = theme.colors.primary,
  overlay = true,
  style,
  textStyle 
}) => {
  const spinValue = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    if (visible) {
      startSpinAnimation();
    }
  }, [visible]);

  const startSpinAnimation = () => {
    spinValue.setValue(0);
    Animated.loop(
      Animated.timing(spinValue, {
        toValue: 1,
        duration: 2000,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    ).start();
  };

  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  if (!visible) {
    return null;
  }

  const containerStyle = overlay ? styles.overlay : styles.inline;

  return (
    <View style={[containerStyle, style]}>
      <View style={styles.container}>
        {/* 自定义旋转动画 */}
        <Animated.View style={[styles.spinner, { transform: [{ rotate: spin }] }]}>
          <View style={[styles.spinnerInner, { borderTopColor: color }]} />
        </Animated.View>
        
        {/* 系统ActivityIndicator作为备选 */}
        <ActivityIndicator 
          size={size} 
          color={color} 
          style={styles.activityIndicator}
        />
        
        {text && (
          <Text style={[styles.text, textStyle]}>
            {text}
          </Text>
        )}
      </View>
    </View>
  );
};

// 全屏加载组件
export const FullScreenLoading = ({ visible, text = '加载中...' }) => {
  return (
    <LoadingSpinner
      visible={visible}
      text={text}
      overlay={true}
      style={styles.fullScreen}
    />
  );
};

// 按钮加载组件
export const ButtonLoading = ({ loading, children, ...props }) => {
  return (
    <View style={styles.buttonContainer}>
      {loading && (
        <ActivityIndicator 
          size="small" 
          color={theme.colors.surface} 
          style={styles.buttonSpinner}
        />
      )}
      {children}
    </View>
  );
};

// 列表加载组件
export const ListLoading = ({ visible }) => {
  return (
    <LoadingSpinner
      visible={visible}
      text="加载更多..."
      overlay={false}
      size="small"
      style={styles.listLoading}
    />
  );
};

// 刷新加载组件
export const RefreshLoading = ({ visible }) => {
  return (
    <LoadingSpinner
      visible={visible}
      text="刷新中..."
      overlay={false}
      size="small"
      style={styles.refreshLoading}
    />
  );
};

// 骨架屏组件
export const SkeletonLoader = ({ width = '100%', height = 20, style }) => {
  const animatedValue = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: false,
        }),
      ])
    ).start();
  }, []);

  const opacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  return (
    <Animated.View
      style={[
        styles.skeleton,
        {
          width,
          height,
          opacity,
        },
        style,
      ]}
    />
  );
};

// 卡片骨架屏
export const CardSkeleton = () => {
  return (
    <View style={styles.cardSkeleton}>
      <SkeletonLoader width="60%" height={16} style={styles.skeletonTitle} />
      <SkeletonLoader width="100%" height={12} style={styles.skeletonLine} />
      <SkeletonLoader width="80%" height={12} style={styles.skeletonLine} />
      <View style={styles.skeletonFooter}>
        <SkeletonLoader width="30%" height={10} />
        <SkeletonLoader width="20%" height={10} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
  },
  
  inline: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.custom.spacing.md,
  },
  
  fullScreen: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.custom.borderRadius.md,
    padding: theme.custom.spacing.lg,
    minWidth: 120,
    ...theme.custom.shadows.md,
  },
  
  spinner: {
    width: 40,
    height: 40,
    marginBottom: theme.custom.spacing.sm,
  },
  
  spinnerInner: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 3,
    borderColor: 'transparent',
    borderTopColor: theme.colors.primary,
  },
  
  activityIndicator: {
    marginBottom: theme.custom.spacing.sm,
  },
  
  text: {
    fontSize: theme.custom.fontSizes.md,
    color: theme.colors.text,
    textAlign: 'center',
  },
  
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  buttonSpinner: {
    marginRight: theme.custom.spacing.sm,
  },
  
  listLoading: {
    paddingVertical: theme.custom.spacing.md,
  },
  
  refreshLoading: {
    paddingVertical: theme.custom.spacing.sm,
  },
  
  skeleton: {
    backgroundColor: '#E0E0E0',
    borderRadius: theme.custom.borderRadius.sm,
  },
  
  cardSkeleton: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.custom.borderRadius.md,
    padding: theme.custom.spacing.md,
    margin: theme.custom.spacing.sm,
    ...theme.custom.shadows.sm,
  },
  
  skeletonTitle: {
    marginBottom: theme.custom.spacing.sm,
  },
  
  skeletonLine: {
    marginBottom: theme.custom.spacing.xs,
  },
  
  skeletonFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: theme.custom.spacing.sm,
  },
});

export default LoadingSpinner;
