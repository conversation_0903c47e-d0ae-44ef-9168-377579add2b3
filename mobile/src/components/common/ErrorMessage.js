import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text, Button, IconButton } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { theme } from '../../styles/theme';

const ErrorMessage = ({
  visible = true,
  title = '出错了',
  message = '请稍后重试',
  type = 'error', // error, warning, info, network
  showRetry = true,
  retryText = '重试',
  onRetry,
  onClose,
  style,
  fullScreen = false,
}) => {
  if (!visible) {
    return null;
  }

  const getErrorConfig = () => {
    switch (type) {
      case 'network':
        return {
          icon: 'wifi-off',
          color: theme.custom.colors.warning,
          title: '网络连接失败',
          message: '请检查网络连接后重试',
        };
      case 'warning':
        return {
          icon: 'warning',
          color: theme.custom.colors.warning,
          title: '警告',
        };
      case 'info':
        return {
          icon: 'info',
          color: theme.custom.colors.info,
          title: '提示',
        };
      default:
        return {
          icon: 'error',
          color: theme.custom.colors.error,
          title: '错误',
        };
    }
  };

  const config = getErrorConfig();
  const containerStyle = fullScreen ? styles.fullScreen : styles.container;

  return (
    <View style={[containerStyle, style]}>
      <View style={styles.content}>
        {/* 关闭按钮 */}
        {onClose && (
          <IconButton
            icon="close"
            size={20}
            onPress={onClose}
            style={styles.closeButton}
          />
        )}

        {/* 错误图标 */}
        <View style={[styles.iconContainer, { backgroundColor: config.color + '20' }]}>
          <Icon
            name={config.icon}
            size={48}
            color={config.color}
          />
        </View>

        {/* 错误标题 */}
        <Text style={[styles.title, { color: config.color }]}>
          {title || config.title}
        </Text>

        {/* 错误消息 */}
        <Text style={styles.message}>
          {message || config.message}
        </Text>

        {/* 重试按钮 */}
        {showRetry && onRetry && (
          <Button
            mode="contained"
            onPress={onRetry}
            style={[styles.retryButton, { backgroundColor: config.color }]}
            labelStyle={styles.retryButtonText}
          >
            {retryText}
          </Button>
        )}
      </View>
    </View>
  );
};

// 网络错误组件
export const NetworkError = ({ visible, onRetry, onClose }) => {
  return (
    <ErrorMessage
      visible={visible}
      type="network"
      onRetry={onRetry}
      onClose={onClose}
      fullScreen={true}
    />
  );
};

// 空数据组件
export const EmptyState = ({
  visible = true,
  icon = 'inbox',
  title = '暂无数据',
  message = '当前没有可显示的内容',
  actionText,
  onAction,
  style,
}) => {
  if (!visible) {
    return null;
  }

  return (
    <View style={[styles.emptyContainer, style]}>
      <View style={styles.emptyIconContainer}>
        <Icon
          name={icon}
          size={64}
          color={theme.custom.colors.textSecondary}
        />
      </View>
      
      <Text style={styles.emptyTitle}>{title}</Text>
      <Text style={styles.emptyMessage}>{message}</Text>
      
      {actionText && onAction && (
        <Button
          mode="outlined"
          onPress={onAction}
          style={styles.emptyAction}
        >
          {actionText}
        </Button>
      )}
    </View>
  );
};

// 内联错误组件
export const InlineError = ({ visible, message, style }) => {
  if (!visible || !message) {
    return null;
  }

  return (
    <View style={[styles.inlineError, style]}>
      <Icon
        name="error"
        size={16}
        color={theme.custom.colors.error}
        style={styles.inlineErrorIcon}
      />
      <Text style={styles.inlineErrorText}>{message}</Text>
    </View>
  );
};

// 成功消息组件
export const SuccessMessage = ({ visible, message, onClose, style }) => {
  if (!visible) {
    return null;
  }

  return (
    <View style={[styles.successContainer, style]}>
      <Icon
        name="check-circle"
        size={20}
        color={theme.custom.colors.success}
        style={styles.successIcon}
      />
      <Text style={styles.successText}>{message}</Text>
      {onClose && (
        <TouchableOpacity onPress={onClose} style={styles.successClose}>
          <Icon
            name="close"
            size={16}
            color={theme.custom.colors.success}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

// 警告消息组件
export const WarningMessage = ({ visible, message, onClose, style }) => {
  if (!visible) {
    return null;
  }

  return (
    <View style={[styles.warningContainer, style]}>
      <Icon
        name="warning"
        size={20}
        color={theme.custom.colors.warning}
        style={styles.warningIcon}
      />
      <Text style={styles.warningText}>{message}</Text>
      {onClose && (
        <TouchableOpacity onPress={onClose} style={styles.warningClose}>
          <Icon
            name="close"
            size={16}
            color={theme.custom.colors.warning}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.custom.borderRadius.md,
    margin: theme.custom.spacing.md,
    ...theme.custom.shadows.md,
  },
  
  fullScreen: {
    flex: 1,
    backgroundColor: theme.colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.custom.spacing.lg,
  },
  
  content: {
    alignItems: 'center',
    padding: theme.custom.spacing.lg,
  },
  
  closeButton: {
    position: 'absolute',
    top: 0,
    right: 0,
    zIndex: 1,
  },
  
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.custom.spacing.md,
  },
  
  title: {
    fontSize: theme.custom.fontSizes.xl,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: theme.custom.spacing.sm,
  },
  
  message: {
    fontSize: theme.custom.fontSizes.md,
    color: theme.custom.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: theme.custom.spacing.lg,
  },
  
  retryButton: {
    paddingHorizontal: theme.custom.spacing.lg,
  },
  
  retryButtonText: {
    color: theme.colors.surface,
    fontSize: theme.custom.fontSizes.md,
  },
  
  // 空状态样式
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.custom.spacing.lg,
  },
  
  emptyIconContainer: {
    marginBottom: theme.custom.spacing.md,
  },
  
  emptyTitle: {
    fontSize: theme.custom.fontSizes.lg,
    fontWeight: '600',
    color: theme.custom.colors.text,
    textAlign: 'center',
    marginBottom: theme.custom.spacing.sm,
  },
  
  emptyMessage: {
    fontSize: theme.custom.fontSizes.md,
    color: theme.custom.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: theme.custom.spacing.lg,
  },
  
  emptyAction: {
    paddingHorizontal: theme.custom.spacing.lg,
  },
  
  // 内联错误样式
  inlineError: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.custom.colors.error + '10',
    borderColor: theme.custom.colors.error + '30',
    borderWidth: 1,
    borderRadius: theme.custom.borderRadius.sm,
    padding: theme.custom.spacing.sm,
    marginTop: theme.custom.spacing.xs,
  },
  
  inlineErrorIcon: {
    marginRight: theme.custom.spacing.xs,
  },
  
  inlineErrorText: {
    flex: 1,
    fontSize: theme.custom.fontSizes.sm,
    color: theme.custom.colors.error,
  },
  
  // 成功消息样式
  successContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.custom.colors.success + '10',
    borderColor: theme.custom.colors.success + '30',
    borderWidth: 1,
    borderRadius: theme.custom.borderRadius.sm,
    padding: theme.custom.spacing.md,
    margin: theme.custom.spacing.sm,
  },
  
  successIcon: {
    marginRight: theme.custom.spacing.sm,
  },
  
  successText: {
    flex: 1,
    fontSize: theme.custom.fontSizes.md,
    color: theme.custom.colors.success,
  },
  
  successClose: {
    padding: theme.custom.spacing.xs,
  },
  
  // 警告消息样式
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.custom.colors.warning + '10',
    borderColor: theme.custom.colors.warning + '30',
    borderWidth: 1,
    borderRadius: theme.custom.borderRadius.sm,
    padding: theme.custom.spacing.md,
    margin: theme.custom.spacing.sm,
  },
  
  warningIcon: {
    marginRight: theme.custom.spacing.sm,
  },
  
  warningText: {
    flex: 1,
    fontSize: theme.custom.fontSizes.md,
    color: theme.custom.colors.warning,
  },
  
  warningClose: {
    padding: theme.custom.spacing.xs,
  },
});

export default ErrorMessage;
