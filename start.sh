#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================"
echo -e "农场智慧管理系统启动脚本"
echo -e "========================================${NC}"
echo

echo -e "${YELLOW}正在检查环境...${NC}"

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ 未检测到Node.js，请先安装Node.js 16.0.0或更高版本${NC}"
    echo -e "${YELLOW}下载地址: https://nodejs.org/${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Node.js 已安装${NC}"

# 检查MongoDB
if ! command -v mongod &> /dev/null; then
    echo -e "${YELLOW}⚠️  未检测到MongoDB，请确保MongoDB已安装并启动${NC}"
    echo -e "${YELLOW}下载地址: https://www.mongodb.com/try/download/community${NC}"
fi

echo
echo -e "${BLUE}选择启动模式:${NC}"
echo "1. 完整启动 (后端 + 前端 + 大屏)"
echo "2. 仅启动后端"
echo "3. 仅启动前端"
echo "4. 仅启动大屏"
echo "5. 安装依赖"
echo "6. 初始化数据库"
echo "7. 退出"
echo

read -p "请输入选择 (1-7): " choice

case $choice in
    1)
        start_all
        ;;
    2)
        start_backend
        ;;
    3)
        start_frontend
        ;;
    4)
        start_dashboard
        ;;
    5)
        install_deps
        ;;
    6)
        init_db
        ;;
    7)
        exit 0
        ;;
    *)
        echo -e "${RED}无效选择，请重新运行脚本${NC}"
        exit 1
        ;;
esac

install_deps() {
    echo
    echo -e "${YELLOW}正在安装依赖...${NC}"
    echo

    echo -e "${YELLOW}安装后端依赖...${NC}"
    cd backend
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 后端依赖安装失败${NC}"
        exit 1
    fi
    cd ..

    echo -e "${YELLOW}安装前端依赖...${NC}"
    cd frontend
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 前端依赖安装失败${NC}"
        exit 1
    fi
    cd ..

    echo -e "${YELLOW}安装大屏依赖...${NC}"
    cd dashboard
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 大屏依赖安装失败${NC}"
        exit 1
    fi
    cd ..

    echo
    echo -e "${GREEN}✅ 所有依赖安装完成！${NC}"

    # 重新显示菜单
    exec "$0"
}

init_db() {
    echo
    echo -e "${YELLOW}正在初始化数据库...${NC}"
    echo

    cd backend
    npm run init-db
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 数据库初始化失败${NC}"
        exit 1
    fi
    cd ..

    echo
    echo -e "${GREEN}✅ 数据库初始化完成！${NC}"
    echo
    echo -e "${BLUE}📋 默认账号信息:${NC}"
    echo -e "${YELLOW}管理员: admin / 123456${NC}"
    echo -e "${YELLOW}经理: manager / 123456${NC}"
    echo -e "${YELLOW}工人: worker / 123456${NC}"
    echo

    # 重新显示菜单
    exec "$0"
}

start_all() {
    echo
    echo -e "${YELLOW}正在启动完整系统...${NC}"
    echo

    # 复制环境配置文件
    if [ ! -f "backend/.env" ]; then
        cp "backend/.env.example" "backend/.env"
        echo -e "${GREEN}✅ 已创建后端环境配置文件，请根据需要修改 backend/.env${NC}"
    fi

    # 启动后端
    echo -e "${YELLOW}启动后端服务...${NC}"
    cd backend
    npm run dev &
    BACKEND_PID=$!
    cd ..

    # 等待后端启动
    sleep 3

    # 启动前端
    echo -e "${YELLOW}启动前端服务...${NC}"
    cd frontend
    npm run dev &
    FRONTEND_PID=$!
    cd ..

    # 启动大屏
    echo -e "${YELLOW}启动数据大屏...${NC}"
    cd dashboard
    npm run dev &
    DASHBOARD_PID=$!
    cd ..

    echo
    echo -e "${GREEN}✅ 系统启动完成！${NC}"
    echo
    echo -e "${BLUE}📱 前端地址: http://localhost:3000${NC}"
    echo -e "${BLUE}🖥️  大屏地址: http://localhost:3001${NC}"
    echo -e "${BLUE}🔧 后端API: http://localhost:5000${NC}"
    echo
    echo -e "${YELLOW}默认管理员账号:${NC}"
    echo -e "${YELLOW}用户名: admin${NC}"
    echo -e "${YELLOW}密码: 123456${NC}"
    echo
    echo -e "${YELLOW}按 Ctrl+C 停止所有服务${NC}"

    # 等待用户中断
    trap 'kill $BACKEND_PID $FRONTEND_PID $DASHBOARD_PID 2>/dev/null; echo -e "\n${GREEN}所有服务已停止${NC}"; exit 0' INT
    wait
}

start_backend() {
    echo
    echo -e "${YELLOW}正在启动后端服务...${NC}"

    if [ ! -f "backend/.env" ]; then
        cp "backend/.env.example" "backend/.env"
        echo -e "${GREEN}✅ 已创建后端环境配置文件${NC}"
    fi

    cd backend
    npm run dev
    cd ..
}

start_frontend() {
    echo
    echo -e "${YELLOW}正在启动前端服务...${NC}"
    cd frontend
    npm run dev
    cd ..
}

start_dashboard() {
    echo
    echo -e "${YELLOW}正在启动数据大屏...${NC}"
    cd dashboard
    npm run dev
    cd ..
}

echo
echo -e "${GREEN}感谢使用农场智慧管理系统！${NC}"
