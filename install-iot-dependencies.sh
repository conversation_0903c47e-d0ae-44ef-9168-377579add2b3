#!/bin/bash

echo "========================================"
echo "安装物联网集成依赖包"
echo "========================================"
echo

echo "正在安装后端IoT依赖..."
cd backend
npm install modbus-serial mqtt axios
echo "后端IoT依赖安装完成"
cd ..

echo
echo "========================================"
echo "所有IoT依赖安装完成！"
echo "========================================"
echo
echo "支持的协议:"
echo "- Modbus TCP/RTU"
echo "- MQTT"
echo "- HTTP API"
echo "- TCP Socket"
echo "- PLC (西门子/三菱)"
echo "========================================"
echo
